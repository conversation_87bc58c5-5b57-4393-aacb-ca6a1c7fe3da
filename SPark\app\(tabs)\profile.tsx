import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { router } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  UserIcon,
  CarIcon,
  PaymentIcon,
  NotificationIcon,
  SettingsIcon,
  InfoIcon,
  ForwardIcon,
  EditIcon,
  PlusIcon,
  WalletIcon,
  StarIcon
} from '../../src/components/ui/Icon';
import { colors, typography, spacing, borderRadius } from '../../src/theme';

export default function ProfileScreen() {
  const insets = useSafeAreaInsets();

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: () => {
            router.replace('/auth/login');
          },
        },
      ]
    );
  };

  const handleMenuPress = (action?: () => void, title?: string) => {
    if (action) {
      action();
    } else {
      Alert.alert('Coming Soon', `${title} will be available soon.`);
    }
  };

  const renderProfileHeader = () => (
    <View style={styles.profileHeader}>
      <View style={styles.avatarContainer}>
        <View style={styles.avatar}>
          <Text style={styles.avatarText}>JS</Text>
        </View>
        <TouchableOpacity style={styles.editButton}>
          <EditIcon size={16} color={colors.white} />
        </TouchableOpacity>
      </View>

      <Text style={styles.userName}>John Smith</Text>
      <Text style={styles.userEmail}><EMAIL></Text>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>12</Text>
          <Text style={styles.statLabel}>Bookings</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>4.8</Text>
          <Text style={styles.statLabel}>Rating</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>2</Text>
          <Text style={styles.statLabel}>Vehicles</Text>
        </View>
      </View>
    </View>
  );

  const menuItems = [
    {
      title: 'My Bookings',
      icon: <CarIcon size={24} color={colors.primary[500]} />,
      action: () => router.push('/bookings')
    },
    {
      title: 'My Vehicles',
      icon: <CarIcon size={24} color={colors.primary[500]} />,
      action: () => router.push('/profile/vehicles')
    },
    {
      title: 'Payment Methods',
      icon: <PaymentIcon size={24} color={colors.primary[500]} />
    },
    {
      title: 'Host Dashboard',
      icon: <StarIcon size={24} color={colors.primary[500]} />,
      action: () => router.push('/host/dashboard')
    },
    {
      title: 'Notifications',
      icon: <NotificationIcon size={24} color={colors.primary[500]} />
    },
    {
      title: 'Help & Support',
      icon: <InfoIcon size={24} color={colors.primary[500]} />
    },
    {
      title: 'Settings',
      icon: <SettingsIcon size={24} color={colors.primary[500]} />
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary[500]} />

      {renderProfileHeader()}

      <ScrollView style={styles.menuContainer} showsVerticalScrollIndicator={false}>
        {menuItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.menuItem}
            onPress={() => handleMenuPress(item.action, item.title)}
          >
            <View style={styles.menuItemIcon}>
              {item.icon}
            </View>
            <Text style={styles.menuItemTitle}>{item.title}</Text>
            <ForwardIcon size={20} color={colors.gray[400]} />
          </TouchableOpacity>
        ))}

        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutText}>Sign Out</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  // Profile Header (Gradient background like modern apps)
  profileHeader: {
    backgroundColor: colors.primary[500],
    paddingTop: 60, // Increased to avoid status bar overlap
    paddingBottom: 40,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: colors.white,
  },
  avatarText: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.primary[500],
  },
  editButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.primary[600],
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: colors.white,
  },
  userName: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
    marginBottom: 20,
  },

  // Stats Container
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginTop: 20,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.black[900],
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.gray[600],
    fontWeight: '600',
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: colors.gray[200],
  },

  // Menu Container
  menuContainer: {
    flex: 1,
    backgroundColor: colors.gray[50],
    paddingTop: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginHorizontal: 20,
    marginBottom: 2,
    borderRadius: 12,
  },
  menuItemIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.primary[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  menuItemTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: colors.black[900],
  },

  // Logout Button
  logoutButton: {
    backgroundColor: colors.error[500],
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 40,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
