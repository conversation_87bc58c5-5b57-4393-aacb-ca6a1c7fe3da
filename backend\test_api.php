<?php

/**
 * Simple API Test Script for SPark Backend
 * Run this with: php test_api.php
 */

$baseUrl = 'http://localhost:8000/api';

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);

    $defaultHeaders = ['Content-Type: application/json'];
    $headers = array_merge($defaultHeaders, $headers);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return [
        'status' => $httpCode,
        'body' => json_decode($response, true),
        'raw' => $response
    ];
}

echo "🚀 Testing SPark Backend API\n";
echo "============================\n\n";

// Test 1: Register a new user
echo "1. Testing User Registration...\n";
$registerData = [
    'email' => 'test' . time() . '@example.com', // Use unique email
    'password' => 'password123',
    'password_confirmation' => 'password123',
    'first_name' => 'Test',
    'last_name' => 'User',
    'user_type' => 'driver'
];

$response = makeRequest($baseUrl . '/auth/register', 'POST', $registerData);
echo "Status: " . $response['status'] . "\n";
if ($response['status'] == 201) {
    echo "✅ Registration successful!\n";
    $token = $response['body']['data']['token'] ?? null;
    echo "Token: " . substr($token, 0, 20) . "...\n";
} else {
    echo "❌ Registration failed\n";
    echo "Response: " . $response['raw'] . "\n";
}
echo "\n";

// Test 2: Login
echo "2. Testing User Login...\n";
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'password'
];

$response = makeRequest($baseUrl . '/auth/login', 'POST', $loginData);
echo "Status: " . $response['status'] . "\n";
if ($response['status'] == 200) {
    echo "✅ Login successful!\n";
    $token = $response['body']['data']['token'] ?? null;
    echo "Token: " . substr($token, 0, 20) . "...\n";
} else {
    echo "❌ Login failed\n";
    echo "Response: " . $response['raw'] . "\n";
    $token = null;
}
echo "\n";

// Test 3: Get Profile (if we have a token)
if ($token) {
    echo "3. Testing Get Profile...\n";
    $response = makeRequest($baseUrl . '/auth/profile', 'GET', null, ['Authorization: Bearer ' . $token]);
    echo "Status: " . $response['status'] . "\n";
    if ($response['status'] == 200) {
        echo "✅ Profile retrieved successfully!\n";
        $user = $response['body']['data'] ?? null;
        if ($user) {
            echo "User: " . $user['first_name'] . " " . $user['last_name'] . " (" . $user['email'] . ")\n";
        }
    } else {
        echo "❌ Profile retrieval failed\n";
        echo "Response: " . $response['raw'] . "\n";
    }
    echo "\n";

    // Test 4: Search Parking Spots
    echo "4. Testing Parking Spot Search...\n";
    $response = makeRequest($baseUrl . '/spots/search?latitude=40.7128&longitude=-74.0060&radius=10', 'GET');
    echo "Status: " . $response['status'] . "\n";
    if ($response['status'] == 200) {
        echo "✅ Spot search successful!\n";
        $spots = $response['body']['data'] ?? [];
        echo "Found " . count($spots) . " parking spots\n";
        if (count($spots) > 0) {
            echo "First spot: " . $spots[0]['title'] . "\n";
        }
    } else {
        echo "❌ Spot search failed\n";
        echo "Response: " . $response['raw'] . "\n";
    }
    echo "\n";

    // Test 5: Get User Vehicles
    echo "5. Testing Get User Vehicles...\n";
    $response = makeRequest($baseUrl . '/vehicles', 'GET', null, ['Authorization: Bearer ' . $token]);
    echo "Status: " . $response['status'] . "\n";
    if ($response['status'] == 200) {
        echo "✅ Vehicles retrieved successfully!\n";
        $vehicles = $response['body']['data'] ?? [];
        echo "Found " . count($vehicles) . " vehicles\n";
        if (count($vehicles) > 0) {
            echo "First vehicle: " . $vehicles[0]['make'] . " " . $vehicles[0]['model'] . "\n";
        }
    } else {
        echo "❌ Vehicles retrieval failed\n";
        echo "Response: " . $response['raw'] . "\n";
    }
    echo "\n";
}

echo "🎉 API Testing Complete!\n";
echo "\nIf you see ✅ marks above, your SPark backend is working correctly!\n";
echo "You can now integrate it with your React Native frontend.\n\n";

echo "📱 Frontend Integration:\n";
echo "- Update your frontend API base URL to: http://localhost:8000/api\n";
echo "- Use the test accounts:\n";
echo "  - Driver: <EMAIL> / password\n";
echo "  - Host: <EMAIL> / password\n";
echo "  - Admin: <EMAIL> / password\n";
