import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  StatusBar,
  Dimensions,
  ScrollView,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withTiming,
  interpolate,
  FadeInDown,
  SlideInUp,
  BounceIn,
} from 'react-native-reanimated';
import MapView, { Marker, Circle } from 'react-native-maps';
import * as Location from 'expo-location';
import { router } from 'expo-router';
import {
  BackIcon,
  LocationIcon,
  ScanIcon,
  SearchIcon
} from '../src/components/ui/AnimatedIcon';
import ParkingSearchAnimation from '../src/components/ui/ParkingSearchAnimation';
import { openStreetMapService } from '../src/services/openStreetMapService';
import { ProcessedParkingSpot } from '../src/types/parking';
import ParkingBottomSheet from '../src/components/parking/ParkingBottomSheet';
import { parkingCache } from '../src/utils/storage';
import { colors, typography } from '../src/theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const FindParkingScreen: React.FC = () => {
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [parkingSpots, setParkingSpots] = useState<ProcessedParkingSpot[]>([]);
  const [searchRadius, setSearchRadius] = useState(1000); // 1km radius
  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const [selectedSpot, setSelectedSpot] = useState<ProcessedParkingSpot | null>(null);
  const mapRef = useRef<any>(null);

  // Animation values
  const pulseAnimation = useSharedValue(0);
  const searchAnimation = useSharedValue(0);
  const buttonScale = useSharedValue(1);
  const buttonPulse = useSharedValue(0);

  useEffect(() => {
    getCurrentLocation();

    // Start pulse animation
    pulseAnimation.value = withRepeat(
      withTiming(1, { duration: 2000 }),
      -1,
      true
    );

    // Start button pulse animation
    buttonPulse.value = withRepeat(
      withTiming(1, { duration: 3000 }),
      -1,
      true
    );
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required to find nearby parking.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
      
      setUserLocation(location);
      
      // Animate to user location
      if (mapRef.current && mapRef.current.animateToRegion) {
        mapRef.current.animateToRegion({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }, 1000);
      }
    } catch (error) {
      Alert.alert('Error', 'Unable to get your location. Please try again.');
    }
  };

  const findNearbyParking = async () => {
    if (!userLocation) {
      Alert.alert('Location Required', 'Please enable location services to find parking.');
      return;
    }

    setIsSearching(true);
    buttonScale.value = withSpring(0.95);
    searchAnimation.value = withTiming(1, { duration: 500 });

    try {
      // Save current location for offline use
      await parkingCache.setLastKnownLocation(
        userLocation.coords.latitude,
        userLocation.coords.longitude
      );

      // Use the OpenStreetMap service
      const searchResult = await openStreetMapService.findNearbyParking(userLocation, searchRadius);

      setParkingSpots(searchResult.spots);

      // Show bottom sheet with results
      setShowBottomSheet(true);

      // Animate map to show all spots
      if (mapRef.current && mapRef.current.fitToCoordinates && searchResult.spots.length > 0) {
        const coordinates = [
          { latitude: userLocation.coords.latitude, longitude: userLocation.coords.longitude },
          ...searchResult.spots.map(spot => ({ latitude: spot.latitude, longitude: spot.longitude }))
        ];

        mapRef.current.fitToCoordinates(coordinates, {
          edgePadding: { top: 100, right: 50, bottom: 300, left: 50 },
          animated: true,
        });
      }

      // Cache results for offline use
      if (searchResult.spots.length > 0) {
        await parkingCache.setOfflineParkingData(searchResult);
      } else {
        Alert.alert(
          'No Parking Found',
          'No available parking spots found in your area. Try expanding your search radius.'
        );
      }

    } catch (error) {
      console.error('Error finding parking:', error);

      // Try to load offline data
      const offlineData = await parkingCache.getOfflineParkingData();
      if (offlineData && offlineData.spots.length > 0) {
        setParkingSpots(offlineData.spots);
        setShowBottomSheet(true);
        Alert.alert(
          'Using Cached Data',
          'Unable to fetch fresh data. Showing previously cached parking spots.'
        );
      } else {
        Alert.alert(
          'Connection Error',
          'Unable to find parking spots. Please check your internet connection and try again.'
        );
      }
    } finally {
      setIsSearching(false);
      buttonScale.value = withSpring(1);
      searchAnimation.value = withTiming(0, { duration: 300 });
    }
  };

  const getMarkerColor = (type: string) => {
    switch (type) {
      case 'multi-storey': return colors.primary[500];
      case 'underground': return colors.primary[700];
      case 'surface': return colors.success[500];
      case 'street_side': return colors.secondary[500];
      default: return colors.gray[500];
    }
  };

  // Animated styles
  const pulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: interpolate(pulseAnimation.value, [0, 1], [1, 1.2]) }],
    opacity: interpolate(pulseAnimation.value, [0, 1], [0.7, 0.3]),
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: buttonScale.value }],
  }));

  const buttonPulseStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: interpolate(buttonPulse.value, [0, 1], [1, 1.1]) }
    ],
    opacity: interpolate(buttonPulse.value, [0, 1], [1, 0.8]),
  }));



  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Full Screen Map Container */}
      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          style={styles.map}
          showsUserLocation={true}
          showsMyLocationButton={false}
          initialRegion={{
            latitude: 37.78825,
            longitude: -122.4324,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }}
        >
          {/* Search radius circle */}
          {userLocation && (
            <Circle
              center={{
                latitude: userLocation.coords.latitude,
                longitude: userLocation.coords.longitude,
              }}
              radius={searchRadius}
              strokeColor={colors.primary[500]}
              strokeWidth={2}
              fillColor="rgba(255, 214, 10, 0.1)"
            />
          )}

          {/* Parking spot markers */}
          {parkingSpots.map((spot) => (
            <Marker
              key={spot.id}
              coordinate={{
                latitude: spot.latitude,
                longitude: spot.longitude,
              }}
              title={spot.name}
              description={`${spot.fee ? 'Paid' : 'Free'} • ${Math.round(spot.distance)}m away`}
              pinColor={getMarkerColor(spot.type)}
              onPress={() => setSelectedSpot(spot)}
            />
          ))}
        </MapView>
      </View>

      {/* Back Button - Floating */}
      <Animated.View
        style={styles.backButtonContainer}
        entering={FadeInDown.duration(600)}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <BackIcon size={24} color={colors.white} />
        </TouchableOpacity>
      </Animated.View>

      {/* Location Button - Floating */}
      <Animated.View
        style={styles.locationButtonContainer}
        entering={FadeInDown.duration(600).delay(200)}
      >
        <TouchableOpacity
          style={styles.locationButton}
          onPress={getCurrentLocation}
          activeOpacity={0.7}
        >
          <LocationIcon size={24} color={colors.white} />
        </TouchableOpacity>
      </Animated.View>

      {/* Circular Find Button - Bottom Center */}
      <Animated.View
        style={styles.findButtonContainer}
        entering={SlideInUp.duration(800).delay(1000)}
      >
        <Animated.View style={[buttonAnimatedStyle, !isSearching && buttonPulseStyle]}>
          <TouchableOpacity
            style={[styles.findButton, isSearching && styles.findButtonSearching]}
            onPress={findNearbyParking}
            disabled={isSearching}
            activeOpacity={0.8}
          >
            <ScanIcon
              size={36}
              color={isSearching ? colors.gray[400] : colors.black[900]}
            />
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>

      {/* Enhanced Search Animation */}
      <ParkingSearchAnimation
        visible={isSearching}
        onComplete={() => setIsSearching(false)}
      />

      {/* Parking Results Bottom Sheet */}
      <ParkingBottomSheet
        spots={parkingSpots}
        isVisible={showBottomSheet}
        onClose={() => setShowBottomSheet(false)}
        onSpotSelect={(spot) => setSelectedSpot(spot)}
        loading={isSearching}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black[900],
  },

  // Floating Controls
  backButtonContainer: {
    position: 'absolute',
    top: 60, // Increased for better spacing from status bar
    left: 20,
    zIndex: 1000,
  },
  backButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(0,0,0,0.8)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  locationButtonContainer: {
    position: 'absolute',
    top: 60, // Increased for better spacing from status bar
    right: 20,
    zIndex: 1000,
  },
  locationButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(0,0,0,0.8)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },

  // Map
  mapContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: screenWidth,
    height: screenHeight,
  },
  map: {
    flex: 1,
    width: '100%',
    height: '100%',
  },

  // Circular Find Button - Fixed positioning
  findButtonContainer: {
    position: 'absolute',
    bottom: 50, // Increased from 40 for better accessibility
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 1000,
    paddingHorizontal: 20, // Ensure it's not cut off on small screens
  },
  findButton: {
    width: 80, // Increased from 72 for better touch target
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary[500],
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.5,
    shadowRadius: 24,
    elevation: 20,
    borderWidth: 3,
    borderColor: colors.white,
  },
  findButtonSearching: {
    backgroundColor: colors.gray[400],
    borderColor: colors.gray[200],
    shadowOpacity: 0.2,
  },

  // Search Overlay
  searchOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 2000,
  },
  searchContent: {
    backgroundColor: colors.white,
    borderRadius: 24,
    padding: 40,
    alignItems: 'center',
    marginHorizontal: 40,
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.3,
    shadowRadius: 24,
    elevation: 16,
  },
  searchIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  searchTitle: {
    fontSize: 24,
    fontWeight: '700',
    fontFamily: typography.fontFamily.brand,
    color: colors.black[900],
    marginBottom: 8,
    textAlign: 'center',
  },
  searchSubtitle: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  loadingDots: {
    flexDirection: 'row',
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});

export default FindParkingScreen;
