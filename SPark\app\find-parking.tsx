import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  StatusBar,
  Dimensions,
  ScrollView,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withTiming,
  interpolate,
  FadeInDown,
  SlideInUp,
  BounceIn,
} from 'react-native-reanimated';
import MapView, { Marker, Circle } from 'react-native-maps';
import * as Location from 'expo-location';
import { router } from 'expo-router';
import {
  BackIcon,
  LocationIcon,
  ScanIcon,
  SearchIcon
} from '../src/components/ui/AnimatedIcon';
import ParkingSearchAnimation from '../src/components/ui/ParkingSearchAnimation';
import { openStreetMapService } from '../src/services/openStreetMapService';
import { ProcessedParkingSpot } from '../src/types/parking';
import ParkingBottomSheet from '../src/components/parking/ParkingBottomSheet';
import SimpleParkingBottomSheet from '../src/components/parking/SimpleParkingBottomSheet';
import { parkingCache } from '../src/utils/storage';
import { colors, typography } from '../src/theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const FindParkingScreen: React.FC = () => {
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [parkingSpots, setParkingSpots] = useState<ProcessedParkingSpot[]>([]);
  const [searchRadius, setSearchRadius] = useState(1000); // 1km radius
  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const [selectedSpot, setSelectedSpot] = useState<ProcessedParkingSpot | null>(null);
  const mapRef = useRef<any>(null);

  // Test function to show bottom sheet with mock data
  const testBottomSheet = async () => {
    console.log('🧪 Testing bottom sheet with mock data');

    // First test with mock data
    const mockSpots: ProcessedParkingSpot[] = [
      {
        id: 'test_1',
        name: 'Test Parking Spot 1',
        address: '123 Test Street, Test City',
        latitude: 37.7749,
        longitude: -122.4194,
        distance: 150,
        type: 'surface',
        fee: false,
        access: 'public',
        amenities: ['Covered', 'Security'],
        rating: 4.5,
        isAvailable: true,
      },
      {
        id: 'test_2',
        name: 'Test Parking Spot 2',
        address: '456 Test Avenue, Test City',
        latitude: 37.7849,
        longitude: -122.4094,
        distance: 300,
        type: 'underground',
        fee: true,
        access: 'public',
        amenities: ['Covered', 'EV Charging'],
        rating: 4.2,
        isAvailable: true,
      },
    ];

    console.log('🔄 Setting mock parking spots...');
    setParkingSpots(mockSpots);
    console.log('🔄 Setting showBottomSheet to true...');
    setShowBottomSheet(true);
    console.log('✅ Mock data set, bottom sheet should show');

    // Also test with real API if location is available
    if (userLocation) {
      console.log('🌐 Also testing with real API data...');
      try {
        const realResult = await openStreetMapService.findNearbyParking(userLocation, 1000);
        console.log('✅ Real API result:', realResult.spots.length, 'spots');
        if (realResult.spots.length > 0) {
          setTimeout(() => {
            setParkingSpots(realResult.spots);
            console.log('🔄 Updated with real data');
          }, 3000);
        }
      } catch (error) {
        console.error('❌ Real API test failed:', error);
      }
    }
  };

  // Animation values
  const pulseAnimation = useSharedValue(0);
  const searchAnimation = useSharedValue(0);
  const buttonScale = useSharedValue(1);
  const buttonPulse = useSharedValue(0);

  useEffect(() => {
    getCurrentLocation();

    // Start pulse animation
    pulseAnimation.value = withRepeat(
      withTiming(1, { duration: 2000 }),
      -1,
      true
    );

    // Start button pulse animation
    buttonPulse.value = withRepeat(
      withTiming(1, { duration: 3000 }),
      -1,
      true
    );
  }, []);

  // Monitor state changes
  useEffect(() => {
    console.log('🔄 State changed - parkingSpots:', parkingSpots.length);
  }, [parkingSpots]);

  useEffect(() => {
    console.log('🔄 State changed - showBottomSheet:', showBottomSheet);
  }, [showBottomSheet]);

  useEffect(() => {
    console.log('🔄 State changed - isSearching:', isSearching);
  }, [isSearching]);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required to find nearby parking.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
      
      setUserLocation(location);
      
      // Animate to user location
      if (mapRef.current && mapRef.current.animateToRegion) {
        mapRef.current.animateToRegion({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }, 1000);
      }
    } catch (error) {
      Alert.alert('Error', 'Unable to get your location. Please try again.');
    }
  };

  const findNearbyParking = async () => {
    if (!userLocation) {
      Alert.alert('Location Required', 'Please enable location services to find parking.');
      return;
    }

    setIsSearching(true);
    buttonScale.value = withSpring(0.95);
    searchAnimation.value = withTiming(1, { duration: 500 });

    try {
      console.log('🔍 Starting parking search...');
      console.log('📍 User location:', userLocation.coords.latitude, userLocation.coords.longitude);
      console.log('📏 Search radius:', searchRadius);

      // Save current location for offline use
      await parkingCache.setLastKnownLocation(
        userLocation.coords.latitude,
        userLocation.coords.longitude
      );

      // Use the OpenStreetMap service
      console.log('🌐 Calling OpenStreetMap service...');
      const searchResult = await openStreetMapService.findNearbyParking(userLocation, searchRadius);

      console.log('✅ API Response received');
      console.log('📊 Search result:', searchResult);
      console.log('🅿️ Number of spots found:', searchResult.spots.length);
      console.log('🗂️ First few spots:', searchResult.spots.slice(0, 3));

      // Update state with parking spots
      console.log('🔄 Updating parking spots state...');
      setParkingSpots(searchResult.spots);

      // Always show bottom sheet (even for empty results)
      console.log('📱 Showing bottom sheet...');
      setShowBottomSheet(true);

      // Animate map to show all spots
      if (mapRef.current && mapRef.current.fitToCoordinates && searchResult.spots.length > 0) {
        console.log('🗺️ Animating map to fit coordinates...');
        const coordinates = [
          { latitude: userLocation.coords.latitude, longitude: userLocation.coords.longitude },
          ...searchResult.spots.map(spot => ({ latitude: spot.latitude, longitude: spot.longitude }))
        ];

        mapRef.current.fitToCoordinates(coordinates, {
          edgePadding: { top: 100, right: 50, bottom: 300, left: 50 },
          animated: true,
        });
      }

      // Cache results for offline use
      if (searchResult.spots.length > 0) {
        console.log('💾 Caching results...');
        await parkingCache.setOfflineParkingData(searchResult);
        console.log(`✅ Found ${searchResult.spots.length} parking spots!`);
      } else {
        console.log('❌ No parking spots found');
        Alert.alert(
          'No Parking Found',
          'No available parking spots found in your area. Try expanding your search radius.'
        );
      }

    } catch (error) {
      console.error('❌ Error finding parking:', error);

      // Try to load offline data
      console.log('🔄 Trying to load offline data...');
      const offlineData = await parkingCache.getOfflineParkingData();
      console.log('💾 Offline data:', offlineData);

      if (offlineData && offlineData.spots && offlineData.spots.length > 0) {
        console.log('✅ Using cached data with', offlineData.spots.length, 'spots');
        setParkingSpots(offlineData.spots);
        setShowBottomSheet(true);
        Alert.alert(
          'Using Cached Data',
          'Unable to fetch fresh data. Showing previously cached parking spots.'
        );
      } else {
        console.log('❌ No offline data available');
        // Still show bottom sheet with empty state
        setParkingSpots([]);
        setShowBottomSheet(true);
        Alert.alert(
          'Connection Error',
          'Unable to find parking spots. Please check your internet connection and try again.'
        );
      }
    } finally {
      setIsSearching(false);
      buttonScale.value = withSpring(1);
      searchAnimation.value = withTiming(0, { duration: 300 });
    }
  };

  const getMarkerColor = (type: string) => {
    switch (type) {
      case 'multi-storey': return colors.primary[500];
      case 'underground': return colors.primary[700];
      case 'surface': return colors.success[500];
      case 'street_side': return colors.secondary[500];
      default: return colors.gray[500];
    }
  };

  // Animated styles
  const pulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: interpolate(pulseAnimation.value, [0, 1], [1, 1.2]) }],
    opacity: interpolate(pulseAnimation.value, [0, 1], [0.7, 0.3]),
  }));

  const buttonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: buttonScale.value }],
  }));

  const buttonPulseStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: interpolate(buttonPulse.value, [0, 1], [1, 1.1]) }
    ],
    opacity: interpolate(buttonPulse.value, [0, 1], [1, 0.8]),
  }));



  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Full Screen Map Container */}
      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          style={styles.map}
          showsUserLocation={true}
          showsMyLocationButton={false}
          initialRegion={{
            latitude: 37.78825,
            longitude: -122.4324,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          }}
        >
          {/* Search radius circle */}
          {userLocation && (
            <Circle
              center={{
                latitude: userLocation.coords.latitude,
                longitude: userLocation.coords.longitude,
              }}
              radius={searchRadius}
              strokeColor={colors.primary[500]}
              strokeWidth={2}
              fillColor="rgba(255, 214, 10, 0.1)"
            />
          )}

          {/* Parking spot markers */}
          {parkingSpots.map((spot) => (
            <Marker
              key={spot.id}
              coordinate={{
                latitude: spot.latitude,
                longitude: spot.longitude,
              }}
              pinColor={selectedSpot?.id === spot.id ? colors.primary[500] : getMarkerColor(spot.type)}
              onPress={() => {
                console.log('📍 Marker pressed:', spot.name);
                setSelectedSpot(spot);
                // Ensure bottom sheet is visible when marker is pressed
                if (!showBottomSheet) {
                  setShowBottomSheet(true);
                }
              }}
            />
          ))}
        </MapView>
      </View>

      {/* Back Button - Floating */}
      <Animated.View
        style={styles.backButtonContainer}
        entering={FadeInDown.duration(600)}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <BackIcon size={24} color={colors.white} />
        </TouchableOpacity>
      </Animated.View>

      {/* Location Button - Floating */}
      <Animated.View
        style={styles.locationButtonContainer}
        entering={FadeInDown.duration(600).delay(200)}
      >
        <TouchableOpacity
          style={styles.locationButton}
          onPress={getCurrentLocation}
          activeOpacity={0.7}
        >
          <LocationIcon size={24} color={colors.white} />
        </TouchableOpacity>
      </Animated.View>

      {/* Circular Find Button - Bottom Center */}
      <Animated.View
        style={styles.findButtonContainer}
        entering={SlideInUp.duration(800).delay(1000)}
      >
        <Animated.View style={[buttonAnimatedStyle, !isSearching && buttonPulseStyle]}>
          <TouchableOpacity
            style={[styles.findButton, isSearching && styles.findButtonSearching]}
            onPress={findNearbyParking}
            disabled={isSearching}
            activeOpacity={0.8}
          >
            <ScanIcon
              size={36}
              color={isSearching ? colors.gray[400] : colors.black[900]}
            />
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>

      {/* Test Button - Debug */}
      <Animated.View
        style={styles.testButtonContainer}
        entering={SlideInUp.duration(800).delay(1200)}
      >
        <TouchableOpacity
          style={styles.testButton}
          onPress={testBottomSheet}
          activeOpacity={0.8}
        >
          <Text style={styles.testButtonText}>TEST</Text>
        </TouchableOpacity>
      </Animated.View>

      {/* Enhanced Search Animation */}
      <ParkingSearchAnimation
        visible={isSearching}
        onComplete={() => setIsSearching(false)}
      />

      {/* Parking Results Bottom Sheet */}
      <SimpleParkingBottomSheet
        spots={parkingSpots}
        isVisible={showBottomSheet}
        onClose={() => {
          setShowBottomSheet(false);
          setSelectedSpot(null); // Clear selection when closing
        }}
        onSpotSelect={(spot) => setSelectedSpot(spot)}
        selectedSpot={selectedSpot}
        loading={isSearching}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black[900],
  },

  // Floating Controls
  backButtonContainer: {
    position: 'absolute',
    top: 60, // Increased for better spacing from status bar
    left: 20,
    zIndex: 1000,
  },
  backButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(0,0,0,0.8)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  locationButtonContainer: {
    position: 'absolute',
    top: 60, // Increased for better spacing from status bar
    right: 20,
    zIndex: 1000,
  },
  locationButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(0,0,0,0.8)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },

  // Map
  mapContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: screenWidth,
    height: screenHeight,
  },
  map: {
    flex: 1,
    width: '100%',
    height: '100%',
  },

  // Circular Find Button - Fixed positioning
  findButtonContainer: {
    position: 'absolute',
    bottom: 50, // Increased from 40 for better accessibility
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 1000,
    paddingHorizontal: 20, // Ensure it's not cut off on small screens
  },
  findButton: {
    width: 80, // Increased from 72 for better touch target
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary[500],
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.5,
    shadowRadius: 24,
    elevation: 20,
    borderWidth: 3,
    borderColor: colors.white,
  },
  findButtonSearching: {
    backgroundColor: colors.gray[400],
    borderColor: colors.gray[200],
    shadowOpacity: 0.2,
  },

  // Search Overlay
  searchOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 2000,
  },
  searchContent: {
    backgroundColor: colors.white,
    borderRadius: 24,
    padding: 40,
    alignItems: 'center',
    marginHorizontal: 40,
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.3,
    shadowRadius: 24,
    elevation: 16,
  },
  searchIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  searchTitle: {
    fontSize: 24,
    fontWeight: '700',
    fontFamily: typography.fontFamily.brand,
    color: colors.black[900],
    marginBottom: 8,
    textAlign: 'center',
  },
  searchSubtitle: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  loadingDots: {
    flexDirection: 'row',
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },

  // Test Button Styles
  testButtonContainer: {
    position: 'absolute',
    bottom: 140,
    right: 20,
    zIndex: 1000,
  },
  testButton: {
    backgroundColor: colors.secondary[500],
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  testButtonText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '700',
  },
});

export default FindParkingScreen;
