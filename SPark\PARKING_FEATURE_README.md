# OpenStreetMap Parking Feature

This document describes the new parking location feature that uses free and open-source APIs to find nearby public parking spots.

## Overview

The parking feature fetches the 5 closest public parking locations from the user's current GPS position using OpenStreetMap (OSM) data through the Overpass API. It provides a mobile-native interface with a bottom sheet that slides up over the map view.

## Features

### Core Functionality
- **Real-time Location**: Uses device GPS to get current position
- **OpenStreetMap Integration**: Fetches parking data from OSM via Overpass API
- **Distance Calculation**: Uses Haversine formula for accurate distance measurements
- **Offline Support**: Caches results for offline scenarios
- **Bottom Sheet UI**: Mobile-native sliding interface
- **Maps Integration**: Opens locations in Google Maps or Apple Maps

### Data Sources
- **Primary**: OpenStreetMap Overpass API
- **Backup**: Cached data for offline use
- **No paid APIs**: Completely free and open-source

### Search Parameters
- **Radius**: 1.5km maximum search radius
- **Results**: Top 5 closest parking spots
- **Amenity Tag**: Uses `amenity=parking` OSM tag
- **Real-time**: Fresh data with 30-minute cache

## Technical Implementation

### Key Components

#### 1. OpenStreetMap Service (`src/services/openStreetMapService.ts`)
- Handles Overpass API queries
- Processes OSM data into app-friendly format
- Manages caching and offline scenarios
- Validates coordinates and handles errors

#### 2. Parking Bottom Sheet (`src/components/parking/ParkingBottomSheet.tsx`)
- Animated sliding interface
- Expandable parking spot details
- Maps app integration
- Touch-friendly mobile design

#### 3. Geo Utilities (`src/utils/geoUtils.ts`)
- Haversine distance calculation
- Coordinate validation
- Bounding box calculations
- Distance formatting

#### 4. Parking Types (`src/types/parking.ts`)
- TypeScript interfaces for OSM data
- Processed parking spot structure
- API response types

### Data Structure

Each parking spot includes:
- **Name**: Extracted from OSM tags or generated
- **Address**: Built from OSM address components
- **Distance**: Calculated using Haversine formula
- **Type**: Surface, multi-storey, underground, street-side
- **Fee**: Free or paid parking
- **Access**: Public, private, or customer-only
- **Amenities**: Covered, wheelchair accessible, lighting, etc.
- **Mock Rating**: Generated since OSM doesn't provide ratings
- **Availability**: Mock data since OSM doesn't provide real-time info

### Caching Strategy

#### Cache Duration
- **Fresh Data**: 30 minutes
- **Offline Fallback**: Indefinite (until manually cleared)
- **Location-based**: Cached by coordinate grid

#### Cache Keys
- Format: `parking_{lat}_{lon}_{radius}`
- Precision: 3 decimal places for coordinates
- Automatic cleanup of expired cache

### Error Handling

#### Network Issues
1. Try fresh API call
2. Fall back to cached data
3. Show appropriate user messages
4. Graceful degradation

#### Location Issues
1. Request permissions properly
2. Handle permission denials
3. Provide manual location entry option
4. Use last known location if available

## User Interface

### Bottom Sheet Design
- **Minimized State**: Shows count and expand option
- **Expanded State**: Full list with details
- **Gesture Support**: Swipe to expand/collapse/close
- **Touch Targets**: Large, accessible buttons

### Parking Spot Cards
- **Header**: Name, address, distance, rating
- **Details**: Type, capacity, fee status
- **Amenities**: Chips showing available features
- **Actions**: Get directions button

### Map Integration
- **Markers**: Color-coded by parking type
- **Selection**: Tap marker to select spot
- **Radius**: Visual circle showing search area
- **Fit to Bounds**: Auto-zoom to show all results

## API Integration

### Overpass API Query
```
[out:json][timeout:25];
(
  node["amenity"="parking"](bbox);
  way["amenity"="parking"](bbox);
  relation["amenity"="parking"](bbox);
);
out center meta;
```

### Request Headers
- `Content-Type: text/plain`
- `User-Agent: SPark-Mobile-App/1.0`
- `Timeout: 10 seconds`

### Rate Limiting
- Respectful API usage
- Caching to reduce requests
- Error handling for rate limits

## Privacy & Permissions

### Location Permission
- Requested only when needed
- Clear explanation to users
- Graceful handling of denials
- No location data stored remotely

### Data Usage
- Only public OSM data used
- No personal data collection
- Local caching only
- No tracking or analytics

## Testing

### Unit Tests
- Coordinate validation
- Distance calculations
- Data processing
- Error scenarios

### Integration Tests
- API connectivity
- Cache functionality
- UI interactions
- Permission handling

## Future Enhancements

### Potential Improvements
1. **Real-time Availability**: Integration with parking sensors
2. **User Reviews**: Community-driven rating system
3. **Booking Integration**: Reserve parking spots
4. **Payment Processing**: In-app parking payments
5. **Navigation**: Turn-by-turn directions
6. **Favorites**: Save frequently used parking spots

### Data Enhancements
1. **More Amenities**: EV charging, security cameras
2. **Pricing Information**: Hourly rates from external sources
3. **Operating Hours**: Better parsing of opening_hours tags
4. **Accessibility**: Enhanced wheelchair accessibility info

## Troubleshooting

### Common Issues

#### No Results Found
- Check internet connection
- Verify location permissions
- Try different search radius
- Check if in supported area

#### Slow Loading
- Network connectivity issues
- Overpass API server load
- Large search radius
- Clear cache and retry

#### Inaccurate Data
- OSM data quality varies by region
- Community-maintained database
- Report issues to OSM community
- Use multiple data sources

### Debug Information
- Enable console logging
- Check network requests
- Verify coordinate accuracy
- Test with known parking areas

## Contributing

### Adding Features
1. Follow existing code patterns
2. Add comprehensive tests
3. Update documentation
4. Consider offline scenarios

### Improving Data
1. Contribute to OpenStreetMap
2. Add missing parking spots
3. Improve existing data quality
4. Add amenity information

## License

This feature uses OpenStreetMap data, which is available under the Open Database License (ODbL). The implementation code follows the project's existing license terms.
