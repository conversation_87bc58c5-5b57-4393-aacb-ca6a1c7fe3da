import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';

class SecureStorage {
  async setItem(key: string, value: string): Promise<void> {
    try {
      await SecureStore.setItemAsync(key, value);
    } catch (error) {
      console.error('SecureStorage setItem error:', error);
      // Fallback to AsyncStorage for development
      await AsyncStorage.setItem(key, value);
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      return await SecureStore.getItemAsync(key);
    } catch (error) {
      console.error('SecureStorage getItem error:', error);
      // Fallback to AsyncStorage for development
      return await AsyncStorage.getItem(key);
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(key);
    } catch (error) {
      console.error('SecureStorage removeItem error:', error);
      // Fallback to AsyncStorage for development
      await AsyncStorage.removeItem(key);
    }
  }

  async clear(): Promise<void> {
    try {
      // SecureStore doesn't have a clear method, so we need to remove items individually
      const keys = ['token', 'user', 'refreshToken'];
      await Promise.all(keys.map(key => this.removeItem(key)));
    } catch (error) {
      console.error('SecureStorage clear error:', error);
    }
  }
}

class LocalStorage {
  async setItem(key: string, value: string): Promise<void> {
    try {
      await AsyncStorage.setItem(key, value);
    } catch (error) {
      console.error('LocalStorage setItem error:', error);
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(key);
    } catch (error) {
      console.error('LocalStorage getItem error:', error);
      return null;
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error('LocalStorage removeItem error:', error);
    }
  }

  async clear(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('LocalStorage clear error:', error);
    }
  }

  async getAllKeys(): Promise<string[]> {
    try {
      return await AsyncStorage.getAllKeys();
    } catch (error) {
      console.error('LocalStorage getAllKeys error:', error);
      return [];
    }
  }

  async multiGet(keys: string[]): Promise<[string, string | null][]> {
    try {
      return await AsyncStorage.multiGet(keys);
    } catch (error) {
      console.error('LocalStorage multiGet error:', error);
      return [];
    }
  }

  async multiSet(keyValuePairs: [string, string][]): Promise<void> {
    try {
      await AsyncStorage.multiSet(keyValuePairs);
    } catch (error) {
      console.error('LocalStorage multiSet error:', error);
    }
  }

  async multiRemove(keys: string[]): Promise<void> {
    try {
      await AsyncStorage.multiRemove(keys);
    } catch (error) {
      console.error('LocalStorage multiRemove error:', error);
    }
  }
}

// Storage utilities for specific data types
export const userPreferences = {
  async setTheme(theme: 'light' | 'dark' | 'system'): Promise<void> {
    await localStorage.setItem('theme', theme);
  },

  async getTheme(): Promise<'light' | 'dark' | 'system'> {
    const theme = await localStorage.getItem('theme');
    return (theme as 'light' | 'dark' | 'system') || 'system';
  },

  async setLanguage(language: string): Promise<void> {
    await localStorage.setItem('language', language);
  },

  async getLanguage(): Promise<string> {
    return (await localStorage.getItem('language')) || 'en';
  },

  async setNotificationSettings(settings: any): Promise<void> {
    await localStorage.setItem('notificationSettings', JSON.stringify(settings));
  },

  async getNotificationSettings(): Promise<any> {
    const settings = await localStorage.getItem('notificationSettings');
    return settings ? JSON.parse(settings) : {
      bookingReminders: true,
      paymentNotifications: true,
      promotionalOffers: false,
      systemUpdates: true,
    };
  },

  async setMapSettings(settings: any): Promise<void> {
    await localStorage.setItem('mapSettings', JSON.stringify(settings));
  },

  async getMapSettings(): Promise<any> {
    const settings = await localStorage.getItem('mapSettings');
    return settings ? JSON.parse(settings) : {
      mapType: 'standard',
      showTraffic: false,
      autoZoom: true,
      defaultRadius: 5,
    };
  },
};

export const cacheManager = {
  async setCache(key: string, data: any, expirationMinutes: number = 60): Promise<void> {
    const cacheData = {
      data,
      timestamp: Date.now(),
      expiration: Date.now() + (expirationMinutes * 60 * 1000),
    };
    await localStorage.setItem(`cache_${key}`, JSON.stringify(cacheData));
  },

  async getCache(key: string): Promise<any | null> {
    try {
      const cacheStr = await localStorage.getItem(`cache_${key}`);
      if (!cacheStr) return null;

      const cache = JSON.parse(cacheStr);
      if (Date.now() > cache.expiration) {
        await localStorage.removeItem(`cache_${key}`);
        return null;
      }

      return cache.data;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  },

  async clearExpiredCache(): Promise<void> {
    try {
      const keys = await localStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      
      for (const key of cacheKeys) {
        const cacheStr = await localStorage.getItem(key);
        if (cacheStr) {
          const cache = JSON.parse(cacheStr);
          if (Date.now() > cache.expiration) {
            await localStorage.removeItem(key);
          }
        }
      }
    } catch (error) {
      console.error('Clear expired cache error:', error);
    }
  },

  async clearAllCache(): Promise<void> {
    try {
      const keys = await localStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      await localStorage.multiRemove(cacheKeys);
    } catch (error) {
      console.error('Clear all cache error:', error);
    }
  },
};

export const secureStorage = new SecureStorage();
export const localStorage = new LocalStorage();

export default {
  secureStorage,
  localStorage,
  userPreferences,
  cacheManager,
};
