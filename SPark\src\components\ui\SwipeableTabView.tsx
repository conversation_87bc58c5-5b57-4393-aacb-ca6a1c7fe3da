import React, { useState } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  withSpring,
  runOnJS,
  interpolate,
} from 'react-native-reanimated';
import { colors } from '../../theme';

const { width: screenWidth } = Dimensions.get('window');

interface SwipeableTabViewProps {
  children: React.ReactNode[];
  currentIndex: number;
  onIndexChange: (index: number) => void;
  tabBarHeight?: number;
}

const SwipeableTabView: React.FC<SwipeableTabViewProps> = ({
  children,
  currentIndex,
  onIndexChange,
  tabBarHeight = 80,
}) => {
  const translateX = useSharedValue(-currentIndex * screenWidth);
  const [activeIndex, setActiveIndex] = useState(currentIndex);

  React.useEffect(() => {
    translateX.value = withSpring(-currentIndex * screenWidth, {
      damping: 15,
      stiffness: 150,
    });
    setActiveIndex(currentIndex);
  }, [currentIndex]);

  const gestureHandler = useAnimatedGestureHandler({
    onStart: (_, context: any) => {
      context.startX = translateX.value;
    },
    onActive: (event, context) => {
      translateX.value = context.startX + event.translationX;
    },
    onEnd: (event) => {
      const velocity = event.velocityX;
      const translation = event.translationX;
      
      // Determine if we should switch tabs
      const shouldSwitchTab = Math.abs(translation) > screenWidth * 0.3 || Math.abs(velocity) > 500;
      
      if (shouldSwitchTab) {
        let newIndex = activeIndex;
        
        if (translation > 0 && activeIndex > 0) {
          // Swipe right - go to previous tab
          newIndex = activeIndex - 1;
        } else if (translation < 0 && activeIndex < children.length - 1) {
          // Swipe left - go to next tab
          newIndex = activeIndex + 1;
        }
        
        translateX.value = withSpring(-newIndex * screenWidth, {
          damping: 15,
          stiffness: 150,
        });
        
        if (newIndex !== activeIndex) {
          runOnJS(onIndexChange)(newIndex);
        }
      } else {
        // Snap back to current tab
        translateX.value = withSpring(-activeIndex * screenWidth, {
          damping: 15,
          stiffness: 150,
        });
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const renderTabContent = () => {
    return children.map((child, index) => (
      <View key={index} style={[styles.tabContent, { width: screenWidth }]}>
        {child}
      </View>
    ));
  };

  return (
    <View style={[styles.container, { paddingBottom: tabBarHeight }]}>
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View style={[styles.contentContainer, animatedStyle]}>
          {renderTabContent()}
        </Animated.View>
      </PanGestureHandler>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  tabContent: {
    flex: 1,
  },
});

export default SwipeableTabView;
