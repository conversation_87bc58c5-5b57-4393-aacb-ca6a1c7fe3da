import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Animated, {
  FadeInDown,
  FadeInUp,
  SlideInRight,
  BounceIn,
} from 'react-native-reanimated';
import { router, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAppDispatch, useAppSelector } from '../../src/store';
import { createBooking } from '../../src/store/slices/bookingSlice';
import { fetchSpotDetails } from '../../src/store/slices/spotSlice';
import { paymentAPI, vehicleAPI } from '../../src/services/api';
import MobileInput from '../../src/components/ui/MobileInput';
import {
  BackIcon,
  PaymentIcon,
  CheckIcon,
  LocationIcon,
  ClockIcon,
  WalletIcon,
  CreditCardIcon
} from '../../src/components/ui/AnimatedIcon';
import { colors, spacing, typography, borderRadius } from '../../src/theme';

export default function PaymentScreen() {
  const { spotId, startTime, endTime } = useLocalSearchParams<{
    spotId: string;
    startTime: string;
    endTime: string;
  }>();
  
  const dispatch = useAppDispatch();
  const { selectedSpot } = useAppSelector((state) => state.spot);
  const { isAuthenticated, user } = useAppSelector((state) => state.auth);
  
  const [paymentMethods, setPaymentMethods] = useState<any[]>([]);
  const [vehicles, setVehicles] = useState<any[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [selectedVehicle, setSelectedVehicle] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);

  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/auth/login');
      return;
    }
    
    loadData();
  }, [isAuthenticated, spotId]);

  const loadData = async () => {
    try {
      setIsLoadingData(true);
      
      // Load spot details if not already loaded
      if (spotId && (!selectedSpot || selectedSpot.id !== spotId)) {
        await dispatch(fetchSpotDetails(spotId));
      }
      
      // Load payment methods and vehicles
      const [paymentResponse, vehiclesResponse] = await Promise.all([
        paymentAPI.getPaymentMethods(),
        vehicleAPI.getUserVehicles(),
      ]);
      
      if (paymentResponse.success && paymentResponse.data) {
        setPaymentMethods(paymentResponse.data);
        const defaultPayment = paymentResponse.data.find((pm: any) => pm.isDefault);
        if (defaultPayment) {
          setSelectedPaymentMethod(defaultPayment.id);
        }
      }
      
      if (vehiclesResponse.success && vehiclesResponse.data) {
        setVehicles(vehiclesResponse.data);
        const defaultVehicle = vehiclesResponse.data.find((v: any) => v.isDefault);
        if (defaultVehicle) {
          setSelectedVehicle(defaultVehicle.id);
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load payment information.');
    } finally {
      setIsLoadingData(false);
    }
  };

  const calculateTotal = () => {
    if (!selectedSpot || !startTime || !endTime) return 0;
    
    const start = new Date(startTime);
    const end = new Date(endTime);
    const hours = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60));
    const subtotal = selectedSpot.hourlyRate * hours;
    const serviceFee = 2.00;
    
    return subtotal + serviceFee;
  };

  const calculateSubtotal = () => {
    if (!selectedSpot || !startTime || !endTime) return 0;
    
    const start = new Date(startTime);
    const end = new Date(endTime);
    const hours = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60));
    
    return selectedSpot.hourlyRate * hours;
  };

  const getDuration = () => {
    if (!startTime || !endTime) return '';
    
    const start = new Date(startTime);
    const end = new Date(endTime);
    const hours = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60));
    
    return `${hours} hour${hours !== 1 ? 's' : ''}`;
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      }),
    };
  };

  const handleConfirmBooking = async () => {
    if (!selectedPaymentMethod) {
      Alert.alert('Payment Method Required', 'Please select a payment method.');
      return;
    }
    
    if (!selectedVehicle) {
      Alert.alert('Vehicle Required', 'Please select a vehicle.');
      return;
    }
    
    setIsLoading(true);
    
    try {
      const bookingData = {
        spotId: spotId!,
        vehicleId: selectedVehicle,
        startTime: startTime!,
        endTime: endTime!,
        paymentMethodId: selectedPaymentMethod,
      };
      
      const result = await dispatch(createBooking(bookingData)).unwrap();
      
      Alert.alert(
        'Booking Confirmed!',
        'Your parking spot has been successfully booked.',
        [
          {
            text: 'View Booking',
            onPress: () => {
              router.replace(`/booking/details?bookingId=${result.id}`);
            },
          },
        ]
      );
    } catch (error: any) {
      Alert.alert('Booking Failed', error.message || 'Failed to create booking. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderBookingSummary = () => {
    if (!selectedSpot) return null;
    
    const startDateTime = formatDateTime(startTime!);
    const endDateTime = formatDateTime(endTime!);
    
    return (
      <Card style={styles.summaryCard}>
        <Text style={styles.sectionTitle}>Booking Summary</Text>
        
        <View style={styles.spotInfo}>
          <Text style={styles.spotTitle}>{selectedSpot.title}</Text>
          <Text style={styles.spotAddress}>{selectedSpot.address}</Text>
        </View>
        
        <View style={styles.timeInfo}>
          <View style={styles.timeRow}>
            <Text style={styles.timeLabel}>Start:</Text>
            <Text style={styles.timeValue}>
              {startDateTime.date} at {startDateTime.time}
            </Text>
          </View>
          <View style={styles.timeRow}>
            <Text style={styles.timeLabel}>End:</Text>
            <Text style={styles.timeValue}>
              {endDateTime.date} at {endDateTime.time}
            </Text>
          </View>
          <View style={styles.timeRow}>
            <Text style={styles.timeLabel}>Duration:</Text>
            <Text style={styles.timeValue}>{getDuration()}</Text>
          </View>
        </View>
      </Card>
    );
  };

  const renderVehicleSelection = () => (
    <Card style={styles.selectionCard}>
      <Text style={styles.sectionTitle}>Select Vehicle</Text>
      
      {vehicles.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>No vehicles found</Text>
          <Button
            title="Add Vehicle"
            onPress={() => router.push('/profile/vehicles')}
            variant="outline"
            size="sm"
          />
        </View>
      ) : (
        <View style={styles.optionsList}>
          {vehicles.map((vehicle) => (
            <TouchableOpacity
              key={vehicle.id}
              style={[
                styles.optionItem,
                selectedVehicle === vehicle.id && styles.optionItemSelected,
              ]}
              onPress={() => setSelectedVehicle(vehicle.id)}
            >
              <View style={styles.optionContent}>
                <Text style={styles.optionTitle}>
                  {vehicle.year} {vehicle.make} {vehicle.model}
                </Text>
                <Text style={styles.optionSubtitle}>{vehicle.licensePlate}</Text>
              </View>
              <View style={styles.radioButton}>
                {selectedVehicle === vehicle.id && <View style={styles.radioButtonSelected} />}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </Card>
  );

  const renderPaymentMethodSelection = () => (
    <Card style={styles.selectionCard}>
      <Text style={styles.sectionTitle}>Payment Method</Text>
      
      {paymentMethods.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>No payment methods found</Text>
          <Button
            title="Add Payment Method"
            onPress={() => router.push('/profile/payment-methods')}
            variant="outline"
            size="sm"
          />
        </View>
      ) : (
        <View style={styles.optionsList}>
          {paymentMethods.map((method) => (
            <TouchableOpacity
              key={method.id}
              style={[
                styles.optionItem,
                selectedPaymentMethod === method.id && styles.optionItemSelected,
              ]}
              onPress={() => setSelectedPaymentMethod(method.id)}
            >
              <View style={styles.optionContent}>
                <Text style={styles.optionTitle}>
                  {method.brand?.toUpperCase()} •••• {method.last4}
                </Text>
                <Text style={styles.optionSubtitle}>
                  {method.type === 'card' ? 'Credit Card' : 'Bank Account'}
                </Text>
              </View>
              <View style={styles.radioButton}>
                {selectedPaymentMethod === method.id && <View style={styles.radioButtonSelected} />}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </Card>
  );

  const renderPricingBreakdown = () => (
    <Animated.View
      style={styles.pricingCard}
      entering={FadeInUp.duration(800).delay(600)}
    >
      <Text style={styles.sectionTitle}>Pricing Breakdown</Text>

      <View style={styles.priceRow}>
        <Text style={styles.priceLabel}>
          ${selectedSpot?.hourlyRate}/hour × {getDuration()}
        </Text>
        <Text style={styles.priceValue}>${calculateSubtotal().toFixed(2)}</Text>
      </View>

      <View style={styles.priceRow}>
        <Text style={styles.priceLabel}>Service fee</Text>
        <Text style={styles.priceValue}>$2.00</Text>
      </View>

      <View style={[styles.priceRow, styles.totalRow]}>
        <Text style={styles.totalLabel}>Total</Text>
        <Text style={styles.totalValue}>${calculateTotal().toFixed(2)}</Text>
      </View>
    </Animated.View>
  );

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  if (isLoadingData) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading payment information...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {renderBookingSummary()}
      {renderVehicleSelection()}
      {renderPaymentMethodSelection()}
      {renderPricingBreakdown()}
      
      <View style={styles.confirmSection}>
        <Button
          title={`Confirm Booking - $${calculateTotal().toFixed(2)}`}
          onPress={handleConfirmBooking}
          loading={isLoading}
          fullWidth
          style={styles.confirmButton}
        />
        
        <Text style={styles.termsText}>
          By confirming this booking, you agree to our Terms of Service and Privacy Policy.
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
    padding: spacing[4],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
  },
  loadingText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  summaryCard: {
    marginBottom: spacing[4],
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[4],
  },
  spotInfo: {
    marginBottom: spacing[4],
  },
  spotTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.gray[900],
    marginBottom: spacing[1],
  },
  spotAddress: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  timeInfo: {
    gap: spacing[2],
  },
  timeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeLabel: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  timeValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.gray[900],
  },
  selectionCard: {
    marginBottom: spacing[4],
  },
  optionsList: {
    gap: spacing[2],
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing[3],
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: borderRadius.md,
    backgroundColor: colors.white,
  },
  optionItemSelected: {
    borderColor: colors.primary[500],
    backgroundColor: colors.primary[50],
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.gray[900],
    marginBottom: spacing[1],
  },
  optionSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.gray[300],
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonSelected: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary[500],
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing[6],
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    marginBottom: spacing[3],
  },
  pricingCard: {
    marginBottom: spacing[6],
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing[2],
  },
  priceLabel: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
  },
  priceValue: {
    fontSize: typography.fontSize.base,
    color: colors.gray[900],
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    marginTop: spacing[2],
    paddingTop: spacing[3],
  },
  totalLabel: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
  },
  totalValue: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
  },
  confirmSection: {
    paddingBottom: spacing[8],
  },
  confirmButton: {
    marginBottom: spacing[4],
  },
  termsText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
    textAlign: 'center',
    lineHeight: 20,
  },
});
