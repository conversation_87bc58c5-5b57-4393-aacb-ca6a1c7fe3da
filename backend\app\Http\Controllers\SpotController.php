<?php

namespace App\Http\Controllers;

use App\Models\ParkingSpot;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class SpotController extends Controller
{
    /**
     * Search parking spots
     */
    public function search(Request $request)
    {
        try {
            $query = ParkingSpot::with(['host', 'reviews'])
                ->active();

            // Location-based search
            if ($request->has(['latitude', 'longitude'])) {
                $radius = $request->get('radius', 5);
                $query->nearby($request->latitude, $request->longitude, $radius);
            }

            // Date range availability
            if ($request->has(['start_date', 'end_date'])) {
                $query->availableBetween($request->start_date, $request->end_date);
            }

            // Price range filter
            if ($request->has(['min_price', 'max_price'])) {
                $query->withinPriceRange($request->min_price, $request->max_price);
            }

            // Amenities filter
            if ($request->has('amenities') && is_array($request->amenities)) {
                $query->withAmenities($request->amenities);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'distance');
            $sortOrder = $request->get('sort_order', 'asc');

            switch ($sortBy) {
                case 'price':
                    $query->orderBy('hourly_rate', $sortOrder);
                    break;
                case 'rating':
                    // Simplified rating sort - can be enhanced later
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'distance':
                default:
                    // Distance ordering is handled in the nearby scope
                    if (!$request->has(['latitude', 'longitude'])) {
                        $query->orderBy('created_at', 'desc');
                    }
                    break;
            }

            // Pagination
            $limit = min($request->get('limit', 20), 50);
            $spots = $query->paginate($limit);

            return response()->json([
                'success' => true,
                'message' => 'Search completed successfully',
                'data' => $spots->items(),
                'pagination' => [
                    'current_page' => $spots->currentPage(),
                    'last_page' => $spots->lastPage(),
                    'per_page' => $spots->perPage(),
                    'total' => $spots->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Search failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get nearby parking spots
     */
    public function nearby(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'lat' => 'required|numeric|between:-90,90',
            'lng' => 'required|numeric|between:-180,180',
            'radius' => 'nullable|numeric|min:0.1|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $radius = $request->get('radius', 5);
            $spots = ParkingSpot::with(['host', 'reviews'])
                ->active()
                ->nearby($request->lat, $request->lng, $radius)
                ->limit(20)
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Nearby spots retrieved successfully',
                'data' => $spots
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get nearby spots',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get spot details
     */
    public function show($id)
    {
        try {
            $spot = ParkingSpot::with(['host', 'reviews.reviewer'])
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $spot
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Spot not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Create a new parking spot
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'address' => 'required|string',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'hourly_rate' => 'required|numeric|min:0',
            'daily_rate' => 'nullable|numeric|min:0',
            'monthly_rate' => 'nullable|numeric|min:0',
            'amenities' => 'nullable|array',
            'availability_schedule' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $spot = ParkingSpot::create([
                'host_id' => auth('api')->id(),
                'title' => $request->title,
                'description' => $request->description,
                'address' => $request->address,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'hourly_rate' => $request->hourly_rate,
                'daily_rate' => $request->daily_rate,
                'monthly_rate' => $request->monthly_rate,
                'amenities' => $request->amenities ?? [],
                'availability_schedule' => $request->availability_schedule ?? [],
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Parking spot created successfully',
                'data' => $spot
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create parking spot',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update parking spot
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string',
            'address' => 'sometimes|required|string',
            'latitude' => 'sometimes|required|numeric|between:-90,90',
            'longitude' => 'sometimes|required|numeric|between:-180,180',
            'hourly_rate' => 'sometimes|required|numeric|min:0',
            'daily_rate' => 'nullable|numeric|min:0',
            'monthly_rate' => 'nullable|numeric|min:0',
            'amenities' => 'nullable|array',
            'availability_schedule' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $spot = ParkingSpot::where('host_id', auth('api')->id())
                ->findOrFail($id);

            $spot->update($request->only([
                'title', 'description', 'address', 'latitude', 'longitude',
                'hourly_rate', 'daily_rate', 'monthly_rate', 'amenities',
                'availability_schedule'
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Parking spot updated successfully',
                'data' => $spot
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update parking spot',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete parking spot
     */
    public function destroy($id)
    {
        try {
            $spot = ParkingSpot::where('host_id', auth('api')->id())
                ->findOrFail($id);

            $spot->delete();

            return response()->json([
                'success' => true,
                'message' => 'Parking spot deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete parking spot',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get host's parking spots
     */
    public function hostSpots()
    {
        try {
            $spots = ParkingSpot::where('host_id', auth('api')->id())
                ->with(['reviews'])
                ->withCount('bookings')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $spots
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get host spots',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle spot availability
     */
    public function toggleAvailability(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'is_active' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $spot = ParkingSpot::where('host_id', auth('api')->id())
                ->findOrFail($id);

            $spot->update(['is_active' => $request->is_active]);

            return response()->json([
                'success' => true,
                'message' => 'Spot availability updated successfully',
                'data' => $spot
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update spot availability',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload spot images
     */
    public function uploadImages(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'images' => 'required|array|max:10',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $spot = ParkingSpot::where('host_id', auth('api')->id())
                ->findOrFail($id);

            $uploadedUrls = [];

            foreach ($request->file('images') as $image) {
                $path = $image->store('spots', 'public');
                $uploadedUrls[] = Storage::url($path);
            }

            // Merge with existing photos
            $existingPhotos = $spot->photos ?? [];
            $allPhotos = array_merge($existingPhotos, $uploadedUrls);

            $spot->update(['photos' => $allPhotos]);

            return response()->json([
                'success' => true,
                'message' => 'Images uploaded successfully',
                'data' => [
                    'urls' => $uploadedUrls,
                    'all_photos' => $allPhotos
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload images',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
