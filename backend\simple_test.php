<?php

echo "🔍 Simple Server Test\n";
echo "====================\n\n";

// Test if server is running
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "❌ Server connection failed: " . $error . "\n";
    echo "Make sure the server is running with: php artisan serve\n";
} else {
    echo "✅ Server is running (HTTP " . $httpCode . ")\n";
    
    // Test a simple API route
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/spots/search');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "API Test (spots/search): HTTP " . $httpCode . "\n";
    if ($httpCode == 200) {
        echo "✅ API is responding\n";
    } else {
        echo "❌ API error - Response: " . substr($response, 0, 200) . "\n";
    }
}
