import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Platform,
} from 'react-native';
import Constants from 'expo-constants';

const NetworkDiagnostic: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, message]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const runDiagnostic = async () => {
    setIsRunning(true);
    clearResults();
    
    addResult('🔍 Network Diagnostic Tool');
    addResult('================================\n');
    
    // Environment info
    addResult('📱 Environment Information:');
    addResult(`Platform: ${Platform.OS}`);
    addResult(`Expo SDK: ${Constants.expoVersion}`);
    addResult(`App Ownership: ${Constants.appOwnership}`);
    addResult(`Device Name: ${Constants.deviceName || 'Unknown'}`);
    addResult('');
    
    // Test multiple URLs systematically
    const testUrls = [
      { name: 'Localhost (Standard)', url: 'http://localhost:8000' },
      { name: 'Localhost Alt', url: 'http://127.0.0.1:8000' },
      { name: 'Common Router IP 1', url: 'http://*************:8000' },
      { name: 'Common Router IP 2', url: 'http://*************:8000' },
      { name: 'Common Router IP 3', url: 'http://**********:8000' },
      { name: 'Common Router IP 4', url: 'http://************:8000' },
    ];
    
    addResult('🌐 Testing Network Connectivity:');
    addResult('');
    
    for (const testUrl of testUrls) {
      try {
        addResult(`Testing ${testUrl.name}...`);
        addResult(`URL: ${testUrl.url}`);
        
        // Test basic connectivity first
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000);
        
        const response = await fetch(`${testUrl.url}/api/test`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          signal: controller.signal,
        });
        
        clearTimeout(timeoutId);
        
        if (response.ok) {
          const data = await response.json();
          addResult('✅ SUCCESS! This URL works!');
          addResult(`Server: ${data.data?.server || 'SPark Backend'}`);
          addResult(`Status: ${response.status}`);
          addResult(`🎯 Use this URL: ${testUrl.url}/api`);
          addResult('');
          break; // Stop testing once we find a working URL
        } else {
          addResult(`❌ HTTP Error: ${response.status}`);
        }
        
      } catch (error: any) {
        if (error.name === 'AbortError') {
          addResult('❌ Timeout (3 seconds)');
        } else if (error.message.includes('Network request failed')) {
          addResult('❌ Network request failed (cannot reach server)');
        } else {
          addResult(`❌ Error: ${error.message}`);
        }
      }
      addResult('');
    }
    
    addResult('🔧 Troubleshooting Steps:');
    addResult('');
    addResult('1. BACKEND CHECK:');
    addResult('   • Make sure Laravel is running: cd backend && php artisan serve');
    addResult('   • Check browser: open http://localhost:8000 in your computer browser');
    addResult('   • Should see Laravel welcome page');
    addResult('');
    addResult('2. NETWORK CHECK:');
    addResult('   • Find your computer\'s IP address:');
    addResult('     Windows: ipconfig | findstr IPv4');
    addResult('     Mac: ifconfig | grep inet');
    addResult('   • Make sure your phone/device is on the same WiFi network');
    addResult('');
    addResult('3. FIREWALL CHECK:');
    addResult('   • Windows: Allow Laravel through Windows Firewall');
    addResult('   • Mac: System Preferences > Security > Firewall');
    addResult('   • Allow incoming connections on port 8000');
    addResult('');
    addResult('4. ALTERNATIVE SOLUTIONS:');
    addResult('   • Try running Laravel on all interfaces:');
    addResult('     php artisan serve --host=0.0.0.0 --port=8000');
    addResult('   • Use ngrok for tunneling (if local network fails)');
    addResult('');
    
    setIsRunning(false);
  };

  const testSpecificIP = () => {
    addResult('\n🔍 Manual IP Test Instructions:');
    addResult('1. Find your computer\'s IP address');
    addResult('2. Update the URL in the main test');
    addResult('3. Make sure Laravel is running with:');
    addResult('   php artisan serve --host=0.0.0.0 --port=8000');
    addResult('');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Network Diagnostic</Text>
      <Text style={styles.subtitle}>
        Find the correct URL for your setup
      </Text>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, isRunning && styles.buttonDisabled]}
          onPress={runDiagnostic}
          disabled={isRunning}
        >
          <Text style={styles.buttonText}>
            {isRunning ? 'Testing...' : 'Run Diagnostic'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.helpButton}
          onPress={testSpecificIP}
        >
          <Text style={styles.helpButtonText}>Help</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.resultsContainer}>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 10,
  },
  button: {
    flex: 2,
    backgroundColor: '#dc3545',
    padding: 15,
    borderRadius: 8,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  helpButton: {
    flex: 1,
    backgroundColor: '#28a745',
    padding: 15,
    borderRadius: 8,
  },
  helpButtonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  resultText: {
    fontSize: 13,
    fontFamily: 'monospace',
    color: '#333',
    lineHeight: 18,
  },
});

export default NetworkDiagnostic;
