import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { colors, typography, spacing, borderRadius, shadows } from '../../theme';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  style,
  textStyle,
  icon,
  iconPosition = 'left',
}) => {
  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: borderRadius.md,
      ...shadows.sm,
    };

    // Size styles
    switch (size) {
      case 'sm':
        baseStyle.paddingHorizontal = spacing[3];
        baseStyle.paddingVertical = spacing[2];
        baseStyle.minHeight = 36;
        break;
      case 'lg':
        baseStyle.paddingHorizontal = spacing[6];
        baseStyle.paddingVertical = spacing[4];
        baseStyle.minHeight = 56;
        break;
      default: // md
        baseStyle.paddingHorizontal = spacing[4];
        baseStyle.paddingVertical = spacing[3];
        baseStyle.minHeight = 48;
    }

    // Variant styles
    switch (variant) {
      case 'secondary':
        baseStyle.backgroundColor = colors.black[800];
        break;
      case 'outline':
        baseStyle.backgroundColor = colors.transparent;
        baseStyle.borderWidth = 2;
        baseStyle.borderColor = colors.primary[500];
        baseStyle.shadowOpacity = 0;
        baseStyle.elevation = 0;
        break;
      case 'ghost':
        baseStyle.backgroundColor = colors.transparent;
        baseStyle.shadowOpacity = 0;
        baseStyle.elevation = 0;
        break;
      case 'danger':
        baseStyle.backgroundColor = colors.error[500];
        break;
      default: // primary
        baseStyle.backgroundColor = colors.primary[500];
    }

    // Disabled styles
    if (disabled || loading) {
      baseStyle.opacity = 0.6;
    }

    // Full width
    if (fullWidth) {
      baseStyle.width = '100%';
    }

    return baseStyle;
  };

  const getTextStyle = (): TextStyle => {
    const baseTextStyle: TextStyle = {
      fontWeight: typography.fontWeight.semibold,
      textAlign: 'center',
    };

    // Size styles
    switch (size) {
      case 'sm':
        baseTextStyle.fontSize = typography.fontSize.sm;
        baseTextStyle.lineHeight = typography.lineHeight.sm;
        break;
      case 'lg':
        baseTextStyle.fontSize = typography.fontSize.lg;
        baseTextStyle.lineHeight = typography.lineHeight.lg;
        break;
      default: // md
        baseTextStyle.fontSize = typography.fontSize.base;
        baseTextStyle.lineHeight = typography.lineHeight.base;
    }

    // Variant text colors
    switch (variant) {
      case 'outline':
        baseTextStyle.color = colors.primary[500];
        break;
      case 'ghost':
        baseTextStyle.color = colors.primary[500];
        break;
      case 'primary':
        baseTextStyle.color = colors.black[900]; // Black text on yellow background
        break;
      default:
        baseTextStyle.color = colors.white;
    }

    return baseTextStyle;
  };

  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size="small"
          color={variant === 'outline' || variant === 'ghost' ? colors.primary[500] :
                 variant === 'primary' ? colors.black[900] : colors.white}
        />
      );
    }

    const textElement = (
      <Text style={[getTextStyle(), textStyle]} numberOfLines={1}>
        {title}
      </Text>
    );

    if (!icon) {
      return textElement;
    }

    return (
      <>
        {iconPosition === 'left' && (
          <>{icon}</>
        )}
        {textElement}
        {iconPosition === 'right' && (
          <>{icon}</>
        )}
      </>
    );
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7} // Better touch feedback
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

export default Button;
