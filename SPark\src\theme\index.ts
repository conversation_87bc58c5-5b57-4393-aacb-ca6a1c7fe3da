import { Dimensions, Platform } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Mobile-First Color Palette (Uber + Duolingo Inspired)
export const colors = {
  // Primary brand colors (Yellow like Duolingo's green energy)
  primary: {
    50: '#FFFEF0',
    100: '#FFFACD',
    200: '#FFF59D',
    300: '#FFED4E',
    400: '#FFE135',
    500: '#FFD60A', // Main brand yellow (iOS system yellow)
    600: '#FFB800',
    700: '#E6A500',
    800: '#CC9400',
    900: '#B38300',
  },

  // Secondary accent colors
  secondary: {
    50: '#F0F9FF',
    100: '#E0F2FE',
    200: '#BAE6FD',
    300: '#7DD3FC',
    400: '#38BDF8',
    500: '#0EA5E9', // Uber blue
    600: '#0284C7',
    700: '#0369A1',
    800: '#075985',
    900: '#0C4A6E',
  },

  // Neutral grays (mobile-optimized)
  gray: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },

  // True blacks and whites
  black: {
    50: '#F7F7F7',
    100: '#F0F0F0',
    200: '#E8E8E8',
    300: '#D1D1D1',
    400: '#B4B4B4',
    500: '#8E8E8E',
    600: '#6B6B6B',
    700: '#4A4A4A',
    800: '#2E2E2E',
    900: '#1A1A1A',
  },

  // Success colors (green)
  success: {
    50: '#F0FDF4',
    100: '#DCFCE7',
    200: '#BBF7D0',
    300: '#86EFAC',
    400: '#4ADE80',
    500: '#22C55E',
    600: '#16A34A',
    700: '#15803D',
    800: '#166534',
    900: '#14532D',
  },

  // Warning colors (orange)
  warning: {
    50: '#FFF7ED',
    100: '#FFEDD5',
    200: '#FED7AA',
    300: '#FDBA74',
    400: '#FB923C',
    500: '#F97316',
    600: '#EA580C',
    700: '#C2410C',
    800: '#9A3412',
    900: '#7C2D12',
  },

  // Error colors (red)
  error: {
    50: '#FEF2F2',
    100: '#FEE2E2',
    200: '#FECACA',
    300: '#FCA5A5',
    400: '#F87171',
    500: '#EF4444',
    600: '#DC2626',
    700: '#B91C1C',
    800: '#991B1B',
    900: '#7F1D1D',
  },

  // Special colors
  white: '#FFFFFF',
  transparent: 'transparent',

  // Gradient colors
  gradients: {
    yellowToOrange: ['#FFD700', '#FFA500'],
    blackToGray: ['#000000', '#1A1A1A'],
    yellowFade: ['#FFD700', '#FFF8DC'],
    darkFade: ['#1A1A1A', '#343A40'],
  },
};

// Modern Typography System with System Fonts (Passion One style)
export const typography = {
  fontFamily: {
    // Primary brand font (Bold system fonts for impact like Passion One)
    brand: Platform.OS === 'ios' ? 'Helvetica-Bold' : 'Roboto-Bold',
    brandBold: Platform.OS === 'ios' ? 'Helvetica-Bold' : 'Roboto-Black',
    brandBlack: Platform.OS === 'ios' ? 'Helvetica-Bold' : 'Roboto-Black',

    // Body text fonts (system fonts for reliability)
    regular: Platform.OS === 'ios' ? 'Helvetica' : 'Roboto',
    medium: Platform.OS === 'ios' ? 'Helvetica-Bold' : 'Roboto-Medium',
    semiBold: Platform.OS === 'ios' ? 'Helvetica-Bold' : 'Roboto-Bold',
    bold: Platform.OS === 'ios' ? 'Helvetica-Bold' : 'Roboto-Bold',
    light: Platform.OS === 'ios' ? 'Helvetica-Light' : 'Roboto-Light',
    extraBold: Platform.OS === 'ios' ? 'Helvetica-Bold' : 'Roboto-Black',

    // Fallback to system fonts
    systemRegular: 'System',
    systemMedium: 'System',
    systemBold: 'System',
  },

  fontSize: {
    xs: 10,
    sm: 12,
    base: 14,
    md: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 28,
    '4xl': 32,
    '5xl': 36,
    '6xl': 42,
    '7xl': 48,
    '8xl': 56,
    '9xl': 64,
  },

  lineHeight: {
    xs: 14,
    sm: 16,
    base: 20,
    md: 24,
    lg: 28,
    xl: 28,
    '2xl': 32,
    '3xl': 36,
    '4xl': 40,
    '5xl': 44,
    '6xl': 48,
    '7xl': 56,
    '8xl': 64,
    '9xl': 72,
  },

  fontWeight: {
    thin: '100' as const,
    extraLight: '200' as const,
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
    extraBold: '800' as const,
    black: '900' as const,
  },

  letterSpacing: {
    tighter: -0.5,
    tight: -0.25,
    normal: 0,
    wide: 0.25,
    wider: 0.5,
    widest: 1,
  },
};

// Spacing
export const spacing = {
  0: 0,
  1: 4,
  2: 8,
  3: 12,
  4: 16,
  5: 20,
  6: 24,
  7: 28,
  8: 32,
  9: 36,
  10: 40,
  12: 48,
  16: 64,
  20: 80,
  24: 96,
  32: 128,
};

// Border radius (Further reduced for cleaner, less roundish look)
export const borderRadius = {
  none: 0,
  sm: 1,
  base: 2,
  md: 4,
  lg: 6,
  xl: 8,
  '2xl': 10,
  '3xl': 12,
  full: 9999,
};

// Modern Shadow System
export const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  sm: {
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
  },
  base: {
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
  md: {
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
  },
  lg: {
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 8,
  },
  xl: {
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 15 },
    shadowOpacity: 0.18,
    shadowRadius: 20,
    elevation: 12,
  },
  '2xl': {
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 25 },
    shadowOpacity: 0.25,
    shadowRadius: 25,
    elevation: 16,
  },
  // Special shadows for yellow theme
  yellow: {
    shadowColor: colors.primary[600],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  yellowGlow: {
    shadowColor: colors.primary[500],
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 6,
  },
};

// Layout
export const layout = {
  screenWidth,
  screenHeight,
  isSmallDevice: screenWidth < 375,
  isMediumDevice: screenWidth >= 375 && screenWidth < 414,
  isLargeDevice: screenWidth >= 414,
  
  // Common dimensions
  headerHeight: 60,
  tabBarHeight: 80,
  buttonHeight: 48,
  inputHeight: 48,
  cardPadding: spacing[4],
  screenPadding: spacing[4],
};

// Animation durations
export const animation = {
  fast: 150,
  normal: 300,
  slow: 500,
};

// Modern Theme Variants with Yellow, Black & White
export const lightTheme = {
  colors: {
    ...colors,
    // Background colors
    background: colors.white,
    backgroundSecondary: colors.gray[50],
    surface: colors.white,
    surfaceSecondary: colors.gray[50],
    card: colors.white,
    cardSecondary: colors.primary[50],

    // Text colors
    text: colors.black[900],
    textSecondary: colors.black[600],
    textMuted: colors.gray[500],
    textInverse: colors.white,
    textOnYellow: colors.black[900],

    // Interactive colors
    primary: colors.primary[500], // Main yellow
    primaryHover: colors.primary[600],
    primaryActive: colors.primary[700],
    primaryDisabled: colors.primary[200],

    // Border and divider colors
    border: colors.gray[200],
    borderSecondary: colors.gray[100],
    divider: colors.gray[100],

    // Status colors
    success: colors.success[500],
    warning: colors.warning[500],
    error: colors.error[500],

    // Overlay
    overlay: 'rgba(0, 0, 0, 0.5)',
    overlayLight: 'rgba(0, 0, 0, 0.3)',
  },
  typography,
  spacing,
  borderRadius,
  shadows,
  layout,
  animation,
};

export const darkTheme = {
  colors: {
    ...colors,
    // Background colors
    background: colors.black[900],
    backgroundSecondary: colors.black[800],
    surface: colors.black[800],
    surfaceSecondary: colors.black[700],
    card: colors.black[800],
    cardSecondary: colors.black[700],

    // Text colors
    text: colors.white,
    textSecondary: colors.gray[300],
    textMuted: colors.gray[500],
    textInverse: colors.black[900],
    textOnYellow: colors.black[900],

    // Interactive colors
    primary: colors.primary[500], // Main yellow
    primaryHover: colors.primary[400],
    primaryActive: colors.primary[300],
    primaryDisabled: colors.primary[800],

    // Border and divider colors
    border: colors.black[600],
    borderSecondary: colors.black[700],
    divider: colors.black[700],

    // Status colors
    success: colors.success[400],
    warning: colors.warning[400],
    error: colors.error[400],

    // Overlay
    overlay: 'rgba(0, 0, 0, 0.7)',
    overlayLight: 'rgba(0, 0, 0, 0.5)',
  },
  typography,
  spacing,
  borderRadius,
  shadows,
  layout,
  animation,
};

export type Theme = typeof lightTheme;

export default {
  light: lightTheme,
  dark: darkTheme,
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  layout,
  animation,
};
