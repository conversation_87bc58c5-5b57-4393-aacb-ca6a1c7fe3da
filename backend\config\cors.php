<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */

    'paths' => ['api/*', 'sanctum/csrf-cookie'],

    'allowed_methods' => ['*'],

    'allowed_origins' => [
        'http://localhost:3000',
        'http://localhost:19006',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:19006',
        'exp://*************:8081', // Expo development
        'exp://localhost:8081',
    ],

    'allowed_origins_patterns' => [
        '/^exp:\/\/.*/',
        '/^http:\/\/192\.168\..*/',
        '/^http:\/\/10\.0\..*/',
        '/^http:\/\/172\.16\..*/',
    ],

    'allowed_headers' => ['*'],

    'exposed_headers' => [],

    'max_age' => 0,

    'supports_credentials' => false,

];
