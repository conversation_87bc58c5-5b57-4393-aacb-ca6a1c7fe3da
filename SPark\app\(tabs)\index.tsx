import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  Dimensions,
  StatusBar,
  SafeAreaView,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  FadeInDown,
  FadeInRight,
} from 'react-native-reanimated';
import { router } from 'expo-router';
import { PlatformMapView as MapView, PlatformMarker as Marker, PLATFORM_PROVIDER_GOOGLE as PROVIDER_GOOGLE } from '../../src/components/PlatformMapView';
import * as Location from 'expo-location';
import {
  SearchIcon,
  LocationIcon,
  CarIcon,
  StarIcon,
  MapIcon,
  UserIcon
} from '../../src/components/ui/AnimatedIcon';
import { colors, typography, spacing, borderRadius } from '../../src/theme';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width, height } = Dimensions.get('window');

export default function HomeScreen() {
  const insets = useSafeAreaInsets();
  const [searchQuery, setSearchQuery] = useState('');
  const [location, setLocation] = useState(null);
  const [nearbySpots, setNearbySpots] = useState([
    {
      id: '1',
      title: 'Downtown Garage',
      address: '123 Main St',
      price: 12,
      rating: 4.5,
      latitude: 37.7749,
      longitude: -122.4194,
      available: true,
      distance: '0.2 mi',
      walkTime: '2 min',
    },
    {
      id: '2',
      title: 'City Center',
      address: '456 Oak Ave',
      price: 8,
      rating: 4.2,
      latitude: 37.7849,
      longitude: -122.4094,
      available: true,
      distance: '0.5 mi',
      walkTime: '5 min',
    },
    {
      id: '3',
      title: 'Business District',
      address: '789 Pine St',
      price: 15,
      rating: 4.8,
      latitude: 37.7649,
      longitude: -122.4294,
      available: false,
      distance: '0.8 mi',
      walkTime: '8 min',
    },
  ]);

  useEffect(() => {
    getCurrentLocation();
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Location permission is required to show nearby parking.');
        return;
      }

      const currentLocation = await Location.getCurrentPositionAsync({});
      setLocation({
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    } catch (error) {
      console.error('Error getting location:', error);
      // Set default location (San Francisco)
      setLocation({
        latitude: 37.7749,
        longitude: -122.4194,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    }
  };

  const handleSearch = () => {
    if (!searchQuery.trim()) {
      Alert.alert('Search Required', 'Please enter a location to search for parking.');
      return;
    }
    router.push('/search');
  };

  const handleQuickAction = (action: string) => {
    Alert.alert('Coming Soon', `${action} functionality will be available soon.`);
  };

  const handleSpotPress = (spotId: string) => {
    router.push(`/spot/details?spotId=${spotId}`);
  };

  const renderHeader = () => (
    <Animated.View
      style={styles.header}
      entering={FadeInDown.duration(800).springify()}
    >
      <View style={styles.headerTop}>
        <View>
          <Text style={styles.greeting}>Good morning</Text>
          <Text style={styles.userName}>John</Text>
        </View>
        <TouchableOpacity
          style={styles.profileButton}
          onPress={() => router.push('/profile')}
          activeOpacity={0.7}
        >
          <UserIcon
            size={24}
            color={colors.gray[600]}
            animationType="bounce"
            trigger={true}
          />
        </TouchableOpacity>
      </View>
    </Animated.View>
  );

  const renderSearchBar = () => (
    <View style={styles.searchSection}>
      <TouchableOpacity style={styles.searchBar} onPress={() => router.push('/search')}>
        <SearchIcon size={20} color={colors.gray[500]} />
        <Text style={styles.searchPlaceholder}>Where do you need to park?</Text>
      </TouchableOpacity>
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.quickActions}>
      <TouchableOpacity style={styles.quickAction} onPress={() => router.push('/bookings')}>
        <View style={styles.quickActionIcon}>
          <CarIcon size={24} color={colors.primary[500]} />
        </View>
        <Text style={styles.quickActionText}>My Bookings</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.quickAction}
        onPress={() => router.push('/find-parking')}
        activeOpacity={0.7}
      >
        <View style={styles.quickActionIcon}>
          <LocationIcon size={24} color={colors.primary[500]} />
        </View>
        <Text style={styles.quickActionText}>Find Nearby</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.quickAction} onPress={() => router.push('/search')}>
        <View style={styles.quickActionIcon}>
          <MapIcon size={24} color={colors.primary[500]} />
        </View>
        <Text style={styles.quickActionText}>Map View</Text>
      </TouchableOpacity>
    </View>
  );

  const renderNearbySpots = () => (
    <View style={styles.nearbySection}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Nearby Spots</Text>
        <TouchableOpacity onPress={() => router.push('/search')}>
          <Text style={styles.seeAllText}>See all</Text>
        </TouchableOpacity>
      </View>

      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.spotsScroll}>
        {nearbySpots.filter(spot => spot.available).map((spot) => (
          <TouchableOpacity
            key={spot.id}
            style={styles.spotCard}
            onPress={() => handleSpotPress(spot.id)}
          >
            <View style={styles.spotHeader}>
              <Text style={styles.spotTitle}>{spot.title}</Text>
              <View style={styles.priceTag}>
                <Text style={styles.priceText}>${spot.price}/hr</Text>
              </View>
            </View>

            <Text style={styles.spotAddress}>{spot.address}</Text>

            <View style={styles.spotFooter}>
              <View style={styles.ratingContainer}>
                <StarIcon size={14} color={colors.primary[500]} />
                <Text style={styles.ratingText}>{spot.rating}</Text>
              </View>
              <Text style={styles.distanceText}>{spot.distance} • {spot.walkTime}</Text>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderHeader()}
        {renderSearchBar()}
        {renderQuickActions()}
        {renderNearbySpots()}

      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    flex: 1,
  },

  // Header Section (Uber-inspired)
  header: {
    paddingHorizontal: 20,
    paddingTop: 50, // Increased to avoid status bar overlap
    paddingBottom: 24,
    backgroundColor: colors.white,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greeting: {
    fontSize: 16,
    color: colors.gray[600],
    fontWeight: '400',
  },
  userName: {
    fontSize: 32,
    fontWeight: '700',
    fontFamily: typography.fontFamily.brand,
    color: colors.black[900],
    marginTop: 2,
    letterSpacing: 1,
  },
  profileButton: {
    width: 44,
    height: 44,
    borderRadius: 8, // Reduced from 22 to 8 for less roundish look
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Search Section (Uber-inspired)
  searchSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: 8, // Reduced from 12 to 8
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 12,
  },
  searchPlaceholder: {
    fontSize: 16,
    color: colors.gray[600],
    fontWeight: '500',
  },

  // Quick Actions (Duolingo-inspired)
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 32,
    gap: 16,
  },
  quickAction: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
  },
  quickActionIcon: {
    width: 56,
    height: 56,
    borderRadius: 12, // Reduced from 28 to 12 for less roundish look
    backgroundColor: colors.primary[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.gray[700],
    textAlign: 'center',
  },
  // Nearby Spots Section
  nearbySection: {
    paddingBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.black[900],
  },
  seeAllText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary[500],
  },

  // Spot Cards (Mobile-optimized)
  spotsScroll: {
    paddingLeft: 20,
  },
  spotCard: {
    width: 280,
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginRight: 16,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  spotHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  spotTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.black[900],
    flex: 1,
  },
  priceTag: {
    backgroundColor: colors.primary[500],
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  priceText: {
    fontSize: 14,
    fontWeight: '700',
    color: colors.black[900],
  },
  spotAddress: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 12,
  },
  spotFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[700],
  },
  distanceText: {
    fontSize: 12,
    color: colors.gray[500],
    fontWeight: '500',
  },
});
