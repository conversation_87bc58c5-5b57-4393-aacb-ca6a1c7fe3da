<?php

namespace App\Http\Controllers;

use App\Models\PaymentMethod;
use App\Models\Booking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Stripe\Stripe;
use Stripe\PaymentMethod as StripePaymentMethod;
use Stripe\PaymentIntent;

class PaymentController extends Controller
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Get user's payment methods
     */
    public function getMethods()
    {
        try {
            $paymentMethods = PaymentMethod::forUser(auth()->id())
                ->orderBy('is_default', 'desc')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $paymentMethods
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get payment methods',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add a new payment method
     */
    public function addMethod(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_method_id' => 'required|string',
            'is_default' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Retrieve payment method from Stripe
            $stripePaymentMethod = StripePaymentMethod::retrieve($request->payment_method_id);

            // Create payment method record
            $paymentMethod = PaymentMethod::create([
                'user_id' => auth()->id(),
                'type' => $stripePaymentMethod->type,
                'last4' => $stripePaymentMethod->card->last4 ?? '',
                'brand' => $stripePaymentMethod->card->brand ?? '',
                'stripe_payment_method_id' => $request->payment_method_id,
                'exp_month' => $stripePaymentMethod->card->exp_month ?? null,
                'exp_year' => $stripePaymentMethod->card->exp_year ?? null,
                'is_default' => $request->get('is_default', false),
            ]);

            // If this should be the default payment method, update others
            if ($request->get('is_default', false)) {
                $paymentMethod->setAsDefault();
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment method added successfully',
                'data' => $paymentMethod
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add payment method',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete payment method
     */
    public function deleteMethod($id)
    {
        try {
            $paymentMethod = PaymentMethod::where('user_id', auth()->id())
                ->findOrFail($id);

            // Detach from Stripe (optional, depending on your needs)
            try {
                $stripePaymentMethod = StripePaymentMethod::retrieve($paymentMethod->stripe_payment_method_id);
                $stripePaymentMethod->detach();
            } catch (\Exception $e) {
                // Log error but don't fail the deletion
                \Log::warning('Failed to detach payment method from Stripe: ' . $e->getMessage());
            }

            $paymentMethod->delete();

            return response()->json([
                'success' => true,
                'message' => 'Payment method deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete payment method',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Set payment method as default
     */
    public function setDefaultMethod($id)
    {
        try {
            $paymentMethod = PaymentMethod::where('user_id', auth()->id())
                ->findOrFail($id);

            $paymentMethod->setAsDefault();

            return response()->json([
                'success' => true,
                'message' => 'Default payment method updated successfully',
                'data' => $paymentMethod
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to set default payment method',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process payment for booking
     */
    public function processPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'booking_id' => 'required|exists:bookings,id',
            'payment_method_id' => 'nullable|exists:payment_methods,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $booking = Booking::where('driver_id', auth()->id())
                ->findOrFail($request->booking_id);

            // Get payment method
            $paymentMethodId = $request->payment_method_id;
            if (!$paymentMethodId) {
                $defaultPaymentMethod = PaymentMethod::where('user_id', auth()->id())
                    ->where('is_default', true)
                    ->first();
                
                if (!$defaultPaymentMethod) {
                    return response()->json([
                        'success' => false,
                        'message' => 'No payment method available'
                    ], 400);
                }
                
                $paymentMethodId = $defaultPaymentMethod->id;
            }

            $paymentMethod = PaymentMethod::where('user_id', auth()->id())
                ->findOrFail($paymentMethodId);

            // Create payment intent
            $paymentIntent = PaymentIntent::create([
                'amount' => $booking->total_amount * 100, // Convert to cents
                'currency' => 'usd',
                'payment_method' => $paymentMethod->stripe_payment_method_id,
                'confirmation_method' => 'manual',
                'confirm' => true,
                'return_url' => config('app.url') . '/payment/return',
            ]);

            // Update booking with payment intent
            $booking->update([
                'payment_intent_id' => $paymentIntent->id,
                'payment_status' => 'paid',
                'status' => 'confirmed',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully',
                'data' => [
                    'booking' => $booking,
                    'payment_intent' => $paymentIntent
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment history
     */
    public function getHistory(Request $request)
    {
        try {
            $query = Booking::with(['spot', 'vehicle'])
                ->where('driver_id', auth()->id())
                ->where('payment_status', 'paid');

            // Pagination
            $page = $request->get('page', 1);
            $limit = min($request->get('limit', 20), 50);
            
            $payments = $query->orderBy('created_at', 'desc')
                ->paginate($limit, ['*'], 'page', $page);

            return response()->json([
                'success' => true,
                'data' => $payments->items(),
                'pagination' => [
                    'current_page' => $payments->currentPage(),
                    'last_page' => $payments->lastPage(),
                    'per_page' => $payments->perPage(),
                    'total' => $payments->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get payment history',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
