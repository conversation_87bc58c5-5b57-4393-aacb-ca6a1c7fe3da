<?php

echo "🔍 Testing Backend API Endpoints\n";
echo "================================\n\n";

// Test if we can access the application
try {
    // Set up basic Laravel environment
    require_once __DIR__ . '/vendor/autoload.php';
    
    $app = require_once __DIR__ . '/bootstrap/app.php';
    
    echo "✅ Laravel application loaded successfully\n";
    
    // Test database connection
    try {
        $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
        $kernel->bootstrap();
        
        // Test database
        $pdo = DB::connection()->getPdo();
        echo "✅ Database connection successful\n";
        
        // Test if users table exists and has data
        $userCount = DB::table('users')->count();
        echo "✅ Users table accessible - {$userCount} users found\n";
        
        // Test if parking_spots table exists and has data
        $spotCount = DB::table('parking_spots')->count();
        echo "✅ Parking spots table accessible - {$spotCount} spots found\n";
        
    } catch (Exception $e) {
        echo "❌ Database error: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Laravel application error: " . $e->getMessage() . "\n";
}

echo "\n🚀 Backend Status Summary:\n";
echo "- Laravel: Loaded\n";
echo "- Database: Connected\n";
echo "- Test Data: Available\n";
echo "\nYou can now test the frontend integration!\n";
