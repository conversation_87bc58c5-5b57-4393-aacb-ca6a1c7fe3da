import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Dimensions,
  Image,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import Animated, {
  FadeInDown,
  FadeInUp,
  SlideInRight,
  useSharedValue,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  interpolate,
} from 'react-native-reanimated';
import { router, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  BackIcon,
  StarIcon,
  LocationIcon,
  ClockIcon,
  CarIcon,
  HeartIcon,
  ShareIcon,
  CheckIcon
} from '../../src/components/ui/AnimatedIcon';
import { colors, spacing, typography, borderRadius } from '../../src/theme';

const { width: screenWidth } = Dimensions.get('window');

export default function SpotDetailsScreen() {
  const { spotId } = useLocalSearchParams<{ spotId: string }>();

  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedDuration, setSelectedDuration] = useState(2); // hours

  // Mock spot data
  const mockSpot = {
    id: spotId || '1',
    title: 'Downtown Parking Garage',
    address: '123 Main St, Downtown',
    description: 'Secure covered parking in the heart of downtown. Perfect for business meetings, shopping, or dining. 24/7 access with security cameras.',
    hourlyRate: 12,
    dailyRate: 80,
    rating: 4.5,
    reviewCount: 127,
    amenities: ['Covered', 'Security Camera', '24/7 Access', 'EV Charging'],
    images: [
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400',
      'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
    ],
    host: {
      name: 'John Smith',
      rating: 4.8,
      responseTime: '< 1 hour',
    },
    availability: {
      isAvailable: true,
      nextAvailable: null,
    },
  };

  const handleBookNow = () => {
    const startTime = selectedDate.toISOString();
    const endTime = new Date(selectedDate.getTime() + selectedDuration * 60 * 60 * 1000).toISOString();

    Alert.alert(
      'Confirm Booking',
      `Book ${mockSpot.title} for ${selectedDuration} hours at ${formatPrice(calculateTotalPrice())}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Book Now',
          onPress: () => router.push({
            pathname: '/booking/payment',
            params: {
              spotId: mockSpot.id,
              startTime,
              endTime,
            },
          })
        },
      ]
    );
  };

  const formatPrice = (price: number) => {
    return `$${price.toFixed(2)}`;
  };

  const calculateTotalPrice = () => {
    return mockSpot.hourlyRate * selectedDuration;
  };

  const handleDateTimePress = () => {
    Alert.alert('Date/Time Picker', 'Date and time picker will be available soon.');
  };

  const renderImageGallery = () => {
    // Mock images for now
    const images = [
      'https://via.placeholder.com/400x200/1A9BFF/FFFFFF?text=Parking+Spot+1',
      'https://via.placeholder.com/400x200/1A9BFF/FFFFFF?text=Parking+Spot+2',
      'https://via.placeholder.com/400x200/1A9BFF/FFFFFF?text=Parking+Spot+3',
    ];

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.imageGallery}
        pagingEnabled
      >
        {images.map((image, index) => (
          <View key={index} style={styles.imageContainer}>
            <View style={styles.imagePlaceholder}>
              <Text style={styles.imagePlaceholderText}>📷</Text>
            </View>
          </View>
        ))}
      </ScrollView>
    );
  };

  const renderSpotInfo = () => {
    return (
      <View style={styles.spotInfoCard}>
        <Text style={styles.spotTitle}>{mockSpot.title}</Text>
        <Text style={styles.spotAddress}>{mockSpot.address}</Text>

        <View style={styles.ratingContainer}>
          <Text style={styles.rating}>★ {mockSpot.rating.toFixed(1)}</Text>
          <Text style={styles.reviewCount}>({mockSpot.reviewCount} reviews)</Text>
        </View>

        <Text style={styles.description}>{mockSpot.description}</Text>

        <View style={styles.priceContainer}>
          <Text style={styles.priceLabel}>Pricing</Text>
          <Text style={styles.hourlyRate}>{formatPrice(mockSpot.hourlyRate)}/hour</Text>
          <Text style={styles.dailyRate}>{formatPrice(mockSpot.dailyRate)}/day</Text>
        </View>
      </View>
    );
  };

  const renderAmenities = () => {
    return (
      <View style={styles.amenitiesCard}>
        <Text style={styles.sectionTitle}>Amenities</Text>
        <View style={styles.amenitiesContainer}>
          {mockSpot.amenities.map((amenity, index) => (
            <View key={index} style={styles.amenityChip}>
              <Text style={styles.amenityText}>{amenity}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderBookingSection = () => {
    const durationOptions = [1, 2, 4, 8, 24];

    return (
      <View style={styles.bookingCard}>
        <Text style={styles.sectionTitle}>Book This Spot</Text>

        <View style={styles.dateTimeSection}>
          <Text style={styles.inputLabel}>Start Time</Text>
          <TouchableOpacity
            style={styles.dateTimeButton}
            onPress={handleDateTimePress}
          >
            <Text style={styles.dateTimeText}>
              {selectedDate.toLocaleDateString()} at {selectedDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.durationSection}>
          <Text style={styles.inputLabel}>Duration</Text>
          <View style={styles.durationOptions}>
            {durationOptions.map((duration) => (
              <TouchableOpacity
                key={duration}
                style={[
                  styles.durationOption,
                  selectedDuration === duration && styles.durationOptionSelected,
                ]}
                onPress={() => setSelectedDuration(duration)}
              >
                <Text
                  style={[
                    styles.durationOptionText,
                    selectedDuration === duration && styles.durationOptionTextSelected,
                  ]}
                >
                  {duration}h
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.priceBreakdown}>
          <View style={styles.priceRow}>
            <Text style={styles.priceRowLabel}>
              {formatPrice(mockSpot.hourlyRate)} × {selectedDuration} hours
            </Text>
            <Text style={styles.priceRowValue}>
              {formatPrice(calculateTotalPrice())}
            </Text>
          </View>
          <View style={styles.priceRow}>
            <Text style={styles.priceRowLabel}>Service fee</Text>
            <Text style={styles.priceRowValue}>$2.00</Text>
          </View>
          <View style={[styles.priceRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total</Text>
            <Text style={styles.totalValue}>
              {formatPrice(calculateTotalPrice() + 2)}
            </Text>
          </View>
        </View>

        <TouchableOpacity
          style={styles.bookButton}
          onPress={handleBookNow}
        >
          <Text style={styles.bookButtonText}>Book Now</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderAvailability = () => {
    return (
      <View style={styles.availabilityCard}>
        <Text style={styles.sectionTitle}>Availability</Text>
        <Text style={styles.availabilityText}>
          Available 24/7 • Instant booking
        </Text>

        <View style={styles.availabilitySchedule}>
          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
            <View key={index} style={styles.daySchedule}>
              <Text style={styles.dayLabel}>{day}</Text>
              <Text style={styles.timeLabel}>24h</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  // Remove authentication check for now - can be added later
  // if (!isAuthenticated) {
  //   return null; // Will redirect to login
  // }

  // Remove loading check for now - using mock data
  // if (isLoading || !selectedSpot) {
  //   return (
  //     <View style={styles.loadingContainer}>
  //       <Text style={styles.loadingText}>Loading spot details...</Text>
  //     </View>
  //   );
  // }

  const scrollY = useSharedValue(0);
  const insets = useSafeAreaInsets();

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  const headerStyle = useAnimatedStyle(() => {
    const opacity = interpolate(scrollY.value, [0, 100], [0, 1], 'clamp');
    return { opacity };
  });

  const renderFloatingHeader = () => (
    <Animated.View style={[styles.floatingHeader, headerStyle]}>
      <TouchableOpacity style={styles.headerButton} onPress={() => router.back()}>
        <BackIcon size={24} color={colors.black[900]} />
      </TouchableOpacity>
      <Text style={styles.floatingHeaderTitle}>{mockSpot.title}</Text>
      <View style={styles.headerActions}>
        <TouchableOpacity style={styles.headerButton}>
          <ShareIcon size={24} color={colors.black[900]} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.headerButton}>
          <HeartIcon size={24} color={colors.black[900]} />
        </TouchableOpacity>
      </View>
    </Animated.View>
  );

  const renderFixedBookingBar = () => (
    <Animated.View
      style={styles.fixedBookingBar}
      entering={SlideInRight.duration(800).delay(1000)}
    >
      <View style={styles.priceInfo}>
        <Text style={styles.fixedPrice}>${mockSpot.hourlyRate}/hr</Text>
        <Text style={styles.fixedSubtext}>Available now</Text>
      </View>
      <TouchableOpacity style={styles.fixedBookButton} onPress={handleBookNow}>
        <Text style={styles.fixedBookButtonText}>Book Now</Text>
      </TouchableOpacity>
    </Animated.View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {renderFloatingHeader()}

      <Animated.ScrollView
        style={styles.scrollView}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
      >
        {renderImageGallery()}
        {renderSpotInfo()}
        {renderAmenities()}
        {renderAvailability()}
        {renderBookingSection()}

        {/* Add bottom padding for fixed booking bar */}
        <View style={styles.bottomPadding} />
      </Animated.ScrollView>

      {renderFixedBookingBar()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    flex: 1,
  },

  // Floating Header
  floatingHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    zIndex: 1000,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  floatingHeaderTitle: {
    fontSize: 18,
    fontWeight: '700',
    fontFamily: typography.fontFamily.brand,
    color: colors.black[900],
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },

  // Fixed Booking Bar
  fixedBookingBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingBottom: 34, // Safe area
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  priceInfo: {
    flex: 1,
  },
  fixedPrice: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.black[900],
  },
  fixedSubtext: {
    fontSize: 14,
    color: colors.success[600],
    fontWeight: '600',
  },
  fixedBookButton: {
    backgroundColor: colors.primary[500],
    borderRadius: 8,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  fixedBookButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.black[900],
  },
  bottomPadding: {
    height: 100, // Space for fixed booking bar
  },
  loadingText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  imageGallery: {
    height: 200,
    marginBottom: spacing[4],
  },
  imageContainer: {
    width: screenWidth,
    height: 200,
  },
  imagePlaceholder: {
    flex: 1,
    backgroundColor: colors.gray[200],
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePlaceholderText: {
    fontSize: 48,
  },
  spotInfoCard: {
    margin: spacing[4],
    marginBottom: spacing[3],
  },
  spotTitle: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.gray[900],
    marginBottom: spacing[1],
  },
  spotAddress: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    marginBottom: spacing[2],
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[3],
  },
  rating: {
    fontSize: typography.fontSize.base,
    color: colors.warning[600],
    marginRight: spacing[2],
  },
  reviewCount: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
  },
  description: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
    lineHeight: 24,
    marginBottom: spacing[4],
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[3],
  },
  priceLabel: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
  },
  hourlyRate: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
  },
  dailyRate: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  amenitiesCard: {
    margin: spacing[4],
    marginTop: 0,
    marginBottom: spacing[3],
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[3],
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing[2],
  },
  amenityChip: {
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    backgroundColor: colors.primary[50],
    borderRadius: borderRadius.full,
    borderWidth: 1,
    borderColor: colors.primary[200],
  },
  amenityText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary[700],
  },
  availabilityCard: {
    margin: spacing[4],
    marginTop: 0,
    marginBottom: spacing[3],
  },
  availabilityText: {
    fontSize: typography.fontSize.base,
    color: colors.success[600],
    marginBottom: spacing[3],
  },
  availabilitySchedule: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  daySchedule: {
    alignItems: 'center',
  },
  dayLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    marginBottom: spacing[1],
  },
  timeLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.gray[900],
  },
  bookingCard: {
    margin: spacing[4],
    marginTop: 0,
  },
  dateTimeSection: {
    marginBottom: spacing[4],
  },
  inputLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.gray[700],
    marginBottom: spacing[2],
  },
  dateTimeButton: {
    padding: spacing[3],
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: borderRadius.md,
    backgroundColor: colors.white,
  },
  dateTimeText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[900],
  },
  durationSection: {
    marginBottom: spacing[4],
  },
  durationOptions: {
    flexDirection: 'row',
    gap: spacing[2],
  },
  durationOption: {
    flex: 1,
    paddingVertical: spacing[2],
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: borderRadius.md,
    backgroundColor: colors.white,
  },
  durationOptionSelected: {
    borderColor: colors.primary[500],
    backgroundColor: colors.primary[50],
  },
  durationOptionText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[700],
  },
  durationOptionTextSelected: {
    color: colors.primary[600],
    fontWeight: typography.fontWeight.medium,
  },
  priceBreakdown: {
    marginBottom: spacing[4],
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing[2],
  },
  priceRowLabel: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
  },
  priceRowValue: {
    fontSize: typography.fontSize.base,
    color: colors.gray[900],
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    marginTop: spacing[2],
    paddingTop: spacing[3],
  },
  totalLabel: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
  },
  totalValue: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
  },
  bookButton: {
    marginTop: spacing[2],
    backgroundColor: colors.primary[600],
    paddingVertical: spacing[3],
    paddingHorizontal: spacing[4],
    borderRadius: borderRadius.md,
    alignItems: 'center',
  },
  bookButtonText: {
    color: colors.white,
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
  },
});
