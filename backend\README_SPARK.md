# SPark Backend API

A comprehensive Laravel backend API for the SPark parking application, providing real-time parking spot management, booking system, payment processing, and user management.

## 🚀 Features

### Core Functionality
- **JWT Authentication** - Secure token-based authentication
- **User Management** - Driver, Host, and Admin user types
- **Parking Spot Management** - CRUD operations with geolocation search
- **Booking System** - Real-time booking with conflict detection
- **Payment Integration** - Stripe payment processing
- **Review System** - Mutual rating system between drivers and hosts
- **Notification System** - Real-time notifications
- **Vehicle Management** - User vehicle profiles
- **Image Upload** - Spot photos and user avatars

### API Endpoints

#### Authentication (`/api/auth/`)
- `POST /login` - User login
- `POST /register` - User registration
- `POST /logout` - User logout
- `GET /profile` - Get user profile
- `PUT /profile` - Update user profile
- `POST /forgot-password` - Send password reset email
- `POST /reset-password` - Reset password
- `POST /refresh` - Refresh JWT token

#### Parking Spots (`/api/spots/`)
- `GET /search` - Search spots with filters
- `GET /nearby` - Get nearby spots
- `GET /{id}` - Get spot details
- `POST /` - Create new spot (Host only)
- `PUT /{id}` - Update spot (Host only)
- `DELETE /{id}` - Delete spot (Host only)
- `GET /host` - Get host's spots
- `PUT /{id}/availability` - Toggle spot availability
- `POST /{id}/images` - Upload spot images

#### Bookings (`/api/bookings/`)
- `GET /` - Get user bookings
- `POST /` - Create new booking
- `GET /active` - Get active booking
- `GET /host` - Get host bookings
- `GET /{id}` - Get booking details
- `PUT /{id}/extend` - Extend booking
- `PUT /{id}/cancel` - Cancel booking
- `PUT /{id}/complete` - Complete booking

#### Vehicles (`/api/vehicles/`)
- `GET /` - Get user vehicles
- `POST /` - Add new vehicle
- `PUT /{id}` - Update vehicle
- `DELETE /{id}` - Delete vehicle
- `PUT /{id}/default` - Set default vehicle

#### Payment (`/api/payment/`)
- `GET /methods` - Get payment methods
- `POST /methods` - Add payment method
- `DELETE /methods/{id}` - Delete payment method
- `PUT /methods/{id}/default` - Set default payment method
- `POST /process` - Process payment
- `GET /history` - Get payment history

#### Reviews (`/api/reviews/`)
- `GET /` - Get reviews (with filters)
- `POST /` - Create review
- `GET /{id}` - Get review details
- `PUT /{id}` - Update review
- `DELETE /{id}` - Delete review

#### Notifications (`/api/notifications/`)
- `GET /` - Get user notifications
- `PUT /{id}/read` - Mark notification as read
- `PUT /read-all` - Mark all notifications as read

## 🛠️ Installation & Setup

### Prerequisites
- PHP 8.2 or higher
- Composer
- SQLite (default) or MySQL/PostgreSQL
- Node.js (for frontend integration)

### Installation Steps

1. **Install Dependencies**
   ```bash
   composer install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

3. **Configure Database**
   - For SQLite (default): Database file will be created automatically
   - For MySQL/PostgreSQL: Update `.env` with your database credentials

4. **Install Required Packages**
   ```bash
   composer require tymon/jwt-auth
   composer require stripe/stripe-php
   composer require intervention/image
   composer require spatie/laravel-cors
   ```

5. **Generate JWT Secret**
   ```bash
   php artisan jwt:secret
   ```

6. **Run Migrations**
   ```bash
   php artisan migrate
   ```

7. **Create Storage Link**
   ```bash
   php artisan storage:link
   ```

8. **Start Development Server**
   ```bash
   php artisan serve
   ```

### Environment Variables

Update your `.env` file with the following configurations:

```env
# App Configuration
APP_NAME="SPark Backend"
APP_URL=http://localhost:8000

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_TTL=60
JWT_REFRESH_TTL=20160

# Stripe Configuration
STRIPE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# CORS Configuration (for frontend)
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:19006
```

## 📱 Frontend Integration

The API is designed to work seamlessly with the SPark React Native frontend:

- **Base URL**: `http://localhost:8000/api` (development)
- **Authentication**: JWT tokens in Authorization header
- **Content Type**: `application/json`
- **File Uploads**: `multipart/form-data` for image uploads

### Example API Usage

```javascript
// Login
const response = await fetch('http://localhost:8000/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password'
  })
});

// Authenticated requests
const spots = await fetch('http://localhost:8000/api/spots/search', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  }
});
```

## 🔧 Development

### Running Tests
```bash
php artisan test
```

### Code Style
```bash
./vendor/bin/pint
```

### Database Seeding
```bash
php artisan db:seed
```

## 📊 Database Schema

### Key Tables
- `users` - User accounts (drivers, hosts, admins)
- `parking_spots` - Parking spot listings
- `bookings` - Booking records
- `vehicles` - User vehicles
- `payment_methods` - Stripe payment methods
- `reviews` - User and spot reviews
- `notifications` - System notifications

## 🔒 Security Features

- JWT token authentication
- Password hashing with bcrypt
- CORS protection
- Input validation and sanitization
- SQL injection prevention
- Rate limiting (configurable)

## 🚀 Deployment

### Production Setup
1. Set `APP_ENV=production` in `.env`
2. Set `APP_DEBUG=false`
3. Configure production database
4. Set up SSL certificates
5. Configure web server (Apache/Nginx)
6. Set up queue workers for background jobs

### Recommended Production Stack
- **Server**: Ubuntu 20.04+ or CentOS 8+
- **Web Server**: Nginx with PHP-FPM
- **Database**: MySQL 8.0+ or PostgreSQL 13+
- **Cache**: Redis
- **Queue**: Redis or Database
- **Storage**: AWS S3 for file uploads

## 📞 Support

For issues and questions:
- Check the Laravel documentation
- Review API endpoint documentation
- Check frontend integration examples
- Create GitHub issues for bugs

---

**SPark Backend** - Powering the future of urban parking! 🅿️
