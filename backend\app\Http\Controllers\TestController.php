<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class TestController extends Controller
{
    public function test()
    {
        return response()->json([
            'success' => true,
            'message' => 'Test endpoint working',
            'data' => [
                'timestamp' => now(),
                'server' => 'SPark Backend'
            ]
        ]);
    }

    public function testAuth()
    {
        try {
            $user = auth()->user();
            return response()->json([
                'success' => true,
                'message' => 'Auth test working',
                'user' => $user
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Auth test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
