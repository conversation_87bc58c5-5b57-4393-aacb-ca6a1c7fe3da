import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { secureStorage } from '../../utils/storage';
import { ApiResponse, User, ParkingSpot, Booking, SearchFilters } from '../../types';

// API Configuration for Expo
const getAPIBaseURL = () => {
  if (!__DEV__) {
    return 'https://api.spark-parking.com/api';
  }

  // For Expo development:
  // - Expo Go on iOS/Android: localhost works fine
  // - Physical devices: need your computer's IP
  // - Web: localhost works

  // TODO: Replace ************* with your actual computer IP for physical device testing
  return 'http://localhost:8000/api';
};

const API_BASE_URL = getAPIBaseURL();

console.log('🌐 API Base URL:', API_BASE_URL);

class APIClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      async (config) => {
        const token = await secureStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          await secureStorage.removeItem('token');
          await secureStorage.removeItem('user');
          // Navigate to login screen
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.get(url, config);
      // Handle Laravel response structure
      if (response.data.success === false) {
        throw new Error(response.data.message || 'Request failed');
      }
      return {
        success: response.data.success,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message);
    }
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.post(url, data, config);
      // Handle Laravel response structure
      if (response.data.success === false) {
        throw new Error(response.data.message || 'Request failed');
      }
      return {
        success: response.data.success,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message);
    }
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.put(url, data, config);
      // Handle Laravel response structure
      if (response.data.success === false) {
        throw new Error(response.data.message || 'Request failed');
      }
      return {
        success: response.data.success,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message);
    }
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.delete(url, config);
      // Handle Laravel response structure
      if (response.data.success === false) {
        throw new Error(response.data.message || 'Request failed');
      }
      return {
        success: response.data.success,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.message);
    }
  }
}

const apiClient = new APIClient();

// Auth API
export const authAPI = {
  login: (credentials: { email: string; password: string }) =>
    apiClient.post<{ user: User; token: string }>('/auth/login', credentials),
  
  register: (userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
    userType: 'driver' | 'host';
  }) => apiClient.post<{ user: User; token: string }>('/auth/register', {
    email: userData.email,
    password: userData.password,
    password_confirmation: userData.password,
    first_name: userData.firstName,
    last_name: userData.lastName,
    phone: userData.phone,
    user_type: userData.userType,
  }),
  
  logout: () => apiClient.post('/auth/logout'),
  
  forgotPassword: (email: string) =>
    apiClient.post('/auth/forgot-password', { email }),
  
  resetPassword: (token: string, password: string) =>
    apiClient.post('/auth/reset-password', { token, password }),
  
  updateProfile: (userData: Partial<User>) =>
    apiClient.put<User>('/auth/profile', userData),
  
  verifyEmail: (token: string) =>
    apiClient.post('/auth/verify-email', { token }),
};

// Spot API
export const spotAPI = {
  searchSpots: (filters: SearchFilters) => {
    const params = new URLSearchParams();
    if (filters.latitude) params.append('latitude', filters.latitude.toString());
    if (filters.longitude) params.append('longitude', filters.longitude.toString());
    if (filters.radius) params.append('radius', filters.radius.toString());
    if (filters.startDate) params.append('start_date', filters.startDate);
    if (filters.endDate) params.append('end_date', filters.endDate);
    if (filters.minPrice) params.append('min_price', filters.minPrice.toString());
    if (filters.maxPrice) params.append('max_price', filters.maxPrice.toString());
    if (filters.amenities) params.append('amenities', JSON.stringify(filters.amenities));
    if (filters.sortBy) params.append('sort_by', filters.sortBy);
    if (filters.sortOrder) params.append('sort_order', filters.sortOrder);

    return apiClient.get<ParkingSpot[]>(`/spots/search?${params.toString()}`);
  },
  
  getNearbySpots: (location: { latitude: number; longitude: number; radius?: number }) =>
    apiClient.get<ParkingSpot[]>(`/spots/nearby?lat=${location.latitude}&lng=${location.longitude}&radius=${location.radius || 5}`),
  
  getSpotDetails: (spotId: string) =>
    apiClient.get<ParkingSpot>(`/spots/${spotId}`),
  
  createSpot: (spotData: Omit<ParkingSpot, 'id' | 'rating' | 'reviewCount' | 'createdAt' | 'updatedAt'>) =>
    apiClient.post<ParkingSpot>('/spots', spotData),
  
  updateSpot: (spotId: string, updates: Partial<ParkingSpot>) =>
    apiClient.put<ParkingSpot>(`/spots/${spotId}`, updates),
  
  deleteSpot: (spotId: string) =>
    apiClient.delete(`/spots/${spotId}`),
  
  getHostSpots: () =>
    apiClient.get<ParkingSpot[]>('/spots/host'),
  
  toggleSpotAvailability: (spotId: string, isActive: boolean) =>
    apiClient.put<ParkingSpot>(`/spots/${spotId}/availability`, { isActive }),
  
  uploadSpotImages: (spotId: string, images: FormData) =>
    apiClient.post<{ urls: string[] }>(`/spots/${spotId}/images`, images, {
      headers: { 'Content-Type': 'multipart/form-data' },
    }),
};

// Booking API
export const bookingAPI = {
  createBooking: (bookingData: {
    spotId: string;
    vehicleId: string;
    startTime: string;
    endTime: string;
    paymentMethodId: string;
  }) => apiClient.post<Booking>('/bookings', {
    spot_id: bookingData.spotId,
    vehicle_id: bookingData.vehicleId,
    start_time: bookingData.startTime,
    end_time: bookingData.endTime,
    payment_method_id: bookingData.paymentMethodId,
  }),
  
  getUserBookings: (params: { page?: number; limit?: number; status?: string }) =>
    apiClient.get<Booking[]>('/bookings', { params }),
  
  getBookingDetails: (bookingId: string) =>
    apiClient.get<Booking>(`/bookings/${bookingId}`),
  
  getActiveBooking: () =>
    apiClient.get<Booking | null>('/bookings/active'),
  
  extendBooking: (bookingId: string, newEndTime: string) =>
    apiClient.put<Booking>(`/bookings/${bookingId}/extend`, { new_end_time: newEndTime }),
  
  cancelBooking: (bookingId: string) =>
    apiClient.put<Booking>(`/bookings/${bookingId}/cancel`),
  
  completeBooking: (bookingId: string) =>
    apiClient.put<Booking>(`/bookings/${bookingId}/complete`),
  
  getHostBookings: (params: { page?: number; limit?: number; status?: string }) =>
    apiClient.get<Booking[]>('/bookings/host', { params }),
};

// Vehicle API
export const vehicleAPI = {
  getUserVehicles: () =>
    apiClient.get<any[]>('/vehicles'),
  
  addVehicle: (vehicleData: any) =>
    apiClient.post<any>('/vehicles', vehicleData),
  
  updateVehicle: (vehicleId: string, updates: any) =>
    apiClient.put<any>(`/vehicles/${vehicleId}`, updates),
  
  deleteVehicle: (vehicleId: string) =>
    apiClient.delete(`/vehicles/${vehicleId}`),
  
  setDefaultVehicle: (vehicleId: string) =>
    apiClient.put(`/vehicles/${vehicleId}/default`),
};

// Payment API
export const paymentAPI = {
  getPaymentMethods: () =>
    apiClient.get<any[]>('/payment/methods'),
  
  addPaymentMethod: (paymentMethodData: any) =>
    apiClient.post<any>('/payment/methods', paymentMethodData),
  
  deletePaymentMethod: (paymentMethodId: string) =>
    apiClient.delete(`/payment/methods/${paymentMethodId}`),
  
  setDefaultPaymentMethod: (paymentMethodId: string) =>
    apiClient.put(`/payment/methods/${paymentMethodId}/default`),
  
  processPayment: (paymentData: any) =>
    apiClient.post<any>('/payment/process', paymentData),
  
  getPaymentHistory: (params: { page?: number; limit?: number }) =>
    apiClient.get<any[]>('/payment/history', { params }),
};

export default apiClient;
