import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
  interpolate,
  FadeInDown,
  BounceIn,
} from 'react-native-reanimated';
import { 
  SearchIcon, 
  LocationIcon, 
  CarIcon, 
  ParkingIcon 
} from './AnimatedIcon';
import { colors, typography, spacing } from '../../theme';

interface ParkingSearchAnimationProps {
  visible: boolean;
  onComplete?: () => void;
}

const ParkingSearchAnimation: React.FC<ParkingSearchAnimationProps> = ({
  visible,
  onComplete,
}) => {
  const searchPulse = useSharedValue(0);
  const radarSweep = useSharedValue(0);
  const dotsAnimation = useSharedValue(0);
  const carMove = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      // Search pulse animation
      searchPulse.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 800 }),
          withTiming(0, { duration: 800 })
        ),
        -1,
        false
      );

      // Radar sweep animation
      radarSweep.value = withRepeat(
        withTiming(1, { duration: 2000 }),
        -1,
        false
      );

      // Loading dots animation
      dotsAnimation.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 400 }),
          withTiming(0, { duration: 400 })
        ),
        -1,
        false
      );

      // Car movement animation
      carMove.value = withRepeat(
        withSequence(
          withTiming(1, { duration: 1500 }),
          withTiming(0, { duration: 1500 })
        ),
        -1,
        true
      );
    }
  }, [visible]);

  const searchPulseStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: interpolate(searchPulse.value, [0, 1], [1, 1.3]) }
    ],
    opacity: interpolate(searchPulse.value, [0, 1], [0.8, 0.3]),
  }));

  const radarStyle = useAnimatedStyle(() => ({
    transform: [
      { rotate: `${interpolate(radarSweep.value, [0, 1], [0, 360])}deg` }
    ],
  }));

  const dot1Style = useAnimatedStyle(() => ({
    opacity: interpolate(
      dotsAnimation.value,
      [0, 0.33, 0.66, 1],
      [1, 0.3, 0.3, 0.3]
    ),
    transform: [
      { scale: interpolate(
        dotsAnimation.value,
        [0, 0.33, 0.66, 1],
        [1.2, 1, 1, 1]
      )}
    ],
  }));

  const dot2Style = useAnimatedStyle(() => ({
    opacity: interpolate(
      dotsAnimation.value,
      [0, 0.33, 0.66, 1],
      [0.3, 1, 0.3, 0.3]
    ),
    transform: [
      { scale: interpolate(
        dotsAnimation.value,
        [0, 0.33, 0.66, 1],
        [1, 1.2, 1, 1]
      )}
    ],
  }));

  const dot3Style = useAnimatedStyle(() => ({
    opacity: interpolate(
      dotsAnimation.value,
      [0, 0.33, 0.66, 1],
      [0.3, 0.3, 1, 0.3]
    ),
    transform: [
      { scale: interpolate(
        dotsAnimation.value,
        [0, 0.33, 0.66, 1],
        [1, 1, 1.2, 1]
      )}
    ],
  }));

  const carMoveStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: interpolate(carMove.value, [0, 1], [-20, 20]) }
    ],
  }));

  if (!visible) return null;

  return (
    <View style={styles.overlay}>
      <Animated.View 
        style={styles.container}
        entering={BounceIn.duration(800)}
      >
        {/* Radar Animation */}
        <View style={styles.radarContainer}>
          <Animated.View style={[styles.radarSweep, radarStyle]} />
          <View style={styles.radarCenter}>
            <LocationIcon size={32} color={colors.primary[500]} />
          </View>
          
          {/* Pulse rings */}
          <Animated.View style={[styles.pulseRing, styles.ring1, searchPulseStyle]} />
          <Animated.View style={[styles.pulseRing, styles.ring2, searchPulseStyle]} />
          <Animated.View style={[styles.pulseRing, styles.ring3, searchPulseStyle]} />
        </View>

        {/* Moving Car */}
        <Animated.View style={[styles.carContainer, carMoveStyle]}>
          <CarIcon size={24} color={colors.primary[600]} />
        </Animated.View>

        {/* Search Icon */}
        <Animated.View 
          style={styles.searchIconContainer}
          entering={FadeInDown.duration(600).delay(400)}
        >
          <SearchIcon size={48} color={colors.primary[500]} />
        </Animated.View>

        {/* Title and Subtitle */}
        <Animated.Text 
          style={styles.title}
          entering={FadeInDown.duration(600).delay(600)}
        >
          Finding Perfect Spots
        </Animated.Text>
        
        <Animated.Text 
          style={styles.subtitle}
          entering={FadeInDown.duration(600).delay(800)}
        >
          Scanning nearby parking areas...
        </Animated.Text>

        {/* Animated Loading Dots */}
        <Animated.View 
          style={styles.dotsContainer}
          entering={FadeInDown.duration(600).delay(1000)}
        >
          <Animated.View style={[styles.dot, dot1Style]} />
          <Animated.View style={[styles.dot, dot2Style]} />
          <Animated.View style={[styles.dot, dot3Style]} />
        </Animated.View>

        {/* Progress Steps */}
        <Animated.View 
          style={styles.stepsContainer}
          entering={FadeInDown.duration(600).delay(1200)}
        >
          <View style={styles.step}>
            <ParkingIcon size={16} color={colors.primary[500]} />
            <Text style={styles.stepText}>Locating you</Text>
          </View>
          <View style={styles.step}>
            <SearchIcon size={16} color={colors.primary[400]} />
            <Text style={styles.stepText}>Searching spots</Text>
          </View>
          <View style={styles.step}>
            <CarIcon size={16} color={colors.gray[400]} />
            <Text style={styles.stepText}>Finding best match</Text>
          </View>
        </Animated.View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.8)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 3000,
  },
  container: {
    backgroundColor: colors.white,
    borderRadius: 24,
    padding: 40,
    alignItems: 'center',
    marginHorizontal: 40,
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 16 },
    shadowOpacity: 0.4,
    shadowRadius: 32,
    elevation: 20,
  },
  
  // Radar Animation
  radarContainer: {
    width: 120,
    height: 120,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  radarSweep: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 2,
    borderColor: 'transparent',
    borderTopColor: colors.primary[500],
  },
  radarCenter: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primary[100],
    alignItems: 'center',
    justifyContent: 'center',
  },
  pulseRing: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: colors.primary[300],
    borderRadius: 60,
  },
  ring1: {
    width: 80,
    height: 80,
  },
  ring2: {
    width: 100,
    height: 100,
  },
  ring3: {
    width: 120,
    height: 120,
  },
  
  // Moving Car
  carContainer: {
    position: 'absolute',
    top: 60,
    right: 60,
  },
  
  // Search Icon
  searchIconContainer: {
    marginBottom: 24,
  },
  
  // Text
  title: {
    fontSize: 24,
    fontWeight: '700',
    fontFamily: typography.fontFamily.brand,
    color: colors.black[900],
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  
  // Loading Dots
  dotsContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 32,
  },
  dot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary[500],
  },
  
  // Progress Steps
  stepsContainer: {
    alignItems: 'flex-start',
    gap: 12,
  },
  step: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  stepText: {
    fontSize: 14,
    color: colors.gray[600],
    fontWeight: '500',
  },
});

export default ParkingSearchAnimation;
