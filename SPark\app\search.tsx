import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  FlatList,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import Animated, {
  FadeInDown,
  FadeInUp,
  SlideInRight,
  Layout,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';
import { router } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as Location from 'expo-location';
import { useAppDispatch, useAppSelector } from '../src/store';
import { searchParkingSpots, setFilters, clearSearchResults } from '../src/store/slices/spotSlice';
import MobileInput from '../src/components/ui/MobileInput';
import {
  SearchIcon,
  FilterIcon,
  LocationIcon,
  StarIcon,
  MapIcon,
  RefreshIcon,
  BackIcon
} from '../src/components/ui/AnimatedIcon';
import { colors, spacing, typography, borderRadius } from '../src/theme';
import { SearchFilters, ParkingSpot } from '../src/types';

export default function SearchScreen() {
  const dispatch = useAppDispatch();
  const { searchResults, filters, isLoading } = useAppSelector((state) => state.spot);
  
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [localFilters, setLocalFilters] = useState<SearchFilters>({
    priceRange: { min: 0, max: 50 },
    amenities: [],
    sortBy: 'distance',
    sortOrder: 'asc',
  });

  useEffect(() => {
    // Clear search results when component mounts
    dispatch(clearSearchResults());
  }, [dispatch]);

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      Alert.alert('Search Required', 'Please enter a location to search for parking.');
      return;
    }

    try {
      // Get current location for search
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Location permission is required to search.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      
      const searchFilters: SearchFilters = {
        ...localFilters,
        location: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          radius: 10,
        },
      };

      dispatch(setFilters(searchFilters));
      await dispatch(searchParkingSpots(searchFilters)).unwrap();
    } catch (error) {
      Alert.alert('Search Error', 'Unable to search for parking spots. Please try again.');
    }
  };

  const handleSpotPress = (spotId: string) => {
    router.push(`/spot/details?spotId=${spotId}`);
  };

  const toggleAmenity = (amenity: string) => {
    setLocalFilters(prev => ({
      ...prev,
      amenities: prev.amenities?.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...(prev.amenities || []), amenity],
    }));
  };

  const renderSearchHeader = () => (
    <Animated.View
      style={styles.searchCard}
      entering={FadeInDown.duration(800)}
    >
      <Text style={styles.sectionTitle}>Search Parking Spots</Text>

      <View style={styles.searchContainer}>
        <MobileInput
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Enter destination or address"
          style={styles.searchInput}
        />

        <TouchableOpacity
          style={styles.searchButton}
          onPress={handleSearch}
        >
          <Text style={styles.searchButtonText}>Search</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        style={styles.filtersButton}
        onPress={() => setShowFilters(!showFilters)}
      >
        <Text style={styles.filtersButtonText}>
          {showFilters ? 'Hide Filters' : 'Show Filters'}
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );

  const renderFilters = () => {
    if (!showFilters) return null;

    const amenities = [
      'Covered',
      'Security Camera',
      'EV Charging',
      'Wheelchair Accessible',
      '24/7 Access',
      'Valet Service',
    ];

    return (
      <Animated.View
        style={styles.filtersCard}
        entering={FadeInUp.duration(800).delay(200)}
      >
        <Text style={styles.filterTitle}>Filters</Text>
        
        <View style={styles.filterSection}>
          <Text style={styles.filterLabel}>Price Range (per hour)</Text>
          <View style={styles.priceRangeContainer}>
            <Input
              value={localFilters.priceRange?.min.toString() || '0'}
              onChangeText={(value) => setLocalFilters(prev => ({
                ...prev,
                priceRange: { ...prev.priceRange!, min: parseInt(value) || 0 },
              }))}
              placeholder="Min"
              keyboardType="numeric"
              containerStyle={styles.priceInput}
            />
            <Text style={styles.priceRangeSeparator}>to</Text>
            <Input
              value={localFilters.priceRange?.max.toString() || '50'}
              onChangeText={(value) => setLocalFilters(prev => ({
                ...prev,
                priceRange: { ...prev.priceRange!, max: parseInt(value) || 50 },
              }))}
              placeholder="Max"
              keyboardType="numeric"
              containerStyle={styles.priceInput}
            />
          </View>
        </View>

        <View style={styles.filterSection}>
          <Text style={styles.filterLabel}>Amenities</Text>
          <View style={styles.amenitiesContainer}>
            {amenities.map((amenity) => (
              <TouchableOpacity
                key={amenity}
                style={[
                  styles.amenityChip,
                  localFilters.amenities?.includes(amenity) && styles.amenityChipSelected,
                ]}
                onPress={() => toggleAmenity(amenity)}
              >
                <Text
                  style={[
                    styles.amenityChipText,
                    localFilters.amenities?.includes(amenity) && styles.amenityChipTextSelected,
                  ]}
                >
                  {amenity}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.filterSection}>
          <Text style={styles.filterLabel}>Sort By</Text>
          <View style={styles.sortContainer}>
            {[
              { key: 'distance', label: 'Distance' },
              { key: 'price', label: 'Price' },
              { key: 'rating', label: 'Rating' },
            ].map((option) => (
              <TouchableOpacity
                key={option.key}
                style={[
                  styles.sortOption,
                  localFilters.sortBy === option.key && styles.sortOptionSelected,
                ]}
                onPress={() => setLocalFilters(prev => ({ ...prev, sortBy: option.key as any }))}
              >
                <Text
                  style={[
                    styles.sortOptionText,
                    localFilters.sortBy === option.key && styles.sortOptionTextSelected,
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </Animated.View>
    );
  };

  const renderSpotItem = ({ item }: { item: ParkingSpot }) => (
    <TouchableOpacity
      style={styles.spotItem}
      onPress={() => handleSpotPress(item.id)}
    >
      <View style={styles.spotInfo}>
        <Text style={styles.spotTitle}>{item.title}</Text>
        <Text style={styles.spotAddress}>{item.address}</Text>
        <Text style={styles.spotPrice}>${item.hourlyRate}/hour</Text>
        
        {item.amenities.length > 0 && (
          <View style={styles.spotAmenities}>
            {item.amenities.slice(0, 3).map((amenity, index) => (
              <Text key={index} style={styles.spotAmenity}>
                {amenity}
              </Text>
            ))}
            {item.amenities.length > 3 && (
              <Text style={styles.spotAmenity}>+{item.amenities.length - 3} more</Text>
            )}
          </View>
        )}
      </View>
      
      <View style={styles.spotMeta}>
        <Text style={styles.spotRating}>★ {item.rating.toFixed(1)}</Text>
        <Text style={styles.spotDistance}>0.5 mi</Text>
        <Button
          title="Book Now"
          onPress={() => handleSpotPress(item.id)}
          size="sm"
          style={styles.bookButton}
        />
      </View>
    </TouchableOpacity>
  );

  const renderSearchResults = () => {
    if (searchResults.length === 0 && !isLoading) {
      return (
        <Animated.View
          style={styles.noResultsCard}
          entering={FadeInDown.duration(800)}
        >
          <SearchIcon size={48} color={colors.gray[400]} />
          <Text style={styles.noResultsTitle}>No parking spots found</Text>
          <Text style={styles.noResultsText}>
            Try adjusting your search criteria or location.
          </Text>
        </Animated.View>
      );
    }

    return (
      <FlatList
        data={searchResults}
        renderItem={renderSpotItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.resultsList}
      />
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderSearchHeader()}
        {renderFilters()}
        {searchResults.length > 0 && (
          <Text style={styles.resultsCount}>
            {searchResults.length} parking spot{searchResults.length !== 1 ? 's' : ''} found
          </Text>
        )}
      </ScrollView>
      {renderSearchResults()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollView: {
    flex: 1,
    padding: spacing[4],
  },
  searchCard: {
    marginBottom: spacing[4],
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[3],
  },
  searchContainer: {
    flexDirection: 'row',
    gap: spacing[2],
    marginBottom: spacing[3],
  },
  searchInput: {
    flex: 1,
    marginBottom: 0,
  },
  searchButton: {
    paddingHorizontal: spacing[4],
  },
  filtersButton: {
    marginTop: spacing[2],
  },
  filtersCard: {
    marginBottom: spacing[4],
  },
  filterTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[4],
  },
  filterSection: {
    marginBottom: spacing[4],
  },
  filterLabel: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.gray[700],
    marginBottom: spacing[2],
  },
  priceRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[2],
  },
  priceInput: {
    flex: 1,
    marginBottom: 0,
  },
  priceRangeSeparator: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing[2],
  },
  amenityChip: {
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    borderRadius: borderRadius.full,
    borderWidth: 1,
    borderColor: colors.gray[300],
    backgroundColor: colors.white,
  },
  amenityChipSelected: {
    borderColor: colors.primary[500],
    backgroundColor: colors.primary[50],
  },
  amenityChipText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[700],
  },
  amenityChipTextSelected: {
    color: colors.primary[600],
  },
  sortContainer: {
    flexDirection: 'row',
    gap: spacing[2],
  },
  sortOption: {
    flex: 1,
    paddingVertical: spacing[2],
    paddingHorizontal: spacing[3],
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.gray[300],
    backgroundColor: colors.white,
    alignItems: 'center',
  },
  sortOptionSelected: {
    borderColor: colors.primary[500],
    backgroundColor: colors.primary[50],
  },
  sortOptionText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[700],
  },
  sortOptionTextSelected: {
    color: colors.primary[600],
  },
  resultsCount: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  resultsList: {
    padding: spacing[4],
  },
  spotItem: {
    flexDirection: 'row',
    padding: spacing[4],
    backgroundColor: colors.white,
    borderRadius: borderRadius.md,
    marginBottom: spacing[3],
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  spotInfo: {
    flex: 1,
    marginRight: spacing[3],
  },
  spotTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[1],
  },
  spotAddress: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    marginBottom: spacing[1],
  },
  spotPrice: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
    color: colors.primary[600],
    marginBottom: spacing[2],
  },
  spotAmenities: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing[1],
  },
  spotAmenity: {
    fontSize: typography.fontSize.xs,
    color: colors.gray[500],
    backgroundColor: colors.gray[100],
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: borderRadius.sm,
  },
  spotMeta: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  spotRating: {
    fontSize: typography.fontSize.sm,
    color: colors.warning[600],
    marginBottom: spacing[1],
  },
  spotDistance: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
    marginBottom: spacing[2],
  },
  bookButton: {
    paddingHorizontal: spacing[3],
  },
  noResultsCard: {
    margin: spacing[4],
    alignItems: 'center',
    paddingVertical: spacing[8],
    backgroundColor: colors.white,
    borderRadius: 12,
    margin: 20,
    padding: 24,
  },
  noResultsTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[2],
    marginTop: spacing[4],
  },
  noResultsText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
  },
  searchButton: {
    backgroundColor: colors.primary[500],
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    marginLeft: 12,
  },
  searchButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.black[900],
  },
  filtersButton: {
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 12,
  },
  filtersButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[700],
  },
});
