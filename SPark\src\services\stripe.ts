import { initStripe, StripeProvider } from '@stripe/stripe-react-native';
import { Alert } from 'react-native';

// Stripe configuration
const STRIPE_PUBLISHABLE_KEY = 'pk_test_51234567890abcdef'; // Replace with your actual key
const STRIPE_MERCHANT_ID = 'merchant.com.spark.app';

// Initialize Stripe
export const initializeStripe = async () => {
  try {
    await initStripe({
      publishableKey: STRIPE_PUBLISHABLE_KEY,
      merchantIdentifier: STRIPE_MERCHANT_ID,
      urlScheme: 'spark',
    });
    console.log('Stripe initialized successfully');
  } catch (error) {
    console.error('Failed to initialize Stripe:', error);
  }
};

// Payment service class
export class PaymentService {
  private static instance: PaymentService;
  
  public static getInstance(): PaymentService {
    if (!PaymentService.instance) {
      PaymentService.instance = new PaymentService();
    }
    return PaymentService.instance;
  }

  // Create payment intent on backend
  async createPaymentIntent(amount: number, currency: string = 'usd'): Promise<string> {
    try {
      // In a real app, this would call your backend API
      const response = await fetch('https://your-backend.com/api/payment/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: amount * 100, // Convert to cents
          currency,
        }),
      });
      
      const data = await response.json();
      return data.client_secret;
    } catch (error) {
      console.error('Failed to create payment intent:', error);
      throw new Error('Failed to create payment intent');
    }
  }

  // Mock payment intent for demo
  async createMockPaymentIntent(amount: number): Promise<string> {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Return mock client secret
    return `pi_mock_${Date.now()}_secret_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Process payment with Stripe
  async processPayment(
    clientSecret: string,
    paymentMethodId?: string,
    billingDetails?: any
  ): Promise<{ success: boolean; paymentIntent?: any; error?: string }> {
    try {
      // This would use the actual Stripe SDK
      // For demo purposes, we'll simulate success
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockPaymentIntent = {
        id: `pi_${Date.now()}`,
        status: 'succeeded',
        amount: 1200,
        currency: 'usd',
        created: Date.now(),
      };

      return {
        success: true,
        paymentIntent: mockPaymentIntent,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Payment failed',
      };
    }
  }

  // Add payment method
  async addPaymentMethod(paymentMethodData: any): Promise<{ success: boolean; paymentMethod?: any; error?: string }> {
    try {
      // Simulate adding payment method
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockPaymentMethod = {
        id: `pm_${Date.now()}`,
        type: 'card',
        card: {
          brand: 'visa',
          last4: '4242',
          exp_month: 12,
          exp_year: 2025,
        },
        created: Date.now(),
      };

      return {
        success: true,
        paymentMethod: mockPaymentMethod,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to add payment method',
      };
    }
  }

  // Get saved payment methods
  async getPaymentMethods(): Promise<any[]> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Return mock payment methods
      return [
        {
          id: 'pm_1234567890',
          type: 'card',
          card: {
            brand: 'visa',
            last4: '4242',
            exp_month: 12,
            exp_year: 2025,
          },
          isDefault: true,
        },
        {
          id: 'pm_0987654321',
          type: 'card',
          card: {
            brand: 'mastercard',
            last4: '5555',
            exp_month: 8,
            exp_year: 2026,
          },
          isDefault: false,
        },
      ];
    } catch (error) {
      console.error('Failed to get payment methods:', error);
      return [];
    }
  }

  // Delete payment method
  async deletePaymentMethod(paymentMethodId: string): Promise<boolean> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      return true;
    } catch (error) {
      console.error('Failed to delete payment method:', error);
      return false;
    }
  }

  // Set default payment method
  async setDefaultPaymentMethod(paymentMethodId: string): Promise<boolean> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      return true;
    } catch (error) {
      console.error('Failed to set default payment method:', error);
      return false;
    }
  }

  // Calculate fees and total
  calculateBookingTotal(baseAmount: number): {
    subtotal: number;
    serviceFee: number;
    processingFee: number;
    total: number;
  } {
    const subtotal = baseAmount;
    const serviceFee = Math.round(baseAmount * 0.1); // 10% service fee
    const processingFee = Math.round(baseAmount * 0.029 + 30); // 2.9% + $0.30 processing fee
    const total = subtotal + serviceFee + processingFee;

    return {
      subtotal,
      serviceFee,
      processingFee,
      total,
    };
  }

  // Format currency
  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount / 100);
  }
}

export default PaymentService.getInstance();
