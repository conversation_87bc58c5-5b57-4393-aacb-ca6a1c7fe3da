// React Native compatible EventEmitter
class SimpleEventEmitter {
  private events: { [key: string]: Function[] } = {};

  on(event: string, listener: Function) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
  }

  off(event: string, listener: Function) {
    if (!this.events[event]) return;
    this.events[event] = this.events[event].filter(l => l !== listener);
  }

  emit(event: string, ...args: any[]) {
    if (!this.events[event]) return;
    this.events[event].forEach(listener => listener(...args));
  }

  removeAllListeners(event?: string) {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
  }
}

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
}

export interface SpotUpdate {
  spotId: string;
  isAvailable: boolean;
  currentPrice: number;
  lastUpdated: string;
}

export interface BookingUpdate {
  bookingId: string;
  status: 'confirmed' | 'active' | 'completed' | 'cancelled';
  spotId: string;
  userId: string;
  timestamp: string;
}

export class WebSocketService extends SimpleEventEmitter {
  private static instance: WebSocketService;
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;
  private isConnecting = false;
  private url: string;
  private authToken: string | null = null;

  private constructor() {
    super();
    this.url = __DEV__ 
      ? 'ws://localhost:3001/ws' 
      : 'wss://api.spark.com/ws';
  }

  public static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  // Connect to WebSocket server
  public connect(authToken?: string): void {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.isConnecting = true;
    this.authToken = authToken || this.authToken;

    try {
      const wsUrl = this.authToken 
        ? `${this.url}?token=${this.authToken}`
        : this.url;

      this.ws = new WebSocket(wsUrl);
      this.setupEventListeners();
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.isConnecting = false;
      this.scheduleReconnect();
    }
  }

  // Disconnect from WebSocket server
  public disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.reconnectAttempts = 0;
    this.isConnecting = false;
  }

  // Send message to server
  public send(message: WebSocketMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
    }
  }

  // Subscribe to spot updates
  public subscribeToSpotUpdates(spotIds: string[]): void {
    this.send({
      type: 'subscribe_spots',
      data: { spotIds },
      timestamp: Date.now(),
    });
  }

  // Unsubscribe from spot updates
  public unsubscribeFromSpotUpdates(spotIds: string[]): void {
    this.send({
      type: 'unsubscribe_spots',
      data: { spotIds },
      timestamp: Date.now(),
    });
  }

  // Subscribe to booking updates
  public subscribeToBookingUpdates(userId: string): void {
    this.send({
      type: 'subscribe_bookings',
      data: { userId },
      timestamp: Date.now(),
    });
  }

  // Subscribe to host updates
  public subscribeToHostUpdates(hostId: string): void {
    this.send({
      type: 'subscribe_host',
      data: { hostId },
      timestamp: Date.now(),
    });
  }

  // Update spot availability (for hosts)
  public updateSpotAvailability(spotId: string, isAvailable: boolean): void {
    this.send({
      type: 'update_spot_availability',
      data: { spotId, isAvailable },
      timestamp: Date.now(),
    });
  }

  // Send location update for real-time tracking
  public updateLocation(latitude: number, longitude: number): void {
    this.send({
      type: 'location_update',
      data: { latitude, longitude },
      timestamp: Date.now(),
    });
  }

  // Get connection status
  public getConnectionStatus(): string {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }

  // Setup WebSocket event listeners
  private setupEventListeners(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.emit('connected');
    };

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      this.isConnecting = false;
      this.emit('disconnected', { code: event.code, reason: event.reason });
      
      if (event.code !== 1000) { // Not a normal closure
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.isConnecting = false;
      this.emit('error', error);
    };
  }

  // Handle incoming messages
  private handleMessage(message: WebSocketMessage): void {
    switch (message.type) {
      case 'spot_update':
        this.emit('spotUpdate', message.data as SpotUpdate);
        break;
      
      case 'booking_update':
        this.emit('bookingUpdate', message.data as BookingUpdate);
        break;
      
      case 'new_booking':
        this.emit('newBooking', message.data);
        break;
      
      case 'booking_cancelled':
        this.emit('bookingCancelled', message.data);
        break;
      
      case 'price_update':
        this.emit('priceUpdate', message.data);
        break;
      
      case 'notification':
        this.emit('notification', message.data);
        break;
      
      case 'ping':
        this.send({ type: 'pong', data: {}, timestamp: Date.now() });
        break;
      
      default:
        console.log('Unknown message type:', message.type);
    }
  }

  // Schedule reconnection attempt
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnection attempts reached');
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (this.getConnectionStatus() === 'disconnected') {
        console.log(`Reconnection attempt ${this.reconnectAttempts}`);
        this.connect();
      }
    }, delay);
  }
}

// Create and export singleton instance
export default WebSocketService.getInstance();

// React hook for using WebSocket in components
export const useWebSocket = () => {
  const ws = WebSocketService.getInstance();
  
  return {
    connect: ws.connect.bind(ws),
    disconnect: ws.disconnect.bind(ws),
    send: ws.send.bind(ws),
    subscribeToSpotUpdates: ws.subscribeToSpotUpdates.bind(ws),
    unsubscribeFromSpotUpdates: ws.unsubscribeFromSpotUpdates.bind(ws),
    subscribeToBookingUpdates: ws.subscribeToBookingUpdates.bind(ws),
    subscribeToHostUpdates: ws.subscribeToHostUpdates.bind(ws),
    updateSpotAvailability: ws.updateSpotAvailability.bind(ws),
    updateLocation: ws.updateLocation.bind(ws),
    getConnectionStatus: ws.getConnectionStatus.bind(ws),
    on: ws.on.bind(ws),
    off: ws.off.bind(ws),
    emit: ws.emit.bind(ws),
  };
};
