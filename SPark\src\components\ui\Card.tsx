import React from 'react';
import {
  View,
  TouchableOpacity,
  ViewStyle,
  StyleSheet,
} from 'react-native';
import { colors, spacing, borderRadius, shadows } from '../../theme';

interface CardProps {
  children: React.ReactNode;
  onPress?: () => void;
  style?: ViewStyle;
  padding?: keyof typeof spacing;
  margin?: keyof typeof spacing;
  shadow?: keyof typeof shadows;
  borderRadius?: keyof typeof borderRadius;
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  disabled?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  onPress,
  style,
  padding = 4,
  margin = 0,
  shadow = 'base',
  borderRadius: borderRadiusProp = 'md',
  backgroundColor = colors.white,
  borderColor,
  borderWidth,
  disabled = false,
}) => {
  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor,
      borderRadius: borderRadius[borderRadiusProp],
      padding: spacing[padding],
      margin: spacing[margin],
      ...shadows[shadow],
    };

    if (borderColor && borderWidth) {
      baseStyle.borderColor = borderColor;
      baseStyle.borderWidth = borderWidth;
    }

    if (disabled) {
      baseStyle.opacity = 0.6;
    }

    return baseStyle;
  };

  if (onPress) {
    return (
      <TouchableOpacity
        style={[getCardStyle(), style]}
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.8}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={[getCardStyle(), style]}>
      {children}
    </View>
  );
};

export default Card;
