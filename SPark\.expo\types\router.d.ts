/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/bookings`; params?: Router.UnknownInputParams; } | { pathname: `/find-parking`; params?: Router.UnknownInputParams; } | { pathname: `/integration-test`; params?: Router.UnknownInputParams; } | { pathname: `/search`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/auth/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/register`; params?: Router.UnknownInputParams; } | { pathname: `/booking/details`; params?: Router.UnknownInputParams; } | { pathname: `/booking/payment`; params?: Router.UnknownInputParams; } | { pathname: `/host/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/profile/edit`; params?: Router.UnknownInputParams; } | { pathname: `/profile/vehicles`; params?: Router.UnknownInputParams; } | { pathname: `/spot/details`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/bookings`; params?: Router.UnknownOutputParams; } | { pathname: `/find-parking`; params?: Router.UnknownOutputParams; } | { pathname: `/integration-test`; params?: Router.UnknownOutputParams; } | { pathname: `/search`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/forgot-password`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/login`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/register`; params?: Router.UnknownOutputParams; } | { pathname: `/booking/details`; params?: Router.UnknownOutputParams; } | { pathname: `/booking/payment`; params?: Router.UnknownOutputParams; } | { pathname: `/host/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/profile/edit`; params?: Router.UnknownOutputParams; } | { pathname: `/profile/vehicles`; params?: Router.UnknownOutputParams; } | { pathname: `/spot/details`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/bookings${`?${string}` | `#${string}` | ''}` | `/find-parking${`?${string}` | `#${string}` | ''}` | `/integration-test${`?${string}` | `#${string}` | ''}` | `/search${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/explore${`?${string}` | `#${string}` | ''}` | `/explore${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `/auth/forgot-password${`?${string}` | `#${string}` | ''}` | `/auth/login${`?${string}` | `#${string}` | ''}` | `/auth/register${`?${string}` | `#${string}` | ''}` | `/booking/details${`?${string}` | `#${string}` | ''}` | `/booking/payment${`?${string}` | `#${string}` | ''}` | `/host/dashboard${`?${string}` | `#${string}` | ''}` | `/profile/edit${`?${string}` | `#${string}` | ''}` | `/profile/vehicles${`?${string}` | `#${string}` | ''}` | `/spot/details${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/bookings`; params?: Router.UnknownInputParams; } | { pathname: `/find-parking`; params?: Router.UnknownInputParams; } | { pathname: `/integration-test`; params?: Router.UnknownInputParams; } | { pathname: `/search`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/auth/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `/auth/login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/register`; params?: Router.UnknownInputParams; } | { pathname: `/booking/details`; params?: Router.UnknownInputParams; } | { pathname: `/booking/payment`; params?: Router.UnknownInputParams; } | { pathname: `/host/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/profile/edit`; params?: Router.UnknownInputParams; } | { pathname: `/profile/vehicles`; params?: Router.UnknownInputParams; } | { pathname: `/spot/details`; params?: Router.UnknownInputParams; } | `/+not-found` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
