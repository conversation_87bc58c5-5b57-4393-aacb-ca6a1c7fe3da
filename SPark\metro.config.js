const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add web-specific resolver to exclude native-only modules
config.resolver.platforms = ['ios', 'android', 'native', 'web'];
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Initialize alias object
config.resolver.alias = {
  ...(config.resolver.alias || {}),
};

// Platform-specific module resolution for web and fallbacks
if (process.env.EXPO_PLATFORM === 'web') {
  config.resolver.alias = {
    ...config.resolver.alias,
    'react-native-maps': require.resolve('./src/components/WebMapFallback.tsx'),
    '@stripe/stripe-react-native': require.resolve('./src/components/WebStripeFallback.js'),
  };
} else {
  // For native platforms, provide fallback if maps module fails
  config.resolver.alias = {
    ...config.resolver.alias,
    // Keep native maps as primary, fallback handled in component
  };
}

module.exports = config;
