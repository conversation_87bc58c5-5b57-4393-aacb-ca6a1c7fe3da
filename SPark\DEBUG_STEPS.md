# Debug Steps for Parking Feature

## Current Status
✅ API is working and returning data (7 parking spots found in San Francisco)
✅ Data processing is working correctly
✅ Bottom sheet component is created
❓ Need to verify UI integration and state management

## Step-by-Step Debugging

### 1. Start the App
```bash
cd SPark
npm start
# Press 'i' for iOS or 'a' for Android
```

### 2. Open Developer Console
- **iOS Simulator**: Cmd+D → Debug → Open Chrome DevTools
- **Android**: Cmd+M → Debug → Open Chrome DevTools
- **Expo Go**: Shake device → Debug Remote JS

### 3. Navigate to Find Parking Screen
- Open the app
- Go to Search tab (bottom navigation)
- Tap "Find Nearby (OSM)" button

### 4. Test Bottom Sheet with Mock Data
- Look for the "TEST" button (orange button on bottom right)
- Tap the TEST button
- **Expected**: Bottom sheet should slide up with 2 mock parking spots

### 5. Check Console Logs
Look for these log messages:
```
🧪 Testing bottom sheet with mock data
🔄 Setting mock parking spots...
🔄 Setting showBottomSheet to true...
✅ Mock data set, bottom sheet should show
🏠 SimpleParkingBottomSheet render
👁️ isVisible: true
🅿️ spots: 2
⏳ loading: false
🎨 renderContent called
✅ Rendering spots list with 2 spots
🎨 Rendering SimpleParkingBottomSheet
👁️ Modal visible: true
```

### 6. Test Real API Call
- Allow location permissions when prompted
- Tap the main "Find Nearby Parking" button (center circle)
- **Expected**: Loading animation, then bottom sheet with real data

### 7. Check API Logs
Look for these log messages:
```
🔍 Starting parking search...
📍 User location: [lat] [lon]
📏 Search radius: 1000
🌐 Calling OpenStreetMap service...
🚀 OpenStreetMapService.findNearbyParking called
🌐 Fetching from OpenStreetMap...
📡 Making API request...
✅ API response received
📊 Raw OSM data received: X elements
🔄 Processing OSM data...
✅ Processed spots: X
🎯 Final sorted spots: X
✅ API Response received
📊 Search result: [object]
🅿️ Number of spots found: X
📱 Showing bottom sheet...
```

## Troubleshooting

### Issue 1: Bottom Sheet Not Appearing
**Symptoms**: TEST button works but no bottom sheet visible

**Check**:
1. Console shows `🏠 SimpleParkingBottomSheet render`?
2. Console shows `👁️ Modal visible: true`?
3. Any React Native errors in console?

**Solutions**:
- Check if Modal is being blocked by other UI elements
- Verify theme colors are properly imported
- Check if there are any style conflicts

### Issue 2: API Call Fails
**Symptoms**: Loading animation but no data

**Check**:
1. Internet connection working?
2. Console shows `📡 Making API request...`?
3. Any network errors in console?

**Solutions**:
- Try different location (San Francisco: 37.7749, -122.4194)
- Check if Overpass API is accessible: https://overpass-api.de/api/interpreter
- Verify location permissions are granted

### Issue 3: No Parking Data
**Symptoms**: API succeeds but 0 spots found

**Check**:
1. Console shows `📊 Raw OSM data received: X elements`?
2. Console shows `✅ Processed spots: X`?
3. Are you in an area with parking data?

**Solutions**:
- Test in a major city (San Francisco, New York, London)
- Increase search radius
- Check OpenStreetMap data for your area

### Issue 4: State Not Updating
**Symptoms**: Data received but UI not updating

**Check**:
1. Console shows state change logs?
2. `🔄 State changed - parkingSpots: X`?
3. `🔄 State changed - showBottomSheet: true`?

**Solutions**:
- Check if useState hooks are working
- Verify component re-rendering
- Check for any blocking useEffect dependencies

## Manual Testing Checklist

### Bottom Sheet UI
- [ ] Bottom sheet slides up from bottom
- [ ] Header shows correct parking count
- [ ] Drag handle is visible
- [ ] Expand/collapse button works
- [ ] Close button works
- [ ] Tap outside overlay closes sheet

### Parking Spot Cards
- [ ] Each spot shows name, address, distance
- [ ] Tap spot to expand details
- [ ] Details show type, fee status, amenities
- [ ] "Get Directions" button works
- [ ] Maps selection dialog appears

### Map Integration
- [ ] Parking markers appear on map
- [ ] Markers are color-coded by type
- [ ] Tap marker selects spot
- [ ] Map zooms to fit all results

### Error Handling
- [ ] No location permission handled gracefully
- [ ] No internet connection shows cached data
- [ ] No results shows appropriate message
- [ ] API errors don't crash app

## Expected Results by Location

### San Francisco (37.7749, -122.4194)
- **Expected**: 5-10 parking spots
- **Types**: Underground, surface, street parking
- **Distance**: 200-800 meters

### New York City (40.7128, -74.0060)
- **Expected**: 10+ parking spots
- **Types**: Mostly street parking
- **Distance**: 100-500 meters

### Rural/Suburban Areas
- **Expected**: 0-2 parking spots
- **Message**: "No parking found" with suggestion to expand radius

## Debug Commands

### Clear Cache
```javascript
// In console
await require('./src/utils/storage').parkingCache.clearParkingCache();
```

### Force API Call
```javascript
// In console
const service = require('./src/services/openStreetMapService').openStreetMapService;
const result = await service.findNearbyParking({
  coords: { latitude: 37.7749, longitude: -122.4194 }
}, 1000);
console.log(result);
```

### Test Bottom Sheet Directly
```javascript
// In console (if component is accessible)
setParkingSpots([{
  id: 'debug_1',
  name: 'Debug Parking',
  address: 'Debug Address',
  latitude: 37.7749,
  longitude: -122.4194,
  distance: 100,
  type: 'surface',
  fee: false,
  access: 'public',
  amenities: [],
  rating: 4.5,
  isAvailable: true
}]);
setShowBottomSheet(true);
```

## Success Criteria

The feature is working correctly when:
- ✅ TEST button shows bottom sheet with mock data
- ✅ Real API call returns parking data
- ✅ Bottom sheet displays parking spots correctly
- ✅ Tap interactions work (expand, directions, close)
- ✅ Map markers appear and are interactive
- ✅ Error states are handled gracefully
- ✅ Offline caching works
- ✅ Performance is smooth (no lag or freezing)
