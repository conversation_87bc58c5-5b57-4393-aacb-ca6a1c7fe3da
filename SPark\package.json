{"name": "spark", "main": "expo-router/entry", "version": "1.0.0", "description": "SPark - Real-time parking solution for metropolitan areas", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "test": "jest", "test:watch": "jest --watch", "type-check": "tsc --noEmit"}, "dependencies": {"@expo-google-fonts/inter": "^0.4.1", "@expo-google-fonts/passion-one": "^0.4.0", "@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/geolocation": "^3.4.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@reduxjs/toolkit": "^2.8.2", "@stripe/stripe-react-native": "^0.48.0", "axios": "^1.10.0", "expo": "~53.0.12", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-device": "^7.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.0", "expo-linking": "~7.1.5", "expo-location": "~18.1.5", "expo-notifications": "^0.31.3", "expo-router": "~5.1.0", "expo-secure-store": "^14.2.3", "expo-splash-screen": "^0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-web-browser": "~14.2.0", "lottie-react-native": "^7.2.2", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.58.1", "react-native": "0.79.4", "react-native-date-picker": "^5.0.13", "react-native-device-info": "^14.0.4", "react-native-elements": "^3.4.3", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "~2.24.0", "react-native-image-picker": "^8.2.1", "react-native-keychain": "^10.0.0", "react-native-maps": "1.20.1", "react-native-modal": "^14.0.0-rc.1", "react-native-paper": "^5.14.5", "react-native-permissions": "^5.4.1", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-super-grid": "^6.0.1", "react-native-svg": "^15.12.0", "react-native-toast-message": "^2.3.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-redux": "^9.2.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}