import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
} from 'react-native';

const SimpleAPITest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, message]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testAPIConnection = async () => {
    setIsRunning(true);
    clearResults();

    addResult('🚀 Starting API Connection Test...');

    // List of URLs to try
    const testURLs = [
      'http://localhost:8000/api/test',      // Web/iOS Simulator
      'http://127.0.0.1:8000/api/test',      // Alternative localhost
      'http://********:8000/api/test',       // Android Emulator
      'http://*************:8000/api/test',  // Replace with your actual IP
    ];

    try {
      // Test 1: Try multiple connection URLs
      addResult('\n1. Testing basic connection...');
      addResult('   Trying different URLs...');

      let successfulURL = null;
      let lastError = null;

      for (const url of testURLs) {
        try {
          addResult(`   • Trying: ${url}`);

          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
            },
            timeout: 5000, // 5 second timeout
          });

          if (response.ok) {
            const data = await response.json();
            addResult('✅ Backend connection successful!');
            addResult(`   Server: ${data.data?.server || 'SPark Backend'}`);
            successfulURL = url;
            break;
          } else {
            addResult(`❌ Backend connection failed: HTTP ${response.status}`);
          }
        } catch (error: any) {
          addResult(`❌ Connection failed: ${error.message}`);
          lastError = error;
        }
      }

      if (!successfulURL) {
        addResult('❌ All connection attempts failed');
        throw lastError || new Error('No successful connection');
      }
      
      // Test 2: Login test
      addResult('\n2. Testing login...');
      
      const loginResponse = await fetch('http://localhost:8000/api/auth/login', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password',
        }),
      });
      
      if (loginResponse.ok) {
        const loginData = await loginResponse.json();
        if (loginData.success) {
          addResult('✅ Login successful!');
          addResult(`   User: ${loginData.data.user.first_name} ${loginData.data.user.last_name}`);
          addResult(`   Token: ${loginData.data.token.substring(0, 20)}...`);
        } else {
          addResult(`❌ Login failed: ${loginData.message}`);
        }
      } else {
        addResult(`❌ Login request failed: HTTP ${loginResponse.status}`);
      }
      
      // Test 3: Spot search
      addResult('\n3. Testing spot search...');
      
      const searchResponse = await fetch('http://localhost:8000/api/spots/search?latitude=40.7128&longitude=-74.0060&radius=10', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });
      
      if (searchResponse.ok) {
        const searchData = await searchResponse.json();
        if (searchData.success) {
          addResult('✅ Spot search successful!');
          addResult(`   Found: ${searchData.data.length} parking spots`);
        } else {
          addResult(`❌ Spot search failed: ${searchData.message}`);
        }
      } else {
        addResult(`❌ Spot search request failed: HTTP ${searchResponse.status}`);
      }
      
      addResult('\n🎉 API Integration Test Complete!');
      
    } catch (error: any) {
      addResult(`\n❌ Test failed with error: ${error.message}`);
      addResult('\n🔧 Troubleshooting:');
      addResult('1. Make sure Laravel backend is running (php artisan serve)');
      addResult('2. Check that your device can access localhost:8000');
      addResult('3. For physical devices, use your computer\'s IP address');
    } finally {
      setIsRunning(false);
    }
  };

  const showNetworkInfo = () => {
    Alert.alert(
      'Network Configuration',
      'Backend URL: http://localhost:8000/api\n\n' +
      'For physical devices:\n' +
      '• Use your computer\'s IP address\n' +
      '• Example: http://*************:8000/api\n\n' +
      'Make sure:\n' +
      '• Laravel server is running\n' +
      '• Firewall allows connections\n' +
      '• Device is on same network',
      [{ text: 'OK' }]
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>API Connection Test</Text>
      <Text style={styles.subtitle}>
        Test the connection between your app and Laravel backend
      </Text>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, isRunning && styles.buttonDisabled]}
          onPress={testAPIConnection}
          disabled={isRunning}
        >
          <Text style={styles.buttonText}>
            {isRunning ? 'Testing...' : 'Run API Test'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.infoButton}
          onPress={showNetworkInfo}
        >
          <Text style={styles.infoButtonText}>Network Info</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.resultsContainer}>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 10,
  },
  button: {
    flex: 1,
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  infoButton: {
    backgroundColor: '#6c757d',
    padding: 15,
    borderRadius: 8,
    minWidth: 100,
  },
  infoButtonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  resultText: {
    fontSize: 14,
    fontFamily: 'monospace',
    color: '#333',
    lineHeight: 20,
  },
});

export default SimpleAPITest;
