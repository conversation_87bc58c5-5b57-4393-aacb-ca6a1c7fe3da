/**
 * Debug script to test the parking service directly
 * Run with: node debug-parking.js
 */

const axios = require('axios');

// Mock location data (San Francisco)
const testLocation = {
  coords: {
    latitude: 37.7749,
    longitude: -122.4194,
    altitude: null,
    accuracy: null,
    altitudeAccuracy: null,
    heading: null,
    speed: null,
  },
  timestamp: Date.now(),
};

// Calculate bounding box
function calculateBoundingBox(latitude, longitude, radiusMeters) {
  const earthRadius = 6371000;
  const latRadian = (latitude * Math.PI) / 180;
  
  const deltaLat = radiusMeters / earthRadius;
  const deltaLon = radiusMeters / (earthRadius * Math.cos(latRadian));
  
  const deltaLatDegrees = (deltaLat * 180) / Math.PI;
  const deltaLonDegrees = (deltaLon * 180) / Math.PI;
  
  return {
    north: latitude + deltaLatDegrees,
    south: latitude - deltaLatDegrees,
    east: longitude + deltaLonDegrees,
    west: longitude - deltaLonDegrees,
  };
}

// Validate coordinates
function validateCoordinates(latitude, longitude) {
  return (
    latitude >= -90 &&
    latitude <= 90 &&
    longitude >= -180 &&
    longitude <= 180 &&
    !isNaN(latitude) &&
    !isNaN(longitude)
  );
}

// Calculate distance
function calculateHaversineDistance(lat1, lon1, lat2, lon2) {
  const R = 6371000;
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return Math.round(R * c);
}

// Process OSM data
function processOSMData(osmSpots, searchParams) {
  console.log('🔄 Processing', osmSpots.length, 'OSM spots');
  const { latitude: userLat, longitude: userLon, radius } = searchParams;
  const processedSpots = [];

  for (let i = 0; i < osmSpots.length; i++) {
    const spot = osmSpots[i];
    try {
      console.log(`Processing spot ${i + 1}:`, spot.id, spot.type);
      
      let spotLat, spotLon;
      
      if (spot.type === 'node') {
        spotLat = spot.lat;
        spotLon = spot.lon;
      } else if (spot.type === 'way' || spot.type === 'relation') {
        spotLat = spot.lat || 0;
        spotLon = spot.lon || 0;
      } else {
        console.log('Skipping unknown type:', spot.type);
        continue;
      }

      if (!validateCoordinates(spotLat, spotLon)) {
        console.log('Invalid coordinates:', spotLat, spotLon);
        continue;
      }

      const distance = calculateHaversineDistance(userLat, userLon, spotLat, spotLon);
      
      if (distance > radius) {
        console.log('Outside radius:', distance, '>', radius);
        continue;
      }

      const name = spot.tags?.name || `Parking ${spot.type} ${spot.id}`;
      const address = [
        spot.tags?.['addr:housenumber'],
        spot.tags?.['addr:street'],
        spot.tags?.['addr:city']
      ].filter(Boolean).join(' ') || 'Address not available';

      const processed = {
        id: `osm_${spot.type}_${spot.id}`,
        name,
        address,
        latitude: spotLat,
        longitude: spotLon,
        distance,
        type: spot.tags?.parking || 'unknown',
        fee: spot.tags?.fee === 'yes',
        access: spot.tags?.access || 'unknown',
        amenities: [],
        rating: 4.0 + Math.random(),
        isAvailable: true,
      };

      console.log('✅ Processed:', processed.name, processed.distance + 'm');
      processedSpots.push(processed);
    } catch (error) {
      console.error('Error processing spot:', error);
    }
  }

  return processedSpots;
}

// Main test function
async function testParkingService() {
  console.log('🧪 Testing parking service...');
  
  const { latitude, longitude } = testLocation.coords;
  const radius = 1000;
  
  console.log('📍 Location:', latitude, longitude);
  console.log('📏 Radius:', radius);
  
  // Validate coordinates
  if (!validateCoordinates(latitude, longitude)) {
    console.error('❌ Invalid coordinates');
    return;
  }
  
  // Calculate bounding box
  const bbox = calculateBoundingBox(latitude, longitude, radius);
  console.log('📦 Bounding box:', bbox);
  
  // Create query
  const query = `
    [out:json][timeout:25];
    (
      node["amenity"="parking"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
      way["amenity"="parking"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
      relation["amenity"="parking"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
    );
    out center meta;
  `;
  
  console.log('📝 Query:', query.trim());
  
  try {
    console.log('🌐 Making API request...');
    const response = await axios.post(
      'https://overpass-api.de/api/interpreter',
      query,
      {
        headers: {
          'Content-Type': 'text/plain',
          'User-Agent': 'SPark-Debug/1.0',
        },
        timeout: 10000,
      }
    );
    
    console.log('✅ Response received');
    console.log('📊 Status:', response.status);
    console.log('🔢 Elements:', response.data.elements?.length || 0);
    
    if (response.data.elements && response.data.elements.length > 0) {
      console.log('📝 Sample element:', response.data.elements[0]);
      
      // Process the data
      const searchParams = { latitude, longitude, radius };
      const processed = processOSMData(response.data.elements, searchParams);
      
      console.log('✅ Final result:', processed.length, 'spots');
      console.log('🎯 Top 3 spots:');
      processed.slice(0, 3).forEach((spot, i) => {
        console.log(`${i + 1}. ${spot.name} - ${spot.distance}m`);
      });
      
      return {
        spots: processed.slice(0, 5),
        totalFound: processed.length,
        searchRadius: radius,
        userLocation: { latitude, longitude },
        cached: false,
        timestamp: Date.now(),
      };
    } else {
      console.log('❌ No elements found');
      return {
        spots: [],
        totalFound: 0,
        searchRadius: radius,
        userLocation: { latitude, longitude },
        cached: false,
        timestamp: Date.now(),
      };
    }
  } catch (error) {
    console.error('❌ API Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    throw error;
  }
}

// Run the test
testParkingService()
  .then(result => {
    console.log('\n🎉 Test completed successfully!');
    console.log('📊 Final result:', JSON.stringify(result, null, 2));
  })
  .catch(error => {
    console.error('\n💥 Test failed:', error.message);
  });
