import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  FlatList,
} from 'react-native';
import { router } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import MobileInput from '../../src/components/ui/MobileInput';
import { SearchIcon, FilterIcon, StarIcon, LocationIcon, BackIcon } from '../../src/components/ui/Icon';
import { colors, typography, spacing, borderRadius } from '../../src/theme';

export default function SearchScreen() {
  const insets = useSafeAreaInsets();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState({ min: 0, max: 50 });

  const amenities = ['Covered', 'Security', 'EV Charging', 'Valet', '24/7 Access', 'Disabled Access'];

  const mockSpots = [
    {
      id: '1',
      title: 'Downtown Garage',
      address: '123 Main St',
      price: 12,
      rating: 4.5,
      amenities: ['Covered', 'Security'],
      distance: '0.2 mi',
      walkTime: '2 min',
      available: true,
    },
    {
      id: '2',
      title: 'City Center Lot',
      address: '456 Oak Ave',
      price: 8,
      rating: 4.2,
      amenities: ['24/7 Access'],
      distance: '0.5 mi',
      walkTime: '5 min',
      available: true,
    },
    {
      id: '3',
      title: 'Business District',
      address: '789 Pine St',
      price: 15,
      rating: 4.8,
      amenities: ['Covered', 'EV Charging', 'Valet'],
      distance: '0.8 mi',
      walkTime: '8 min',
      available: false,
    },
  ];

  const handleSearch = () => {
    if (!searchQuery.trim()) {
      Alert.alert('Search Required', 'Please enter a location to search for parking.');
      return;
    }
    Alert.alert('Search', `Searching for parking near: ${searchQuery}`);
  };

  const handleSpotPress = (spotId: string) => {
    router.push(`/spot/details?spotId=${spotId}`);
  };

  const toggleAmenity = (amenity: string) => {
    setSelectedAmenities(prev =>
      prev.includes(amenity)
        ? prev.filter(a => a !== amenity)
        : [...prev, amenity]
    );
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
        <BackIcon size={24} color={colors.black[900]} />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>Find Parking</Text>
      <TouchableOpacity
        style={styles.filterButton}
        onPress={() => setShowFilters(!showFilters)}
      >
        <FilterIcon size={24} color={showFilters ? colors.primary[500] : colors.black[900]} />
      </TouchableOpacity>
    </View>
  );

  const renderSearchBar = () => (
    <View style={styles.searchSection}>
      <MobileInput
        variant="search"
        placeholder="Where do you need to park?"
        value={searchQuery}
        onChangeText={setSearchQuery}
        leftIcon={<SearchIcon size={20} color={colors.gray[500]} />}
        onSubmitEditing={handleSearch}
        returnKeyType="search"
      />

      {/* Quick Find Button */}
      <TouchableOpacity
        style={styles.quickFindButton}
        onPress={() => router.push('/find-parking')}
        activeOpacity={0.8}
      >
        <LocationIcon size={20} color={colors.white} />
        <Text style={styles.quickFindText}>Find Nearby</Text>
      </TouchableOpacity>
    </View>
  );

  const renderFilters = () => {
    if (!showFilters) return null;

    return (
      <View style={styles.filtersSection}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.amenitiesScroll}>
          {amenities.map((amenity) => (
            <TouchableOpacity
              key={amenity}
              style={[
                styles.amenityChip,
                selectedAmenities.includes(amenity) && styles.amenityChipSelected,
              ]}
              onPress={() => toggleAmenity(amenity)}
            >
              <Text
                style={[
                  styles.amenityChipText,
                  selectedAmenities.includes(amenity) && styles.amenityChipTextSelected,
                ]}
              >
                {amenity}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderSpotItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.spotCard}
      onPress={() => handleSpotPress(item.id)}
    >
      <View style={styles.spotHeader}>
        <View style={styles.spotInfo}>
          <Text style={styles.spotTitle}>{item.title}</Text>
          <View style={styles.addressRow}>
            <LocationIcon size={14} color={colors.gray[500]} />
            <Text style={styles.spotAddress}>{item.address}</Text>
          </View>
        </View>
        <View style={styles.priceContainer}>
          <Text style={styles.priceText}>${item.price}</Text>
          <Text style={styles.priceUnit}>/hr</Text>
        </View>
      </View>

      <View style={styles.spotFooter}>
        <View style={styles.ratingRow}>
          <StarIcon size={14} color={colors.primary[500]} />
          <Text style={styles.ratingText}>{item.rating}</Text>
          <Text style={styles.distanceText}>• {item.distance}</Text>
        </View>
        <View style={[
          styles.statusBadge,
          { backgroundColor: item.available ? colors.success[100] : colors.gray[100] }
        ]}>
          <Text style={[
            styles.statusText,
            { color: item.available ? colors.success[700] : colors.gray[600] }
          ]}>
            {item.available ? 'Available' : 'Occupied'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      {renderHeader()}
      {renderSearchBar()}
      {renderFilters()}

      <FlatList
        data={mockSpots.filter(spot => spot.available)}
        renderItem={renderSpotItem}
        keyExtractor={(item) => item.id}
        style={styles.spotsList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.spotsListContent}
      />
    </SafeAreaView>
  );
}



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50, // Increased to avoid status bar overlap
    paddingBottom: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.black[900],
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Search Section
  searchSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.white,
    gap: 12,
  },
  quickFindButton: {
    backgroundColor: colors.primary[500],
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickFindText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },

  // Filters Section
  filtersSection: {
    paddingHorizontal: 20,
    paddingBottom: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  amenitiesScroll: {
    paddingVertical: 8,
  },
  amenityChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    marginRight: 12,
  },
  amenityChipSelected: {
    backgroundColor: colors.primary[500],
  },
  amenityChipText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[700],
  },
  amenityChipTextSelected: {
    color: colors.black[900],
  },

  // Spots List
  spotsList: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  spotsListContent: {
    padding: 20,
  },
  spotCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  spotHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  spotInfo: {
    flex: 1,
  },
  spotTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.black[900],
    marginBottom: 4,
  },
  addressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  spotAddress: {
    fontSize: 14,
    color: colors.gray[600],
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  priceText: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.primary[500],
  },
  priceUnit: {
    fontSize: 14,
    color: colors.gray[600],
  },
  spotFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[700],
  },
  distanceText: {
    fontSize: 14,
    color: colors.gray[500],
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
});
