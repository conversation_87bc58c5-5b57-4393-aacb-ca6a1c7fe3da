import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import Constants from 'expo-constants';

export interface NotificationData {
  type: 'booking_confirmed' | 'booking_reminder' | 'spot_available' | 'payment_processed' | 'host_new_booking';
  title: string;
  body: string;
  data?: any;
}

export class NotificationService {
  private static instance: NotificationService;
  private expoPushToken: string | null = null;

  private constructor() {
    this.setupNotificationHandler();
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // Setup notification handler
  private setupNotificationHandler(): void {
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });
  }

  // Initialize push notifications
  public async initialize(): Promise<string | null> {
    try {
      // Check if we're in Expo Go (which doesn't support push notifications in SDK 53+)
      const isExpoGo = Constants.appOwnership === 'expo';

      if (isExpoGo) {
        console.log('📱 Push notifications are not available in Expo Go (SDK 53+)');
        console.log('💡 Use a development build for push notification testing');
        return null;
      }

      if (!Device.isDevice) {
        console.log('Push notifications only work on physical devices');
        return null;
      }

      // Check if projectId is available
      const projectId = Constants.expoConfig?.extra?.eas?.projectId || Constants.easConfig?.projectId;
      if (!projectId) {
        console.log('⚠️ No projectId found - push notifications require EAS project setup');
        return null;
      }

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return null;
      }

      // Get push token
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      });

      this.expoPushToken = token.data;
      console.log('Push token:', this.expoPushToken);

      // Configure notification channels for Android
      if (Platform.OS === 'android') {
        await this.setupAndroidChannels();
      }

      return this.expoPushToken;
    } catch (error) {
      console.error('Error initializing notifications:', error);
      return null;
    }
  }

  // Setup Android notification channels
  private async setupAndroidChannels(): Promise<void> {
    await Notifications.setNotificationChannelAsync('booking', {
      name: 'Booking Updates',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#1a9bff',
      sound: 'default',
    });

    await Notifications.setNotificationChannelAsync('reminders', {
      name: 'Parking Reminders',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#f59e0b',
      sound: 'default',
    });

    await Notifications.setNotificationChannelAsync('host', {
      name: 'Host Notifications',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#10b981',
      sound: 'default',
    });
  }

  // Get current push token
  public getPushToken(): string | null {
    return this.expoPushToken;
  }

  // Schedule local notification
  public async scheduleLocalNotification(
    notification: NotificationData,
    trigger?: Notifications.NotificationTriggerInput
  ): Promise<string> {
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title: notification.title,
        body: notification.body,
        data: notification.data || {},
        sound: 'default',
        priority: Notifications.AndroidNotificationPriority.HIGH,
      },
      trigger: trigger || null,
    });

    return notificationId;
  }

  // Send immediate local notification
  public async sendLocalNotification(notification: NotificationData): Promise<void> {
    await Notifications.presentNotificationAsync({
      title: notification.title,
      body: notification.body,
      data: notification.data || {},
      sound: 'default',
      priority: Notifications.AndroidNotificationPriority.HIGH,
    });
  }

  // Schedule booking reminder
  public async scheduleBookingReminder(
    bookingId: string,
    spotTitle: string,
    startTime: Date
  ): Promise<string> {
    const reminderTime = new Date(startTime.getTime() - 15 * 60 * 1000); // 15 minutes before

    return await this.scheduleLocalNotification(
      {
        type: 'booking_reminder',
        title: 'Parking Reminder',
        body: `Your parking at ${spotTitle} starts in 15 minutes`,
        data: { bookingId, type: 'booking_reminder' },
      },
      {
        date: reminderTime,
      }
    );
  }

  // Cancel scheduled notification
  public async cancelNotification(notificationId: string): Promise<void> {
    await Notifications.cancelScheduledNotificationAsync(notificationId);
  }

  // Cancel all notifications for a booking
  public async cancelBookingNotifications(bookingId: string): Promise<void> {
    const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
    
    for (const notification of scheduledNotifications) {
      if (notification.content.data?.bookingId === bookingId) {
        await Notifications.cancelScheduledNotificationAsync(notification.identifier);
      }
    }
  }

  // Handle notification received while app is running
  public addNotificationReceivedListener(
    listener: (notification: Notifications.Notification) => void
  ): Notifications.Subscription {
    return Notifications.addNotificationReceivedListener(listener);
  }

  // Handle notification response (when user taps notification)
  public addNotificationResponseReceivedListener(
    listener: (response: Notifications.NotificationResponse) => void
  ): Notifications.Subscription {
    return Notifications.addNotificationResponseReceivedListener(listener);
  }

  // Send push notification to server (for sending to other users)
  public async sendPushNotification(
    targetToken: string,
    notification: NotificationData
  ): Promise<boolean> {
    try {
      const message = {
        to: targetToken,
        sound: 'default',
        title: notification.title,
        body: notification.body,
        data: notification.data || {},
        priority: 'high',
        channelId: this.getChannelForNotificationType(notification.type),
      };

      // In a real app, this would go through your backend
      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });

      const result = await response.json();
      return result.data?.status === 'ok';
    } catch (error) {
      console.error('Failed to send push notification:', error);
      return false;
    }
  }

  // Get notification channel based on type
  private getChannelForNotificationType(type: NotificationData['type']): string {
    switch (type) {
      case 'booking_confirmed':
      case 'payment_processed':
        return 'booking';
      case 'booking_reminder':
        return 'reminders';
      case 'host_new_booking':
        return 'host';
      default:
        return 'booking';
    }
  }

  // Create notification templates
  public static createBookingConfirmedNotification(
    spotTitle: string,
    startTime: string,
    bookingId: string
  ): NotificationData {
    return {
      type: 'booking_confirmed',
      title: 'Booking Confirmed!',
      body: `Your parking at ${spotTitle} is confirmed for ${startTime}`,
      data: { bookingId, type: 'booking_confirmed' },
    };
  }

  public static createSpotAvailableNotification(
    spotTitle: string,
    spotId: string
  ): NotificationData {
    return {
      type: 'spot_available',
      title: 'Parking Spot Available!',
      body: `${spotTitle} is now available for booking`,
      data: { spotId, type: 'spot_available' },
    };
  }

  public static createHostNewBookingNotification(
    spotTitle: string,
    guestName: string,
    bookingId: string
  ): NotificationData {
    return {
      type: 'host_new_booking',
      title: 'New Booking Received!',
      body: `${guestName} booked your parking spot at ${spotTitle}`,
      data: { bookingId, type: 'host_new_booking' },
    };
  }

  public static createPaymentProcessedNotification(
    amount: number,
    spotTitle: string,
    bookingId: string
  ): NotificationData {
    return {
      type: 'payment_processed',
      title: 'Payment Processed',
      body: `$${amount.toFixed(2)} payment confirmed for ${spotTitle}`,
      data: { bookingId, type: 'payment_processed' },
    };
  }

  // Get notification badge count
  public async getBadgeCount(): Promise<number> {
    return await Notifications.getBadgeCountAsync();
  }

  // Set notification badge count
  public async setBadgeCount(count: number): Promise<void> {
    await Notifications.setBadgeCountAsync(count);
  }

  // Clear all notifications
  public async clearAllNotifications(): Promise<void> {
    await Notifications.dismissAllNotificationsAsync();
    await this.setBadgeCount(0);
  }
}

export default NotificationService.getInstance();
