import axios from 'axios';
import * as Location from 'expo-location';
import {
  OSMParkingSpot,
  ProcessedParkingSpot,
  OverpassResponse,
  ParkingSearchParams,
  ParkingSearchResult,
  CachedParkingData,
} from '../types/parking';
import {
  calculateHaversineDistance,
  validateCoordinates,
  generateLocationCacheKey,
  calculateBoundingBox,
} from '../utils/geoUtils';
import { cacheManager } from '../utils/storage';

class OpenStreetMapService {
  private readonly OVERPASS_API_URL = 'https://overpass-api.de/api/interpreter';
  private readonly NOMINATIM_API_URL = 'https://nominatim.openstreetmap.org';
  private readonly MAX_RADIUS = 1500; // 1.5km max radius
  private readonly CACHE_DURATION_MINUTES = 30; // Cache for 30 minutes
  private readonly REQUEST_TIMEOUT = 10000; // 10 seconds timeout

  /**
   * Find nearby parking spots using OpenStreetMap data
   */
  async findNearbyParking(
    userLocation: Location.LocationObject,
    searchRadius: number = 1000
  ): Promise<ParkingSearchResult> {
    const { latitude, longitude } = userLocation.coords;

    // Validate inputs
    if (!validateCoordinates(latitude, longitude)) {
      throw new Error('Invalid coordinates provided');
    }

    const radius = Math.min(searchRadius, this.MAX_RADIUS);
    const searchParams: ParkingSearchParams = {
      latitude,
      longitude,
      radius,
      maxResults: 5,
    };

    // Check cache first
    const cachedResult = await this.getCachedResult(searchParams);
    if (cachedResult) {
      return cachedResult;
    }

    try {
      // Fetch from OpenStreetMap
      const osmData = await this.fetchParkingFromOSM(searchParams);
      const processedSpots = this.processOSMData(osmData, searchParams);
      
      // Sort by distance and limit to 5 closest
      const sortedSpots = processedSpots
        .sort((a, b) => a.distance - b.distance)
        .slice(0, 5);

      const result: ParkingSearchResult = {
        spots: sortedSpots,
        totalFound: processedSpots.length,
        searchRadius: radius,
        userLocation: { latitude, longitude },
        cached: false,
        timestamp: Date.now(),
      };

      // Cache the result
      await this.cacheResult(searchParams, result);

      return result;
    } catch (error) {
      console.error('Error fetching parking data:', error);
      
      // Try to return stale cached data if available
      const staleCache = await this.getStaleCache(searchParams);
      if (staleCache) {
        return { ...staleCache, cached: true };
      }

      // Return empty result if no cache available
      return {
        spots: [],
        totalFound: 0,
        searchRadius: radius,
        userLocation: { latitude, longitude },
        cached: false,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * Fetch parking data from Overpass API
   */
  private async fetchParkingFromOSM(params: ParkingSearchParams): Promise<OSMParkingSpot[]> {
    const { latitude, longitude, radius } = params;
    const bbox = calculateBoundingBox(latitude, longitude, radius);

    // Overpass QL query to find parking amenities
    const query = `
      [out:json][timeout:25];
      (
        node["amenity"="parking"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
        way["amenity"="parking"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
        relation["amenity"="parking"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
      );
      out center meta;
    `;

    const response = await axios.post<OverpassResponse>(
      this.OVERPASS_API_URL,
      query,
      {
        headers: {
          'Content-Type': 'text/plain',
          'User-Agent': 'SPark-Mobile-App/1.0',
        },
        timeout: this.REQUEST_TIMEOUT,
      }
    );

    return response.data.elements || [];
  }

  /**
   * Process raw OSM data into app-friendly format
   */
  private processOSMData(
    osmSpots: OSMParkingSpot[],
    searchParams: ParkingSearchParams
  ): ProcessedParkingSpot[] {
    const { latitude: userLat, longitude: userLon, radius } = searchParams;
    const processedSpots: ProcessedParkingSpot[] = [];

    for (const spot of osmSpots) {
      try {
        // Get coordinates (handle different OSM element types)
        let spotLat: number, spotLon: number;
        
        if (spot.type === 'node') {
          spotLat = spot.lat;
          spotLon = spot.lon;
        } else if (spot.type === 'way' || spot.type === 'relation') {
          // For ways and relations, use center coordinates if available
          spotLat = spot.lat || 0;
          spotLon = spot.lon || 0;
        } else {
          continue; // Skip if no coordinates
        }

        if (!validateCoordinates(spotLat, spotLon)) {
          continue;
        }

        // Calculate distance
        const distance = calculateHaversineDistance(userLat, userLon, spotLat, spotLon);
        
        // Skip if outside radius
        if (distance > radius) {
          continue;
        }

        // Process the spot data
        const processed = this.createProcessedSpot(spot, spotLat, spotLon, distance);
        processedSpots.push(processed);
      } catch (error) {
        console.warn('Error processing OSM spot:', error);
        continue;
      }
    }

    return processedSpots;
  }

  /**
   * Create a processed parking spot from OSM data
   */
  private createProcessedSpot(
    osmSpot: OSMParkingSpot,
    latitude: number,
    longitude: number,
    distance: number
  ): ProcessedParkingSpot {
    const tags = osmSpot.tags;
    
    // Generate name
    const name = tags.name || 
                 tags.operator || 
                 `Parking ${osmSpot.type} ${osmSpot.id.slice(-4)}` ||
                 'Public Parking';

    // Generate address
    const address = this.generateAddress(tags);

    // Parse capacity
    const capacity = tags.capacity ? parseInt(tags.capacity, 10) : undefined;

    // Determine parking type
    const type = this.mapParkingType(tags.parking);

    // Determine if fee is required
    const fee = tags.fee === 'yes';

    // Determine access type
    const access = this.mapAccessType(tags.access);

    // Generate amenities
    const amenities = this.extractAmenities(tags);

    // Generate mock rating (OSM doesn't provide ratings)
    const rating = this.generateMockRating();

    // Generate mock availability (OSM doesn't provide real-time data)
    const isAvailable = Math.random() > 0.2; // 80% chance of being available

    return {
      id: `osm_${osmSpot.type}_${osmSpot.id}`,
      name,
      address,
      latitude,
      longitude,
      distance,
      capacity,
      type,
      fee,
      access,
      operator: tags.operator,
      openingHours: tags.opening_hours,
      maxStay: tags.maxstay,
      amenities,
      rating,
      isAvailable,
    };
  }

  /**
   * Generate address from OSM tags
   */
  private generateAddress(tags: any): string {
    const parts: string[] = [];
    
    if (tags['addr:housenumber']) {
      parts.push(tags['addr:housenumber']);
    }
    if (tags['addr:street']) {
      parts.push(tags['addr:street']);
    }
    if (tags['addr:city']) {
      parts.push(tags['addr:city']);
    }
    if (tags['addr:postcode']) {
      parts.push(tags['addr:postcode']);
    }

    return parts.length > 0 ? parts.join(' ') : 'Address not available';
  }

  /**
   * Map OSM parking type to app type
   */
  private mapParkingType(osmType?: string): ProcessedParkingSpot['type'] {
    switch (osmType) {
      case 'surface':
        return 'surface';
      case 'multi-storey':
        return 'multi-storey';
      case 'underground':
        return 'underground';
      case 'street_side':
        return 'street_side';
      default:
        return 'unknown';
    }
  }

  /**
   * Map OSM access type to app access type
   */
  private mapAccessType(osmAccess?: string): ProcessedParkingSpot['access'] {
    switch (osmAccess) {
      case 'yes':
      case 'permissive':
        return 'public';
      case 'private':
        return 'private';
      case 'customers':
        return 'customers';
      default:
        return 'unknown';
    }
  }

  /**
   * Extract amenities from OSM tags
   */
  private extractAmenities(tags: any): string[] {
    const amenities: string[] = [];

    if (tags.covered === 'yes') amenities.push('Covered');
    if (tags.wheelchair === 'yes') amenities.push('Wheelchair Accessible');
    if (tags.surface === 'paved') amenities.push('Paved');
    if (tags.lighting === 'yes') amenities.push('Lighting');
    if (tags.surveillance === 'yes') amenities.push('Security');
    if (tags['fee:conditional']) amenities.push('Time-based Pricing');

    return amenities;
  }

  /**
   * Generate mock rating since OSM doesn't provide ratings
   */
  private generateMockRating(): number {
    // Generate rating between 3.5 and 5.0
    return Math.round((3.5 + Math.random() * 1.5) * 10) / 10;
  }

  /**
   * Cache parking search result
   */
  private async cacheResult(
    params: ParkingSearchParams,
    result: ParkingSearchResult
  ): Promise<void> {
    try {
      const cacheKey = generateLocationCacheKey(
        params.latitude,
        params.longitude,
        params.radius
      );

      const cacheData: CachedParkingData = {
        result,
        timestamp: Date.now(),
        expiration: Date.now() + (this.CACHE_DURATION_MINUTES * 60 * 1000),
        location: {
          latitude: params.latitude,
          longitude: params.longitude,
        },
      };

      await cacheManager.setCache(cacheKey, cacheData, this.CACHE_DURATION_MINUTES);
    } catch (error) {
      console.warn('Failed to cache parking result:', error);
    }
  }

  /**
   * Get cached parking result if available and valid
   */
  private async getCachedResult(params: ParkingSearchParams): Promise<ParkingSearchResult | null> {
    try {
      const cacheKey = generateLocationCacheKey(
        params.latitude,
        params.longitude,
        params.radius
      );

      const cachedData: CachedParkingData | null = await cacheManager.getCache(cacheKey);
      
      if (cachedData && Date.now() < cachedData.expiration) {
        return { ...cachedData.result, cached: true };
      }

      return null;
    } catch (error) {
      console.warn('Failed to get cached parking result:', error);
      return null;
    }
  }

  /**
   * Get stale cache data for offline scenarios
   */
  private async getStaleCache(params: ParkingSearchParams): Promise<ParkingSearchResult | null> {
    try {
      const cacheKey = generateLocationCacheKey(
        params.latitude,
        params.longitude,
        params.radius
      );

      const cachedData: CachedParkingData | null = await cacheManager.getCache(cacheKey);
      
      if (cachedData) {
        return { ...cachedData.result, cached: true };
      }

      return null;
    } catch (error) {
      console.warn('Failed to get stale cache:', error);
      return null;
    }
  }
}

export const openStreetMapService = new OpenStreetMapService();
export default openStreetMapService;
