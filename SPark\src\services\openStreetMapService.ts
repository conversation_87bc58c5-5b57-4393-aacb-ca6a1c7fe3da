import axios from 'axios';
import * as Location from 'expo-location';
import {
  OSMParkingSpot,
  ProcessedParkingSpot,
  OverpassResponse,
  ParkingSearchParams,
  ParkingSearchResult,
  CachedParkingData,
} from '../types/parking';
import {
  calculateHaversineDistance,
  validateCoordinates,
  generateLocationCacheKey,
  calculateBoundingBox,
} from '../utils/geoUtils';
import { cacheManager } from '../utils/storage';

const OVERPASS_API_URL = 'https://overpass-api.de/api/interpreter';
const NOMINATIM_API_URL = 'https://nominatim.openstreetmap.org';
const MAX_RADIUS = 1500; // 1.5km max radius
const CACHE_DURATION_MINUTES = 30; // Cache for 30 minutes
const REQUEST_TIMEOUT = 10000; // 10 seconds timeout

/**
 * Find nearby parking spots using OpenStreetMap data
 */
async function findNearbyParking(
  userLocation: Location.LocationObject,
  searchRadius: number = 1000
): Promise<ParkingSearchResult> {
    console.log('🚀 OpenStreetMapService.findNearbyParking called');
    const { latitude, longitude } = userLocation.coords;
    console.log('📍 Coordinates:', latitude, longitude);
    console.log('📏 Search radius:', searchRadius);

    // Validate inputs
    if (!validateCoordinates(latitude, longitude)) {
      console.error('❌ Invalid coordinates provided');
      throw new Error('Invalid coordinates provided');
    }

    const radius = Math.min(searchRadius, MAX_RADIUS);
    console.log('✅ Using radius:', radius);

    const searchParams: ParkingSearchParams = {
      latitude,
      longitude,
      radius,
      maxResults: 5,
    };

    // Check cache first
    console.log('🔍 Checking cache...');
    const cachedResult = await getCachedResult(searchParams);
    if (cachedResult) {
      console.log('✅ Found cached result with', cachedResult.spots.length, 'spots');
      return cachedResult;
    }
    console.log('❌ No cached result found');

    try {
      // Fetch from OpenStreetMap
      console.log('🌐 Fetching from OpenStreetMap...');
      const osmData = await fetchParkingFromOSM(searchParams);
      console.log('📊 Raw OSM data received:', osmData.length, 'elements');

      console.log('🔄 Processing OSM data...');
      const processedSpots = processOSMData(osmData, searchParams);
      console.log('✅ Processed spots:', processedSpots.length);

      // Sort by distance and limit to 5 closest
      const sortedSpots = processedSpots
        .sort((a, b) => a.distance - b.distance)
        .slice(0, 5);
      console.log('🎯 Final sorted spots:', sortedSpots.length);

      const result: ParkingSearchResult = {
        spots: sortedSpots,
        totalFound: processedSpots.length,
        searchRadius: radius,
        userLocation: { latitude, longitude },
        cached: false,
        timestamp: Date.now(),
      };

      console.log('💾 Caching result...');
      // Cache the result
      await cacheResult(searchParams, result);

      console.log('✅ Returning result:', result);
      return result;
    } catch (error) {
      console.error('Error fetching parking data:', error);
      
      // Try to return stale cached data if available
      const staleCache = await getStaleCache(searchParams);
      if (staleCache) {
        return { ...staleCache, cached: true };
      }

      // Return empty result if no cache available
      return {
        spots: [],
        totalFound: 0,
        searchRadius: radius,
        userLocation: { latitude, longitude },
        cached: false,
        timestamp: Date.now(),
      };
    }
}

/**
 * Fetch parking data from Overpass API
 */
async function fetchParkingFromOSM(params: ParkingSearchParams): Promise<OSMParkingSpot[]> {
    console.log('🌐 fetchParkingFromOSM called');
    const { latitude, longitude, radius } = params;
    const bbox = calculateBoundingBox(latitude, longitude, radius);
    console.log('📦 Bounding box:', bbox);

    // Overpass QL query to find parking amenities
    // Focus on nodes only for reliable coordinates
    const query = `
      [out:json][timeout:25];
      (
        node["amenity"="parking"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
      );
      out meta;
    `;

    console.log('📝 Overpass query:', query.trim());
    console.log('🔗 API URL:', OVERPASS_API_URL);

    try {
      console.log('📡 Making API request...');
      const response = await axios.post<OverpassResponse>(
        OVERPASS_API_URL,
        query,
        {
          headers: {
            'Content-Type': 'text/plain',
            'User-Agent': 'SPark-Mobile-App/1.0',
          },
          timeout: REQUEST_TIMEOUT,
        }
      );

      console.log('✅ API response received');
      console.log('📊 Response status:', response.status);
      console.log('📋 Response data keys:', Object.keys(response.data));
      console.log('🔢 Elements count:', response.data.elements?.length || 0);

      const elements = response.data.elements || [];
      console.log('🎯 Returning elements:', elements.length);

      if (elements.length > 0) {
        console.log('📝 Sample element:', elements[0]);
      }

      return elements;
    } catch (error) {
      console.error('❌ API request failed:', error);
      throw error;
    }
}

/**
 * Process raw OSM data into app-friendly format
 */
function processOSMData(
  osmSpots: OSMParkingSpot[],
  searchParams: ParkingSearchParams
): ProcessedParkingSpot[] {
    console.log('🔄 processOSMData called with', osmSpots.length, 'spots');
    const { latitude: userLat, longitude: userLon, radius } = searchParams;
    const processedSpots: ProcessedParkingSpot[] = [];

    for (let i = 0; i < osmSpots.length; i++) {
      const spot = osmSpots[i];
      try {
        // Only log first few spots to avoid console spam
        if (i < 3) {
          console.log(`🔍 Processing spot ${i + 1}/${osmSpots.length}:`, spot.id, spot.type);
        }

        // Get coordinates (only nodes for now)
        if (spot.type !== 'node') {
          if (i < 3) console.log('⚠️ Skipping non-node element:', spot.type, spot.id);
          continue;
        }

        const spotLat = spot.lat;
        const spotLon = spot.lon;

        if (!validateCoordinates(spotLat, spotLon)) {
          if (i < 3) console.log('⚠️ Skipping spot with invalid coordinates:', spotLat, spotLon);
          continue;
        }

        // Calculate distance
        const distance = calculateHaversineDistance(userLat, userLon, spotLat, spotLon);

        // Skip if outside radius
        if (distance > radius) {
          if (i < 3) console.log('⚠️ Skipping spot outside radius:', distance, '>', radius);
          continue;
        }

        // Process the spot data
        const processed = createProcessedSpot(spot, spotLat, spotLon, distance);
        if (i < 3) console.log('✅ Processed spot:', processed.name, processed.distance + 'm');
        processedSpots.push(processed);
      } catch (error) {
        console.warn('❌ Error processing OSM spot:', error);
        continue;
      }
    }

    console.log('✅ processOSMData completed. Processed', processedSpots.length, 'spots');
    return processedSpots;
}

/**
 * Create a processed parking spot from OSM data
 */
function createProcessedSpot(
  osmSpot: OSMParkingSpot,
  latitude: number,
  longitude: number,
  distance: number
): ProcessedParkingSpot {
    const tags = osmSpot.tags;
    
    // Generate name
    const name = tags.name || 
                 tags.operator || 
                 `Parking ${osmSpot.type} ${osmSpot.id.slice(-4)}` ||
                 'Public Parking';

    // Generate address
    const address = generateAddress(tags);

    // Parse capacity
    const capacity = tags.capacity ? parseInt(tags.capacity, 10) : undefined;

    // Determine parking type
    const type = mapParkingType(tags.parking);

    // Determine if fee is required
    const fee = tags.fee === 'yes';

    // Determine access type
    const access = mapAccessType(tags.access);

    // Generate amenities
    const amenities = extractAmenities(tags);

    // Generate mock rating (OSM doesn't provide ratings)
    const rating = generateMockRating();

    // Generate mock availability (OSM doesn't provide real-time data)
    const isAvailable = Math.random() > 0.2; // 80% chance of being available

    return {
      id: `osm_${osmSpot.type}_${osmSpot.id}`,
      name,
      address,
      latitude,
      longitude,
      distance,
      capacity,
      type,
      fee,
      access,
      operator: tags.operator,
      openingHours: tags.opening_hours,
      maxStay: tags.maxstay,
      amenities,
      rating,
      isAvailable,
    };
  }

/**
 * Generate address from OSM tags
 */
function generateAddress(tags: any): string {
    const parts: string[] = [];
    
    if (tags['addr:housenumber']) {
      parts.push(tags['addr:housenumber']);
    }
    if (tags['addr:street']) {
      parts.push(tags['addr:street']);
    }
    if (tags['addr:city']) {
      parts.push(tags['addr:city']);
    }
    if (tags['addr:postcode']) {
      parts.push(tags['addr:postcode']);
    }

    return parts.length > 0 ? parts.join(' ') : 'Address not available';
}

/**
 * Map OSM parking type to app type
 */
function mapParkingType(osmType?: string): ProcessedParkingSpot['type'] {
  switch (osmType) {
    case 'surface':
      return 'surface';
    case 'multi-storey':
      return 'multi-storey';
    case 'underground':
      return 'underground';
    case 'street_side':
      return 'street_side';
    default:
      return 'unknown';
  }
}

/**
 * Map OSM access type to app access type
 */
function mapAccessType(osmAccess?: string): ProcessedParkingSpot['access'] {
  switch (osmAccess) {
    case 'yes':
    case 'permissive':
      return 'public';
    case 'private':
      return 'private';
    case 'customers':
      return 'customers';
    default:
      return 'unknown';
  }
}

/**
 * Extract amenities from OSM tags
 */
function extractAmenities(tags: any): string[] {
    const amenities: string[] = [];

    if (tags.covered === 'yes') amenities.push('Covered');
    if (tags.wheelchair === 'yes') amenities.push('Wheelchair Accessible');
    if (tags.surface === 'paved') amenities.push('Paved');
    if (tags.lighting === 'yes') amenities.push('Lighting');
    if (tags.surveillance === 'yes') amenities.push('Security');
    if (tags['fee:conditional']) amenities.push('Time-based Pricing');

    return amenities;
}

/**
 * Generate mock rating since OSM doesn't provide ratings
 */
function generateMockRating(): number {
  // Generate rating between 3.5 and 5.0
  return Math.round((3.5 + Math.random() * 1.5) * 10) / 10;
}

/**
 * Cache parking search result
 */
async function cacheResult(
  params: ParkingSearchParams,
  result: ParkingSearchResult
): Promise<void> {
    try {
      const cacheKey = generateLocationCacheKey(
        params.latitude,
        params.longitude,
        params.radius
      );

      const cacheData: CachedParkingData = {
        result,
        timestamp: Date.now(),
        expiration: Date.now() + (this.CACHE_DURATION_MINUTES * 60 * 1000),
        location: {
          latitude: params.latitude,
          longitude: params.longitude,
        },
      };

      await cacheManager.setCache(cacheKey, cacheData, CACHE_DURATION_MINUTES);
    } catch (error) {
      console.warn('Failed to cache parking result:', error);
    }
}

/**
 * Get cached parking result if available and valid
 */
async function getCachedResult(params: ParkingSearchParams): Promise<ParkingSearchResult | null> {
  try {
    const cacheKey = generateLocationCacheKey(
      params.latitude,
      params.longitude,
      params.radius
    );

    const cachedData: CachedParkingData | null = await cacheManager.getCache(cacheKey);

    if (cachedData && Date.now() < cachedData.expiration) {
      return { ...cachedData.result, cached: true };
    }

    return null;
  } catch (error) {
    console.warn('Failed to get cached parking result:', error);
    return null;
  }
}

/**
 * Get stale cache data for offline scenarios
 */
async function getStaleCache(params: ParkingSearchParams): Promise<ParkingSearchResult | null> {
  try {
    const cacheKey = generateLocationCacheKey(
      params.latitude,
      params.longitude,
      params.radius
    );

    const cachedData: CachedParkingData | null = await cacheManager.getCache(cacheKey);

    if (cachedData) {
      return { ...cachedData.result, cached: true };
    }

    return null;
  } catch (error) {
    console.warn('Failed to get stale cache:', error);
    return null;
  }
}

// Export the main function as a service object
export const openStreetMapService = {
  findNearbyParking,
};

export default openStreetMapService;
