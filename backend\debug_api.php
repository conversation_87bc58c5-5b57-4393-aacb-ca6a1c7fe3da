<?php

echo "🔍 API Debug Test\n";
echo "=================\n\n";

function testEndpoint($url, $description) {
    echo "Testing: $description\n";
    echo "URL: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    curl_close($ch);
    
    echo "Status: $httpCode\n";
    echo "Content-Type: $contentType\n";
    
    if (strpos($response, '<!DOCTYPE html>') !== false || strpos($response, '<html') !== false) {
        echo "❌ Getting HTML response (likely an error page)\n";
        // Extract title or error message from HTML
        if (preg_match('/<title>(.*?)<\/title>/i', $response, $matches)) {
            echo "Page title: " . $matches[1] . "\n";
        }
        if (preg_match('/<h1[^>]*>(.*?)<\/h1>/i', $response, $matches)) {
            echo "Error: " . strip_tags($matches[1]) . "\n";
        }
    } else {
        echo "✅ Getting non-HTML response\n";
        echo "Response preview: " . substr($response, 0, 200) . "\n";
    }
    echo "\n";
}

// Test different endpoints
testEndpoint('http://localhost:8000/api/spots/search', 'Spots Search (Public)');
testEndpoint('http://localhost:8000/api/spots/nearby?lat=40.7128&lng=-74.0060', 'Nearby Spots (Public)');
testEndpoint('http://localhost:8000/api/auth/login', 'Auth Login (Public)');

echo "If you see HTML responses above, there are likely errors in the controllers.\n";
echo "Check the Laravel logs for more details.\n";
