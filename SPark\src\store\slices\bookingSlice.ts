import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { BookingState, Booking, ApiResponse } from '../../types';
import { bookingAPI } from '../../services/api';

const initialState: BookingState = {
  activeBooking: null,
  bookings: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const createBooking = createAsyncThunk(
  'booking/create',
  async (bookingData: {
    spotId: string;
    vehicleId: string;
    startTime: string;
    endTime: string;
    paymentMethodId: string;
  }, { rejectWithValue }) => {
    try {
      const response = await bookingAPI.createBooking(bookingData);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Booking creation failed');
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const fetchUserBookings = createAsyncThunk(
  'booking/fetchUserBookings',
  async (params: { page?: number; limit?: number; status?: string }, { rejectWithValue }) => {
    try {
      const response = await bookingAPI.getUserBookings(params);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch bookings');
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const fetchActiveBooking = createAsyncThunk(
  'booking/fetchActive',
  async (_, { rejectWithValue }) => {
    try {
      const response = await bookingAPI.getActiveBooking();
      if (response.success) {
        return response.data || null;
      }
      return rejectWithValue(response.message || 'Failed to fetch active booking');
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const extendBooking = createAsyncThunk(
  'booking/extend',
  async (data: { bookingId: string; newEndTime: string }, { rejectWithValue }) => {
    try {
      const response = await bookingAPI.extendBooking(data.bookingId, data.newEndTime);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to extend booking');
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const cancelBooking = createAsyncThunk(
  'booking/cancel',
  async (bookingId: string, { rejectWithValue }) => {
    try {
      const response = await bookingAPI.cancelBooking(bookingId);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to cancel booking');
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const completeBooking = createAsyncThunk(
  'booking/complete',
  async (bookingId: string, { rejectWithValue }) => {
    try {
      const response = await bookingAPI.completeBooking(bookingId);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to complete booking');
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

const bookingSlice = createSlice({
  name: 'booking',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setActiveBooking: (state, action: PayloadAction<Booking | null>) => {
      state.activeBooking = action.payload;
    },
    updateBookingStatus: (state, action: PayloadAction<{ bookingId: string; status: string }>) => {
      const { bookingId, status } = action.payload;
      
      // Update in bookings array
      const bookingIndex = state.bookings.findIndex(b => b.id === bookingId);
      if (bookingIndex !== -1) {
        state.bookings[bookingIndex].status = status as any;
      }
      
      // Update active booking if it matches
      if (state.activeBooking?.id === bookingId) {
        state.activeBooking.status = status as any;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Create booking
      .addCase(createBooking.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createBooking.fulfilled, (state, action) => {
        state.isLoading = false;
        state.bookings.unshift(action.payload);
        if (action.payload.status === 'active') {
          state.activeBooking = action.payload;
        }
        state.error = null;
      })
      .addCase(createBooking.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch user bookings
      .addCase(fetchUserBookings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserBookings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.bookings = action.payload;
        state.error = null;
      })
      .addCase(fetchUserBookings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch active booking
      .addCase(fetchActiveBooking.fulfilled, (state, action) => {
        state.activeBooking = action.payload;
      })
      // Extend booking
      .addCase(extendBooking.fulfilled, (state, action) => {
        const updatedBooking = action.payload;
        
        // Update in bookings array
        const bookingIndex = state.bookings.findIndex(b => b.id === updatedBooking.id);
        if (bookingIndex !== -1) {
          state.bookings[bookingIndex] = updatedBooking;
        }
        
        // Update active booking
        if (state.activeBooking?.id === updatedBooking.id) {
          state.activeBooking = updatedBooking;
        }
      })
      // Cancel booking
      .addCase(cancelBooking.fulfilled, (state, action) => {
        const cancelledBooking = action.payload;
        
        // Update in bookings array
        const bookingIndex = state.bookings.findIndex(b => b.id === cancelledBooking.id);
        if (bookingIndex !== -1) {
          state.bookings[bookingIndex] = cancelledBooking;
        }
        
        // Clear active booking if cancelled
        if (state.activeBooking?.id === cancelledBooking.id) {
          state.activeBooking = null;
        }
      })
      // Complete booking
      .addCase(completeBooking.fulfilled, (state, action) => {
        const completedBooking = action.payload;
        
        // Update in bookings array
        const bookingIndex = state.bookings.findIndex(b => b.id === completedBooking.id);
        if (bookingIndex !== -1) {
          state.bookings[bookingIndex] = completedBooking;
        }
        
        // Clear active booking if completed
        if (state.activeBooking?.id === completedBooking.id) {
          state.activeBooking = null;
        }
      });
  },
});

export const { clearError, setActiveBooking, updateBookingStatus } = bookingSlice.actions;
export default bookingSlice.reducer;
