// Core Types for SPark Application

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar?: string;
  user_type: 'driver' | 'host' | 'admin';
  is_verified: boolean;
  created_at: string;
  updated_at: string;
  // Computed properties for backward compatibility
  firstName?: string;
  lastName?: string;
  userType?: 'driver' | 'host' | 'admin';
  isVerified?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface Vehicle {
  id: string;
  userId: string;
  make: string;
  model: string;
  year: number;
  licensePlate: string;
  color: string;
  isDefault: boolean;
}

export interface ParkingSpot {
  id: string;
  hostId: string;
  title: string;
  description: string;
  address: string;
  latitude: number;
  longitude: number;
  hourlyRate: number;
  dailyRate: number;
  monthlyRate?: number;
  amenities: string[];
  photos: string[];
  isActive: boolean;
  availability: AvailabilitySchedule[];
  rating: number;
  reviewCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface AvailabilitySchedule {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  isAvailable: boolean;
}

export interface Booking {
  id: string;
  driverId: string;
  hostId: string;
  spotId: string;
  vehicleId: string;
  startTime: string;
  endTime: string;
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'refunded' | 'failed';
  qrCode?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Review {
  id: string;
  bookingId: string;
  reviewerId: string;
  revieweeId: string;
  rating: number;
  comment: string;
  type: 'driver_to_host' | 'host_to_driver';
  createdAt: string;
}

export interface PaymentMethod {
  id: string;
  userId: string;
  type: 'card' | 'bank_account';
  last4: string;
  brand?: string;
  isDefault: boolean;
  stripePaymentMethodId: string;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'booking' | 'payment' | 'system' | 'promotion';
  isRead: boolean;
  data?: any;
  createdAt: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Search and Filter Types
export interface SearchFilters {
  location?: {
    latitude: number;
    longitude: number;
    radius: number; // in kilometers
  };
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  priceRange?: {
    min: number;
    max: number;
  };
  amenities?: string[];
  sortBy?: 'price' | 'distance' | 'rating';
  sortOrder?: 'asc' | 'desc';
}

// Navigation Types
export type RootStackParamList = {
  '(tabs)': undefined;
  'auth/login': undefined;
  'auth/register': undefined;
  'auth/forgot-password': undefined;
  'booking/details': { bookingId: string };
  'booking/payment': { spotId: string; startTime: string; endTime: string };
  'spot/details': { spotId: string };
  'profile/edit': undefined;
  'profile/vehicles': undefined;
  'profile/payment-methods': undefined;
  'host/create-listing': undefined;
  'host/edit-listing': { spotId: string };
  'host/dashboard': undefined;
  '+not-found': undefined;
};

export type TabParamList = {
  index: undefined;
  search: undefined;
  bookings: undefined;
  profile: undefined;
  host?: undefined;
};

// Redux State Types
export interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

export interface BookingState {
  activeBooking: Booking | null;
  bookings: Booking[];
  isLoading: boolean;
  error: string | null;
}

export interface SpotState {
  spots: ParkingSpot[];
  selectedSpot: ParkingSpot | null;
  searchResults: ParkingSpot[];
  filters: SearchFilters;
  isLoading: boolean;
  error: string | null;
}

export interface RootState {
  auth: AuthState;
  booking: BookingState;
  spot: SpotState;
}
