<?php

echo "🔍 Testing Spot Search\n";
echo "======================\n\n";

function testSearch($url, $description) {
    echo "Testing: $description\n";
    echo "URL: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Status: $httpCode\n";
    
    if ($httpCode == 200) {
        $data = json_decode($response, true);
        if ($data && isset($data['success'])) {
            echo "✅ Success: " . $data['message'] . "\n";
            if (isset($data['data'])) {
                echo "Data count: " . count($data['data']) . "\n";
            }
        } else {
            echo "❌ Invalid response structure\n";
            echo "Response: " . substr($response, 0, 300) . "\n";
        }
    } else {
        echo "❌ HTTP Error\n";
        echo "Response: " . substr($response, 0, 300) . "\n";
    }
    echo "\n";
}

// Test different search variations
testSearch('http://localhost:8000/api/spots/search', 'Basic Search (no params)');
testSearch('http://localhost:8000/api/spots/search?limit=5', 'Search with limit');
testSearch('http://localhost:8000/api/spots/search?latitude=40.7128&longitude=-74.0060', 'Search with location');
testSearch('http://localhost:8000/api/spots/search?latitude=40.7128&longitude=-74.0060&radius=10', 'Search with location and radius');
