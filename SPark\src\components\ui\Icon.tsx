import React from 'react';
import { ViewStyle } from 'react-native';
import { Ionicons, MaterialIcons, Feather, AntDesign, FontAwesome5 } from '@expo/vector-icons';
import { colors } from '../../theme';

export type IconLibrary = 'Ionicons' | 'MaterialIcons' | 'Feather' | 'AntDesign' | 'FontAwesome5';

export interface IconProps {
  name: string;
  size?: number;
  color?: string;
  library?: IconLibrary;
  style?: ViewStyle;
}

const Icon: React.FC<IconProps> = ({
  name,
  size = 24,
  color = colors.black[800],
  library = 'Ionicons',
  style,
}) => {
  const iconProps = {
    name: name as any,
    size,
    color,
    style,
  };

  switch (library) {
    case 'MaterialIcons':
      return <MaterialIcons {...iconProps} />;
    case 'Feather':
      return <Feather {...iconProps} />;
    case 'AntDesign':
      return <AntDesign {...iconProps} />;
    case 'FontAwesome5':
      return <FontAwesome5 {...iconProps} />;
    case 'Ionicons':
    default:
      return <Ionicons {...iconProps} />;
  }
};

// Predefined icon components for common use cases
export const SearchIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="search" library="Ionicons" {...props} />
);

export const LocationIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="location" library="Ionicons" {...props} />
);

export const CarIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="car" library="Ionicons" {...props} />
);

export const StarIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="star" library="Ionicons" {...props} />
);

export const HeartIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="heart" library="Ionicons" {...props} />
);

export const UserIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="person" library="Ionicons" {...props} />
);

export const SettingsIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="settings" library="Ionicons" {...props} />
);

export const NotificationIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="notifications" library="Ionicons" {...props} />
);

export const MenuIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="menu" library="Ionicons" {...props} />
);

export const BackIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="arrow-back" library="Ionicons" {...props} />
);

export const ForwardIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="arrow-forward" library="Ionicons" {...props} />
);

export const CloseIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="close" library="Ionicons" {...props} />
);

export const CheckIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="checkmark" library="Ionicons" {...props} />
);

export const PlusIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="add" library="Ionicons" {...props} />
);

export const MinusIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="remove" library="Ionicons" {...props} />
);

export const EditIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="create" library="Ionicons" {...props} />
);

export const DeleteIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="trash" library="Ionicons" {...props} />
);

export const ShareIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="share" library="Ionicons" {...props} />
);

export const DownloadIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="download" library="Ionicons" {...props} />
);

export const UploadIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="cloud-upload" library="Ionicons" {...props} />
);

export const CameraIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="camera" library="Ionicons" {...props} />
);

export const ImageIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="image" library="Ionicons" {...props} />
);

export const CalendarIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="calendar" library="Ionicons" {...props} />
);

export const ClockIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="time" library="Ionicons" {...props} />
);

export const MapIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="map" library="Ionicons" {...props} />
);

export const FilterIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="filter" library="Ionicons" {...props} />
);

export const SortIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="swap-vertical" library="Ionicons" {...props} />
);

export const RefreshIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="refresh" library="Ionicons" {...props} />
);

export const InfoIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="information-circle" library="Ionicons" {...props} />
);

export const WarningIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="warning" library="Ionicons" {...props} />
);

export const ErrorIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="alert-circle" library="Ionicons" {...props} />
);

export const SuccessIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="checkmark-circle" library="Ionicons" {...props} />
);

// Parking specific icons
export const ParkingIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="car-sport" library="Ionicons" {...props} />
);

export const AvailableIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="checkmark-circle" library="Ionicons" color={colors.success[500]} {...props} />
);

export const OccupiedIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="close-circle" library="Ionicons" color={colors.error[500]} {...props} />
);

export const ReservedIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="time" library="Ionicons" color={colors.warning[500]} {...props} />
);

export const PaymentIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="card" library="Ionicons" {...props} />
);

export const WalletIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="wallet" library="Ionicons" {...props} />
);

export const QRCodeIcon: React.FC<Omit<IconProps, 'name' | 'library'>> = (props) => (
  <Icon name="qr-code" library="Ionicons" {...props} />
);

export default Icon;
