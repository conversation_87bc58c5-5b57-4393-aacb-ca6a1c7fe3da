import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
  interpolate,
  FadeInDown,
  BounceIn,
} from 'react-native-reanimated';
import { ParkingIcon } from './AnimatedIcon';
import { colors, spacing, typography } from '../../theme';

interface AnimatedLoadingProps {
  message?: string;
  color?: string;
  showLogo?: boolean;
  fullScreen?: boolean;
}

const AnimatedLoading: React.FC<AnimatedLoadingProps> = ({
  message = 'Loading...',
  color = colors.primary[500],
  showLogo = true,
  fullScreen = true,
}) => {
  const rotation = useSharedValue(0);
  const scale = useSharedValue(1);
  const opacity = useSharedValue(0.5);

  useEffect(() => {
    // Rotation animation
    rotation.value = withRepeat(
      withTiming(360, { duration: 2000 }),
      -1,
      false
    );

    // Scale animation
    scale.value = withRepeat(
      withSequence(
        withTiming(1.2, { duration: 800 }),
        withTiming(1, { duration: 800 })
      ),
      -1,
      true
    );

    // Opacity animation
    opacity.value = withRepeat(
      withSequence(
        withTiming(1, { duration: 1000 }),
        withTiming(0.5, { duration: 1000 })
      ),
      -1,
      true
    );
  }, []);

  const animatedLogoStyle = useAnimatedStyle(() => ({
    transform: [
      { rotate: `${rotation.value}deg` },
      { scale: scale.value },
    ],
    opacity: opacity.value,
  }));

  const animatedDot1Style = useAnimatedStyle(() => {
    const dotOpacity = interpolate(
      rotation.value,
      [0, 120, 240, 360],
      [1, 0.3, 0.3, 1]
    );
    return { opacity: dotOpacity };
  });

  const animatedDot2Style = useAnimatedStyle(() => {
    const dotOpacity = interpolate(
      rotation.value,
      [0, 120, 240, 360],
      [0.3, 1, 0.3, 0.3]
    );
    return { opacity: dotOpacity };
  });

  const animatedDot3Style = useAnimatedStyle(() => {
    const dotOpacity = interpolate(
      rotation.value,
      [0, 120, 240, 360],
      [0.3, 0.3, 1, 0.3]
    );
    return { opacity: dotOpacity };
  });

  return (
    <View style={[styles.container, !fullScreen && styles.inline]}>
      {showLogo ? (
        <Animated.View 
          style={styles.logoContainer}
          entering={BounceIn.duration(1000)}
        >
          <Animated.View style={animatedLogoStyle}>
            <ParkingIcon 
              size={64} 
              color={color} 
              animationType="pulse"
              trigger={true}
            />
          </Animated.View>
          
          <Animated.View 
            style={styles.dotsContainer}
            entering={FadeInDown.duration(800).delay(500)}
          >
            <Animated.View style={[styles.dot, { backgroundColor: color }, animatedDot1Style]} />
            <Animated.View style={[styles.dot, { backgroundColor: color }, animatedDot2Style]} />
            <Animated.View style={[styles.dot, { backgroundColor: color }, animatedDot3Style]} />
          </Animated.View>
        </Animated.View>
      ) : (
        <Animated.View 
          style={styles.dotsOnlyContainer}
          entering={BounceIn.duration(800)}
        >
          <Animated.View style={[styles.dot, { backgroundColor: color }, animatedDot1Style]} />
          <Animated.View style={[styles.dot, { backgroundColor: color }, animatedDot2Style]} />
          <Animated.View style={[styles.dot, { backgroundColor: color }, animatedDot3Style]} />
        </Animated.View>
      )}
      
      {message && (
        <Animated.Text 
          style={[styles.message, { color: colors.gray[600] }]}
          entering={FadeInDown.duration(800).delay(800)}
        >
          {message}
        </Animated.Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    padding: spacing[4],
  },
  inline: {
    flex: 0,
    padding: spacing[2],
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: spacing[6],
  },
  dotsContainer: {
    flexDirection: 'row',
    marginTop: spacing[4],
    gap: spacing[2],
  },
  dotsOnlyContainer: {
    flexDirection: 'row',
    gap: spacing[2],
    marginBottom: spacing[4],
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  message: {
    marginTop: spacing[3],
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.brand,
    textAlign: 'center',
    fontWeight: '600',
  },
});

export default AnimatedLoading;
