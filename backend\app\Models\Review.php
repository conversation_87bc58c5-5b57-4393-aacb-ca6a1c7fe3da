<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Review extends Model
{
    use HasFactory;

    protected $fillable = [
        'booking_id',
        'spot_id',
        'reviewer_id',
        'reviewee_id',
        'rating',
        'comment',
        'type',
    ];

    protected function casts(): array
    {
        return [
            'rating' => 'integer',
        ];
    }

    /**
     * Relationships
     */
    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }

    public function spot()
    {
        return $this->belongsTo(ParkingSpot::class, 'spot_id');
    }

    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }

    public function reviewee()
    {
        return $this->belongsTo(User::class, 'reviewee_id');
    }

    /**
     * Scopes
     */
    public function scopeForSpot($query, $spotId)
    {
        return $query->where('spot_id', $spotId);
    }

    public function scopeByReviewer($query, $reviewerId)
    {
        return $query->where('reviewer_id', $reviewerId);
    }

    public function scopeForReviewee($query, $revieweeId)
    {
        return $query->where('reviewee_id', $revieweeId);
    }

    public function scopeDriverToHost($query)
    {
        return $query->where('type', 'driver_to_host');
    }

    public function scopeHostToDriver($query)
    {
        return $query->where('type', 'host_to_driver');
    }

    public function scopeHighRated($query, $minRating = 4)
    {
        return $query->where('rating', '>=', $minRating);
    }

    /**
     * Validation rules
     */
    public static function rules()
    {
        return [
            'booking_id' => 'required|exists:bookings,id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
            'type' => 'required|in:driver_to_host,host_to_driver',
        ];
    }
}
