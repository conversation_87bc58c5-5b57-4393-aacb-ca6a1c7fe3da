import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Share,
} from 'react-native';
import Animated, {
  FadeInDown,
  FadeInUp,
} from 'react-native-reanimated';
import { router, useLocalSearchParams } from 'expo-router';
import { useAppDispatch, useAppSelector } from '../../src/store';
import { bookingAPI } from '../../src/services/api';
import { cancelBooking, extendBooking } from '../../src/store/slices/bookingSlice';
import { colors, spacing, typography, borderRadius } from '../../src/theme';
import { Booking } from '../../src/types';

export default function BookingDetailsScreen() {
  const { bookingId } = useLocalSearchParams<{ bookingId: string }>();
  const dispatch = useAppDispatch();
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  
  const [booking, setBooking] = useState<Booking | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/auth/login');
      return;
    }
    
    if (bookingId) {
      loadBookingDetails();
    }
  }, [bookingId, isAuthenticated]);

  const loadBookingDetails = async () => {
    try {
      setIsLoading(true);
      const response = await bookingAPI.getBookingDetails(bookingId!);
      if (response.success && response.data) {
        setBooking(response.data);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load booking details.');
      router.back();
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelBooking = () => {
    if (!booking) return;

    Alert.alert(
      'Cancel Booking',
      'Are you sure you want to cancel this booking? This action cannot be undone.',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              await dispatch(cancelBooking(booking.id)).unwrap();
              Alert.alert('Success', 'Booking cancelled successfully.');
              router.back();
            } catch (error) {
              Alert.alert('Error', 'Failed to cancel booking. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleExtendBooking = () => {
    if (!booking) return;

    Alert.alert(
      'Extend Booking',
      'Extend your parking session by 1 hour?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Extend',
          onPress: async () => {
            try {
              const newEndTime = new Date(new Date(booking.endTime).getTime() + 60 * 60 * 1000).toISOString();
              await dispatch(extendBooking({ bookingId: booking.id, newEndTime })).unwrap();
              Alert.alert('Success', 'Booking extended successfully.');
              loadBookingDetails(); // Refresh data
            } catch (error) {
              Alert.alert('Error', 'Failed to extend booking. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleShareBooking = async () => {
    if (!booking) return;

    try {
      const message = `SPark Booking Details\n\nSpot: Downtown Parking Garage\nDate: ${formatDate(booking.startTime)}\nTime: ${formatTime(booking.startTime)} - ${formatTime(booking.endTime)}\nTotal: $${booking.totalAmount}\nBooking ID: ${booking.id}`;
      
      await Share.share({
        message,
        title: 'SPark Booking Details',
      });
    } catch (error) {
      console.error('Error sharing booking:', error);
    }
  };

  const handleGetDirections = () => {
    // Mock coordinates for demo
    const latitude = 40.7128;
    const longitude = -74.0060;
    const url = `https://maps.google.com/?q=${latitude},${longitude}`;
    
    Alert.alert(
      'Get Directions',
      'Open in your preferred maps app?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open Maps', onPress: () => {
          // In a real app, you would use Linking.openURL(url)
          Alert.alert('Navigation', 'Would open Google Maps with directions.');
        }},
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return colors.success[500];
      case 'confirmed':
        return colors.primary[500];
      case 'pending':
        return colors.warning[500];
      case 'cancelled':
        return colors.error[500];
      case 'completed':
        return colors.gray[500];
      default:
        return colors.gray[500];
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'confirmed':
        return 'Confirmed';
      case 'pending':
        return 'Pending';
      case 'cancelled':
        return 'Cancelled';
      case 'completed':
        return 'Completed';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const calculateDuration = () => {
    if (!booking) return '';
    const start = new Date(booking.startTime);
    const end = new Date(booking.endTime);
    const hours = Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60));
    return `${hours} hour${hours !== 1 ? 's' : ''}`;
  };

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading booking details...</Text>
      </View>
    );
  }

  if (!booking) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Booking not found</Text>
        <Button title="Go Back" onPress={() => router.back()} />
      </View>
    );
  }

  const isActive = booking.status === 'active';
  const isUpcoming = new Date(booking.startTime) > new Date();
  const canCancel = booking.status === 'confirmed' && isUpcoming;
  const canExtend = booking.status === 'active';

  return (
    <ScrollView style={styles.container}>
      <Animated.View
        style={styles.headerCard}
        entering={FadeInDown.duration(800)}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerInfo}>
            <Text style={styles.spotTitle}>Downtown Parking Garage</Text>
            <Text style={styles.spotAddress}>123 Main St, Downtown</Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(booking.status) }]}>
            <Text style={styles.statusText}>{getStatusText(booking.status)}</Text>
          </View>
        </View>
      </Animated.View>

      <Animated.View
        style={styles.detailsCard}
        entering={FadeInUp.duration(800).delay(200)}
      >
        <Text style={styles.sectionTitle}>Booking Details</Text>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Booking ID</Text>
          <Text style={styles.detailValue}>{booking.id}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Date</Text>
          <Text style={styles.detailValue}>{formatDate(booking.startTime)}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Time</Text>
          <Text style={styles.detailValue}>
            {formatTime(booking.startTime)} - {formatTime(booking.endTime)}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Duration</Text>
          <Text style={styles.detailValue}>{calculateDuration()}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Vehicle</Text>
          <Text style={styles.detailValue}>Toyota Camry (ABC-123)</Text>
        </View>
        
        <View style={[styles.detailRow, styles.totalRow]}>
          <Text style={styles.totalLabel}>Total Amount</Text>
          <Text style={styles.totalValue}>${booking.totalAmount}</Text>
        </View>
      </Animated.View>

      {booking.qrCode && (
        <Animated.View
          style={styles.qrCard}
          entering={FadeInUp.duration(800).delay(400)}
        >
          <Text style={styles.sectionTitle}>Access Code</Text>
          <View style={styles.qrContainer}>
            <View style={styles.qrPlaceholder}>
              <Text style={styles.qrText}>QR Code</Text>
              <Text style={styles.qrSubtext}>Show this at the parking entrance</Text>
            </View>
          </View>
        </Animated.View>
      )}

      <Animated.View
        style={styles.actionsCard}
        entering={FadeInUp.duration(800).delay(600)}
      >
        <Text style={styles.sectionTitle}>Actions</Text>

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleGetDirections}
          >
            <Text style={styles.actionButtonText}>Get Directions</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleShareBooking}
          >
            <Text style={styles.actionButtonText}>Share Booking</Text>
          </TouchableOpacity>

          {canExtend && (
            <TouchableOpacity
              style={[styles.actionButton, styles.primaryButton]}
              onPress={handleExtendBooking}
            >
              <Text style={[styles.actionButtonText, styles.primaryButtonText]}>Extend Session</Text>
            </TouchableOpacity>
          )}

          {canCancel && (
            <TouchableOpacity
              style={[styles.actionButton, styles.dangerButton]}
              onPress={handleCancelBooking}
            >
              <Text style={[styles.actionButtonText, styles.dangerButtonText]}>Cancel Booking</Text>
            </TouchableOpacity>
          )}
        </View>
      </Animated.View>

      <Animated.View
        style={styles.supportCard}
        entering={FadeInUp.duration(800).delay(800)}
      >
        <Text style={styles.supportTitle}>Need Help?</Text>
        <Text style={styles.supportText}>
          If you have any issues with your booking, please contact our support team.
        </Text>
        <TouchableOpacity
          style={styles.supportButton}
          onPress={() => Alert.alert('Support', 'Support contact will be available soon.')}
        >
          <Text style={styles.supportButtonText}>Contact Support</Text>
        </TouchableOpacity>
      </Animated.View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
    padding: spacing[4],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
  },
  loadingText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
    padding: spacing[4],
  },
  errorText: {
    fontSize: typography.fontSize.lg,
    color: colors.error[500],
    marginBottom: spacing[4],
  },
  headerCard: {
    marginBottom: spacing[4],
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerInfo: {
    flex: 1,
  },
  spotTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.gray[900],
    marginBottom: spacing[1],
  },
  spotAddress: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  statusBadge: {
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    borderRadius: borderRadius.full,
  },
  statusText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.white,
  },
  detailsCard: {
    marginBottom: spacing[4],
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[4],
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  detailLabel: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  detailValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.gray[900],
    textAlign: 'right',
    flex: 1,
    marginLeft: spacing[2],
  },
  totalRow: {
    borderBottomWidth: 0,
    paddingTop: spacing[4],
    marginTop: spacing[2],
    borderTopWidth: 2,
    borderTopColor: colors.gray[200],
  },
  totalLabel: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
  },
  totalValue: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
  },
  qrCard: {
    marginBottom: spacing[4],
  },
  qrContainer: {
    alignItems: 'center',
  },
  qrPlaceholder: {
    width: 200,
    height: 200,
    backgroundColor: colors.gray[100],
    borderRadius: borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: colors.gray[300],
    borderStyle: 'dashed',
  },
  qrText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[600],
    marginBottom: spacing[1],
  },
  qrSubtext: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
    textAlign: 'center',
  },
  actionsCard: {
    marginBottom: spacing[4],
  },
  actionButtons: {
    gap: spacing[3],
  },
  actionButton: {
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[700],
  },
  primaryButton: {
    backgroundColor: colors.primary[500],
  },
  primaryButtonText: {
    color: colors.black[900],
  },
  dangerButton: {
    backgroundColor: colors.error[500],
  },
  dangerButtonText: {
    color: colors.white,
  },
  supportCard: {
    alignItems: 'center',
    paddingVertical: spacing[6],
    backgroundColor: colors.white,
    borderRadius: 12,
    margin: 20,
    padding: 24,
  },
  supportButton: {
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
  },
  supportButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[700],
  },
  supportTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[2],
  },
  supportText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing[4],
  },
});
