# Testing Guide: OpenStreetMap Parking Feature

This guide will help you test the new parking location feature that uses OpenStreetMap data.

## Prerequisites

1. **Device Setup**
   - Physical device or simulator with location services
   - Internet connection for API calls
   - Location permissions enabled

2. **App Setup**
   - Run `npm install` to ensure all dependencies are installed
   - Start the development server: `npm start`
   - Launch on your preferred platform (iOS/Android)

## Testing Steps

### 1. Basic Functionality Test

1. **Launch the App**
   ```bash
   npm start
   # Then press 'i' for iOS or 'a' for Android
   ```

2. **Navigate to Find Parking**
   - Open the app
   - Go to the "Search" tab (bottom navigation)
   - Tap "Find Nearby (OSM)" button
   - OR go directly to the Find Parking screen

3. **Grant Location Permission**
   - When prompted, allow location access
   - The app needs this to find nearby parking

4. **Search for Parking**
   - Tap the "Find Nearby Parking" button
   - Wait for the search animation to complete
   - A bottom sheet should slide up with results

### 2. UI/UX Testing

1. **Bottom Sheet Interaction**
   - ✅ Bottom sheet slides up from bottom
   - ✅ Shows parking count in header
   - ✅ Tap expand/collapse button works
   - ✅ Tap close button dismisses sheet
   - ✅ Tap outside overlay closes sheet

2. **Parking Spot Cards**
   - ✅ Each spot shows: name, address, distance, rating
   - ✅ Tap a spot to expand details
   - ✅ Details show: type, capacity, fee status, amenities
   - ✅ "Get Directions" button opens maps selection

3. **Map Integration**
   - ✅ Parking spots appear as markers on map
   - ✅ Markers are color-coded by type
   - ✅ Search radius circle is visible
   - ✅ Map zooms to fit all results

### 3. Data Accuracy Testing

1. **Location Verification**
   - Check if your current location is accurate
   - Verify search radius matches expectation (1km default)
   - Confirm distances are calculated correctly

2. **Parking Data Quality**
   - Spot names should be meaningful
   - Addresses should be formatted properly
   - Types should be: surface, underground, multi-storey, street_side
   - Fee status should show "Free" or "Paid"

3. **Maps Integration**
   - Tap "Get Directions" on any spot
   - Choose Google Maps or Apple Maps
   - Verify correct coordinates are passed
   - Check if location name is preserved

### 4. Error Handling Testing

1. **No Location Permission**
   - Deny location permission
   - App should show appropriate error message
   - Should not crash or freeze

2. **No Internet Connection**
   - Turn off WiFi and mobile data
   - Try searching for parking
   - Should show cached results if available
   - Should show appropriate offline message

3. **No Results Found**
   - Test in an area with no parking (rural area)
   - Should show "No parking found" message
   - Should suggest expanding search radius

### 5. Performance Testing

1. **Loading Times**
   - Search should complete within 10-15 seconds
   - Loading animation should be smooth
   - No UI freezing during API calls

2. **Memory Usage**
   - Monitor app memory usage
   - Should not increase significantly after searches
   - No memory leaks after multiple searches

3. **Battery Impact**
   - Location services should stop after search
   - No continuous GPS usage
   - Reasonable battery consumption

## Expected Results

### Successful Test Scenarios

1. **San Francisco Area** (37.7749, -122.4194)
   - Should find 50+ parking spots
   - Mix of surface, underground, and street parking
   - Various fee structures (free and paid)

2. **New York City** (40.7128, -74.0060)
   - Should find many street parking spots
   - Mostly paid parking
   - High density of results

3. **London, UK** (51.5074, -0.1278)
   - Good coverage of parking data
   - Mix of public and private parking
   - Various amenities available

### Common Issues & Solutions

1. **"No parking found"**
   - Try a different location (city center)
   - Increase search radius
   - Check internet connection

2. **Gesture handler warnings**
   - App uses fallback SimpleParkingBottomSheet
   - Functionality should work normally
   - UI might be slightly different

3. **Slow API responses**
   - Overpass API can be slow during peak times
   - Results are cached for 30 minutes
   - Offline fallback available

4. **Inaccurate data**
   - OpenStreetMap data quality varies by region
   - Some spots might have incomplete information
   - This is expected behavior

## Debug Information

### Console Logs to Monitor

```
✅ API Response received
📊 Found X parking elements
🅿️ Sample parking spots:
⚠️ Using cached data
❌ Error finding parking:
```

### Network Requests

- Monitor requests to `overpass-api.de`
- Check for proper User-Agent header
- Verify timeout handling (10 seconds)

### Cache Behavior

- First search: Fresh API call
- Subsequent searches: Cached results (if within 30 minutes)
- Offline: Stale cache used

## Reporting Issues

When reporting issues, please include:

1. **Device Information**
   - Platform (iOS/Android)
   - Device model
   - OS version

2. **Location Details**
   - Approximate location tested
   - Expected vs actual results
   - Screenshots if applicable

3. **Console Logs**
   - Any error messages
   - Network request details
   - Performance metrics

4. **Steps to Reproduce**
   - Exact steps taken
   - Expected behavior
   - Actual behavior

## Success Criteria

The feature is working correctly if:

- ✅ Location permission is properly requested
- ✅ API calls complete successfully
- ✅ Results are displayed in bottom sheet
- ✅ Map markers appear correctly
- ✅ Distance calculations are accurate
- ✅ Maps integration works
- ✅ Offline caching functions
- ✅ Error handling is graceful
- ✅ UI is responsive and smooth

## Next Steps

After successful testing:

1. **User Feedback**
   - Gather feedback on UI/UX
   - Note any missing features
   - Identify improvement opportunities

2. **Performance Optimization**
   - Monitor API response times
   - Optimize data processing
   - Improve caching strategy

3. **Feature Enhancements**
   - Add more parking amenities
   - Implement user reviews
   - Add booking integration
   - Enhance offline capabilities
