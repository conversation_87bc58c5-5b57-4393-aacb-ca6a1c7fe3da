<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\SpotController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\VehicleController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\TestController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Test routes
Route::get('test', [TestController::class, 'test']);

// Public routes
Route::prefix('auth')->group(function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);
    Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('reset-password', [AuthController::class, 'resetPassword']);
});

// Public spot routes (for searching)
Route::get('spots/search', [SpotController::class, 'search']);
Route::get('spots/nearby', [SpotController::class, 'nearby']);
Route::get('spots/{id}', [SpotController::class, 'show']);

// Protected routes
Route::middleware('auth:api')->group(function () {

    // Auth routes
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('profile', [AuthController::class, 'profile']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
        Route::post('refresh', [AuthController::class, 'refresh']);
    });

    // Spot management routes
    Route::prefix('spots')->group(function () {
        Route::post('/', [SpotController::class, 'store']);
        Route::put('{id}', [SpotController::class, 'update']);
        Route::delete('{id}', [SpotController::class, 'destroy']);
        Route::get('host', [SpotController::class, 'hostSpots']);
        Route::put('{id}/availability', [SpotController::class, 'toggleAvailability']);
        Route::post('{id}/images', [SpotController::class, 'uploadImages']);
    });

    // Booking routes
    Route::prefix('bookings')->group(function () {
        Route::get('/', [BookingController::class, 'index']);
        Route::post('/', [BookingController::class, 'store']);
        Route::get('active', [BookingController::class, 'getActiveBooking']);
        Route::get('host', [BookingController::class, 'hostBookings']);
        Route::get('{id}', [BookingController::class, 'show']);
        Route::put('{id}/extend', [BookingController::class, 'extend']);
        Route::put('{id}/cancel', [BookingController::class, 'cancel']);
        Route::put('{id}/complete', [BookingController::class, 'complete']);
    });

    // Vehicle routes
    Route::prefix('vehicles')->group(function () {
        Route::get('/', [VehicleController::class, 'index']);
        Route::post('/', [VehicleController::class, 'store']);
        Route::put('{id}', [VehicleController::class, 'update']);
        Route::delete('{id}', [VehicleController::class, 'destroy']);
        Route::put('{id}/default', [VehicleController::class, 'setDefault']);
    });

    // Payment routes
    Route::prefix('payment')->group(function () {
        Route::get('methods', [PaymentController::class, 'getMethods']);
        Route::post('methods', [PaymentController::class, 'addMethod']);
        Route::delete('methods/{id}', [PaymentController::class, 'deleteMethod']);
        Route::put('methods/{id}/default', [PaymentController::class, 'setDefaultMethod']);
        Route::post('process', [PaymentController::class, 'processPayment']);
        Route::get('history', [PaymentController::class, 'getHistory']);
    });

    // Review routes
    Route::prefix('reviews')->group(function () {
        Route::get('/', [ReviewController::class, 'index']);
        Route::post('/', [ReviewController::class, 'store']);
        Route::get('{id}', [ReviewController::class, 'show']);
        Route::put('{id}', [ReviewController::class, 'update']);
        Route::delete('{id}', [ReviewController::class, 'destroy']);
    });

    // Notification routes
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::put('{id}/read', [NotificationController::class, 'markAsRead']);
        Route::put('read-all', [NotificationController::class, 'markAllAsRead']);
    });
});
