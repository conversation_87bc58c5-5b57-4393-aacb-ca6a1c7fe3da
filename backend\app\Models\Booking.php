<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Booking extends Model
{
    use HasFactory;

    protected $fillable = [
        'driver_id',
        'host_id',
        'spot_id',
        'vehicle_id',
        'start_time',
        'end_time',
        'total_amount',
        'status',
        'payment_status',
        'qr_code',
        'payment_intent_id',
        'cancellation_reason',
    ];

    protected function casts(): array
    {
        return [
            'start_time' => 'datetime',
            'end_time' => 'datetime',
            'total_amount' => 'decimal:2',
        ];
    }

    /**
     * Relationships
     */
    public function driver()
    {
        return $this->belongsTo(User::class, 'driver_id');
    }

    public function host()
    {
        return $this->belongsTo(User::class, 'host_id');
    }

    public function spot()
    {
        return $this->belongsTo(ParkingSpot::class, 'spot_id');
    }

    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class, 'vehicle_id');
    }

    public function review()
    {
        return $this->hasOne(Review::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeForDriver($query, $driverId)
    {
        return $query->where('driver_id', $driverId);
    }

    public function scopeForHost($query, $hostId)
    {
        return $query->where('host_id', $hostId);
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_time', '>', now())
                    ->whereIn('status', ['confirmed', 'pending']);
    }

    public function scopeCurrent($query)
    {
        return $query->where('start_time', '<=', now())
                    ->where('end_time', '>', now())
                    ->where('status', 'active');
    }

    public function scopePast($query)
    {
        return $query->where('end_time', '<', now())
                    ->whereIn('status', ['completed', 'cancelled']);
    }

    /**
     * Accessors
     */
    public function getDurationInHoursAttribute()
    {
        return $this->start_time->diffInHours($this->end_time);
    }

    public function getDurationInMinutesAttribute()
    {
        return $this->start_time->diffInMinutes($this->end_time);
    }

    public function getIsActiveAttribute()
    {
        return $this->status === 'active' && 
               $this->start_time <= now() && 
               $this->end_time > now();
    }

    public function getIsUpcomingAttribute()
    {
        return $this->start_time > now() && 
               in_array($this->status, ['confirmed', 'pending']);
    }

    public function getIsPastAttribute()
    {
        return $this->end_time < now() || 
               in_array($this->status, ['completed', 'cancelled']);
    }

    public function getCanBeCancelledAttribute()
    {
        return in_array($this->status, ['pending', 'confirmed']) && 
               $this->start_time > now()->addHours(1); // Can cancel up to 1 hour before
    }

    public function getCanBeExtendedAttribute()
    {
        return $this->status === 'active' && 
               $this->end_time > now();
    }

    /**
     * Helper methods
     */
    public function calculateTotalAmount()
    {
        $hours = $this->getDurationInHoursAttribute();
        $rate = $this->spot->hourly_rate;
        
        // Apply daily rate if booking is for 8+ hours
        if ($hours >= 8 && $this->spot->daily_rate) {
            $days = ceil($hours / 24);
            return $days * $this->spot->daily_rate;
        }
        
        return $hours * $rate;
    }

    public function generateQRCode()
    {
        // Generate a unique QR code for the booking
        return 'SPARK_' . $this->id . '_' . time();
    }

    public function canExtendTo($newEndTime)
    {
        $newEndTime = Carbon::parse($newEndTime);
        
        // Check if spot is available for the extended period
        return !$this->spot->hasConflictingBooking(
            $this->end_time, 
            $newEndTime, 
            $this->id
        );
    }

    public function extend($newEndTime)
    {
        if (!$this->canExtendTo($newEndTime)) {
            throw new \Exception('Cannot extend booking due to conflicting reservations');
        }

        $oldEndTime = $this->end_time;
        $this->end_time = Carbon::parse($newEndTime);
        
        // Calculate additional amount
        $additionalHours = $oldEndTime->diffInHours($this->end_time);
        $additionalAmount = $additionalHours * $this->spot->hourly_rate;
        $this->total_amount += $additionalAmount;
        
        $this->save();
        
        return $additionalAmount;
    }

    public function cancel($reason = null)
    {
        $this->status = 'cancelled';
        $this->cancellation_reason = $reason;
        $this->save();
    }

    public function complete()
    {
        $this->status = 'completed';
        $this->save();
    }
}
