<?php

namespace App\Http\Controllers;

use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class VehicleController extends Controller
{
    /**
     * Get user's vehicles
     */
    public function index()
    {
        try {
            $vehicles = Vehicle::forUser(auth()->id())
                ->orderBy('is_default', 'desc')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $vehicles
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get vehicles',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add a new vehicle
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'make' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'license_plate' => 'required|string|max:20|unique:vehicles',
            'color' => 'required|string|max:50',
            'is_default' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $vehicle = Vehicle::create([
                'user_id' => auth()->id(),
                'make' => $request->make,
                'model' => $request->model,
                'year' => $request->year,
                'license_plate' => strtoupper($request->license_plate),
                'color' => $request->color,
                'is_default' => $request->get('is_default', false),
            ]);

            // If this should be the default vehicle, update others
            if ($request->get('is_default', false)) {
                $vehicle->setAsDefault();
            }

            return response()->json([
                'success' => true,
                'message' => 'Vehicle added successfully',
                'data' => $vehicle
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add vehicle',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update vehicle
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'make' => 'sometimes|required|string|max:255',
            'model' => 'sometimes|required|string|max:255',
            'year' => 'sometimes|required|integer|min:1900|max:' . (date('Y') + 1),
            'license_plate' => 'sometimes|required|string|max:20|unique:vehicles,license_plate,' . $id,
            'color' => 'sometimes|required|string|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $vehicle = Vehicle::where('user_id', auth()->id())
                ->findOrFail($id);

            $updateData = $request->only(['make', 'model', 'year', 'color']);
            
            if ($request->has('license_plate')) {
                $updateData['license_plate'] = strtoupper($request->license_plate);
            }

            $vehicle->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Vehicle updated successfully',
                'data' => $vehicle
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update vehicle',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete vehicle
     */
    public function destroy($id)
    {
        try {
            $vehicle = Vehicle::where('user_id', auth()->id())
                ->findOrFail($id);

            // Check if vehicle has active bookings
            $activeBookings = $vehicle->bookings()
                ->whereIn('status', ['pending', 'confirmed', 'active'])
                ->count();

            if ($activeBookings > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete vehicle with active bookings'
                ], 409);
            }

            $vehicle->delete();

            return response()->json([
                'success' => true,
                'message' => 'Vehicle deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete vehicle',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Set vehicle as default
     */
    public function setDefault($id)
    {
        try {
            $vehicle = Vehicle::where('user_id', auth()->id())
                ->findOrFail($id);

            $vehicle->setAsDefault();

            return response()->json([
                'success' => true,
                'message' => 'Default vehicle updated successfully',
                'data' => $vehicle
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to set default vehicle',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
