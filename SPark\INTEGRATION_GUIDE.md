# SPark Frontend-Backend Integration Guide

## 🎯 Overview

This guide will help you test and verify the integration between your React Native frontend and <PERSON><PERSON> backend.

## ✅ Prerequisites

Before testing the integration, ensure:

1. **<PERSON>vel Backend is Running**
   ```bash
   cd backend
   php artisan serve
   # Should be running on http://localhost:8000
   ```

2. **Database is Seeded**
   ```bash
   cd backend
   php artisan migrate
   php artisan db:seed
   ```

3. **React Native Development Environment**
   - Metro bundler is running
   - Expo or React Native CLI is set up
   - Device/emulator can access localhost:8000

## 🔧 Integration Changes Made

### 1. API Configuration Updated
- **Base URL**: Changed from `localhost:3000` to `localhost:8000`
- **Response Handling**: Updated to handle <PERSON><PERSON>'s response structure
- **Error Handling**: Enhanced to work with <PERSON><PERSON> error responses

### 2. API Method Mappings
- **Registration**: Maps frontend fields to <PERSON><PERSON>'s snake_case format
- **Spot Search**: Changed from POST to GET with query parameters
- **Booking**: Maps camelCase to snake_case field names
- **Error Responses**: <PERSON>les <PERSON>'s `{success, message, data}` structure

### 3. Type Definitions
- **User Interface**: Updated to include <PERSON><PERSON>'s snake_case fields
- **Backward Compatibility**: Maintained camelCase properties for existing code

## 🧪 Testing the Integration

### Method 1: Using the Integration Test Component

1. **Add the test component to your app:**
   ```typescript
   // In your main App.tsx or a test screen
   import IntegrationTest from './src/components/IntegrationTest';
   
   // Add to your navigation or render directly
   <IntegrationTest />
   ```

2. **Run the tests:**
   - Tap "Run Integration Tests" button
   - Watch each test execute in real-time
   - Green checkmarks = success, red X = failure
   - Tap on test items to see detailed results

### Method 2: Manual API Testing

1. **Test Authentication:**
   ```typescript
   import { authAPI } from './src/services/api';
   
   // Test login
   const loginResult = await authAPI.login({
     email: '<EMAIL>',
     password: 'password'
   });
   console.log('Login result:', loginResult);
   ```

2. **Test Spot Search:**
   ```typescript
   import { spotAPI } from './src/services/api';
   
   // Test search
   const searchResult = await spotAPI.searchSpots({
     latitude: 40.7128,
     longitude: -74.0060,
     radius: 10
   });
   console.log('Search result:', searchResult);
   ```

### Method 3: Using the Test Script

1. **Import and run the test script:**
   ```typescript
   import { runIntegrationTests } from './src/tests/integration.test';
   
   // Run in a useEffect or button press
   runIntegrationTests();
   ```

## 🔍 Expected Test Results

### ✅ Successful Integration
When everything is working correctly, you should see:

```
🚀 Starting Frontend-Backend Integration Tests...

1. Testing User Registration...
✅ Registration successful
   User: Test User
   Token: eyJ0eXAiOiJKV1QiLCJh...

2. Testing User Login...
✅ Login successful
   User: John Driver
   Token: eyJ0eXAiOiJKV1QiLCJh...

3. Testing Spot Search...
✅ Spot search successful
   Found 3 parking spots
   First spot: Downtown Parking Spot

4. Testing Nearby Spots...
✅ Nearby spots retrieval successful
   Found 3 nearby spots

5. Testing Get User Vehicles...
✅ Vehicles retrieval successful
   Found 1 vehicles
   First vehicle: Toyota Camry

🎉 All integration tests passed!
```

## 🚨 Troubleshooting

### Common Issues and Solutions

#### 1. Connection Refused / Network Error
**Problem**: Cannot connect to backend
**Solutions**:
- Ensure Laravel server is running: `php artisan serve`
- Check if port 8000 is accessible
- For physical devices, use your computer's IP instead of localhost
- Update API_BASE_URL to use your IP: `http://*************:8000/api`

#### 2. 404 Not Found Errors
**Problem**: API routes not found
**Solutions**:
- Verify `api` routes are registered in `bootstrap/app.php`
- Check route list: `php artisan route:list`
- Ensure API routes file exists: `routes/api.php`

#### 3. 401 Unauthorized Errors
**Problem**: Authentication issues
**Solutions**:
- Check JWT configuration in Laravel
- Verify JWT secret is set: `php artisan jwt:secret`
- Ensure auth guard is set to 'api' in routes

#### 4. 500 Internal Server Errors
**Problem**: Backend errors
**Solutions**:
- Check Laravel logs: `storage/logs/laravel.log`
- Verify database connection
- Run migrations: `php artisan migrate`
- Seed test data: `php artisan db:seed`

#### 5. CORS Issues
**Problem**: Cross-origin request blocked
**Solutions**:
- Check CORS configuration in `config/cors.php`
- Ensure React Native origin is allowed
- For Expo: Add expo development URLs to allowed origins

## 📱 Device-Specific Configuration

### For Physical Devices
Update the API base URL to use your computer's IP:

```typescript
// In src/services/api/index.ts
const API_BASE_URL = __DEV__ 
  ? 'http://*************:8000/api'  // Replace with your IP
  : 'https://api.spark-parking.com/api';
```

### For Expo Development
Expo automatically handles localhost forwarding, but if you have issues:

```typescript
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:8000/api'  // Should work with Expo
  : 'https://api.spark-parking.com/api';
```

## 🎯 Next Steps

Once integration tests pass:

1. **Test Core Features**:
   - User registration and login flows
   - Parking spot search and booking
   - Vehicle management
   - Payment processing (if Stripe is configured)

2. **Performance Testing**:
   - Test with larger datasets
   - Check response times
   - Monitor memory usage

3. **Error Handling**:
   - Test offline scenarios
   - Test invalid data inputs
   - Test network timeouts

4. **Production Preparation**:
   - Update production API URL
   - Configure SSL certificates
   - Set up proper error logging
   - Implement analytics

## 📞 Support

If you encounter issues:

1. Check the Laravel logs: `backend/storage/logs/laravel.log`
2. Check React Native Metro logs
3. Verify network connectivity
4. Test API endpoints directly with curl or Postman
5. Review the integration test results for specific error messages

---

**🎉 Congratulations!** Once all tests pass, your SPark app is successfully integrated and ready for development and testing!
