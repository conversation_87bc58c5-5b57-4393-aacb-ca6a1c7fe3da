import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { useAppDispatch, useAppSelector } from '../../src/store';
import { authAPI } from '../../src/services/api';
import MobileInput from '../../src/components/ui/MobileInput';
import { colors, spacing, typography } from '../../src/theme';

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [emailError, setEmailError] = useState('');

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSendResetEmail = async () => {
    // Clear previous errors
    setEmailError('');

    // Validation
    if (!email.trim()) {
      setEmailError('Email is required');
      return;
    }

    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);

    try {
      await authAPI.forgotPassword(email.trim());
      setEmailSent(true);
      Alert.alert(
        'Reset Email Sent',
        'We\'ve sent a password reset link to your email address. Please check your inbox and follow the instructions.',
        [{ text: 'OK' }]
      );
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to send reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    router.back();
  };

  const handleResendEmail = () => {
    setEmailSent(false);
    handleSendResetEmail();
  };

  if (emailSent) {
    return (
      <View style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Text style={styles.title}>Check Your Email</Text>
            <Text style={styles.subtitle}>
              We've sent a password reset link to {email}
            </Text>
          </View>

          <Card style={styles.successCard}>
            <View style={styles.successIcon}>
              <Text style={styles.successIconText}>📧</Text>
            </View>
            
            <Text style={styles.successTitle}>Email Sent Successfully</Text>
            <Text style={styles.successMessage}>
              Please check your email and click the reset link to create a new password.
            </Text>
            
            <Text style={styles.instructionText}>
              Didn't receive the email? Check your spam folder or try again.
            </Text>

            <Button
              title="Resend Email"
              onPress={handleResendEmail}
              variant="outline"
              fullWidth
              style={styles.resendButton}
            />

            <Button
              title="Back to Sign In"
              onPress={handleBackToLogin}
              fullWidth
              style={styles.backButton}
            />
          </Card>
        </ScrollView>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Text style={styles.title}>Reset Password</Text>
          <Text style={styles.subtitle}>
            Enter your email address and we'll send you a link to reset your password
          </Text>
        </View>

        <Card style={styles.formCard}>
          <Text style={styles.formTitle}>Forgot Password</Text>
          
          <Input
            label="Email Address"
            value={email}
            onChangeText={setEmail}
            placeholder="Enter your email address"
            keyboardType="email-address"
            autoCapitalize="none"
            autoCorrect={false}
            error={emailError}
            required
          />

          <Button
            title="Send Reset Link"
            onPress={handleSendResetEmail}
            loading={isLoading}
            fullWidth
            style={styles.sendButton}
          />

          <Button
            title="Back to Sign In"
            onPress={handleBackToLogin}
            variant="ghost"
            fullWidth
            style={styles.backToLoginButton}
          />
        </Card>

        <View style={styles.helpSection}>
          <Text style={styles.helpTitle}>Need Help?</Text>
          <Text style={styles.helpText}>
            If you're having trouble resetting your password, please contact our support team.
          </Text>
          <Button
            title="Contact Support"
            onPress={() => {
              Alert.alert('Contact Support', 'Support contact will be available soon.');
            }}
            variant="ghost"
            style={styles.supportButton}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    flexGrow: 1,
    padding: spacing[4],
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing[8],
  },
  title: {
    fontSize: typography.fontSize['3xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  subtitle: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: 24,
  },
  formCard: {
    marginBottom: spacing[6],
  },
  formTitle: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[6],
    textAlign: 'center',
  },
  sendButton: {
    marginTop: spacing[2],
  },
  backToLoginButton: {
    marginTop: spacing[4],
  },
  successCard: {
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  successIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.success[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing[4],
  },
  successIconText: {
    fontSize: 40,
  },
  successTitle: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.gray[900],
    marginBottom: spacing[3],
    textAlign: 'center',
  },
  successMessage: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: spacing[4],
  },
  instructionText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[500],
    textAlign: 'center',
    marginBottom: spacing[6],
  },
  resendButton: {
    marginBottom: spacing[3],
  },
  backButton: {
    marginTop: spacing[2],
  },
  helpSection: {
    alignItems: 'center',
    paddingTop: spacing[4],
  },
  helpTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[2],
  },
  helpText: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing[3],
  },
  supportButton: {
    paddingHorizontal: 0,
  },
});
