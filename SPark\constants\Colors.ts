/**
 * Modern Yellow, Black & White Color Scheme for SPark
 * Designed for optimal contrast, readability, and modern aesthetics
 */

const tintColorLight = '#FFD700'; // Golden yellow
const tintColorDark = '#FFD700';  // Golden yellow

export const Colors = {
  light: {
    text: '#000000',
    textSecondary: '#1A1A1A',
    textMuted: '#6B7280',
    background: '#FFFFFF',
    backgroundSecondary: '#F9FAFB',
    tint: tintColorLight,
    primary: '#FFD700',
    primaryHover: '#F59E0B',
    primaryActive: '#D97706',
    surface: '#FFFFFF',
    card: '#FFFFFF',
    border: '#E5E7EB',
    icon: '#1A1A1A',
    iconSecondary: '#6B7280',
    tabIconDefault: '#6B7280',
    tabIconSelected: tintColorLight,
    success: '#22C55E',
    warning: '#F97316',
    error: '#EF4444',
    overlay: 'rgba(0, 0, 0, 0.5)',
  },
  dark: {
    text: '#FFFFFF',
    textSecondary: '#E5E7EB',
    textMuted: '#9CA3AF',
    background: '#000000',
    backgroundSecondary: '#1A1A1A',
    tint: tintColorDark,
    primary: '#FFD700',
    primaryHover: '#FFC107',
    primaryActive: '#FF9800',
    surface: '#1A1A1A',
    card: '#1A1A1A',
    border: '#374151',
    icon: '#FFFFFF',
    iconSecondary: '#9CA3AF',
    tabIconDefault: '#9CA3AF',
    tabIconSelected: tintColorDark,
    success: '#4ADE80',
    warning: '#FB923C',
    error: '#F87171',
    overlay: 'rgba(0, 0, 0, 0.7)',
  },
};
