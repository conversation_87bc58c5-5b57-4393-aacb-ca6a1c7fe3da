<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'message',
        'type',
        'is_read',
        'data',
    ];

    protected function casts(): array
    {
        return [
            'is_read' => 'boolean',
            'data' => 'array',
        ];
    }

    /**
     * Relationships
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scopes
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeBookingNotifications($query)
    {
        return $query->where('type', 'booking');
    }

    public function scopePaymentNotifications($query)
    {
        return $query->where('type', 'payment');
    }

    public function scopeSystemNotifications($query)
    {
        return $query->where('type', 'system');
    }

    public function scopePromotionNotifications($query)
    {
        return $query->where('type', 'promotion');
    }

    /**
     * Helper methods
     */
    public function markAsRead()
    {
        $this->is_read = true;
        $this->save();
    }

    public function markAsUnread()
    {
        $this->is_read = false;
        $this->save();
    }

    /**
     * Static helper methods for creating notifications
     */
    public static function createBookingNotification($userId, $title, $message, $data = [])
    {
        return self::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => 'booking',
            'data' => $data,
        ]);
    }

    public static function createPaymentNotification($userId, $title, $message, $data = [])
    {
        return self::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => 'payment',
            'data' => $data,
        ]);
    }

    public static function createSystemNotification($userId, $title, $message, $data = [])
    {
        return self::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => 'system',
            'data' => $data,
        ]);
    }

    public static function createPromotionNotification($userId, $title, $message, $data = [])
    {
        return self::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => 'promotion',
            'data' => $data,
        ]);
    }
}
