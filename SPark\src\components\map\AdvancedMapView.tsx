import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Alert,
  Dimensions,
} from 'react-native';
import {
  PlatformMapView as MapView,
  PlatformMarker as Marker,
  PLATFORM_PROVIDER_GOOGLE as PROVIDER_GOOGLE
} from '../PlatformMapView';

// Define types that might not be available on web
interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

// Mock components for web compatibility
const Callout = ({ children }: { children: React.ReactNode }) => <>{children}</>;
const Circle = (props: any) => null;
const Polyline = (props: any) => null;
import * as Location from 'expo-location';
import { useWebSocket } from '../../services/websocket';

interface ParkingSpot {
  id: string;
  title: string;
  latitude: number;
  longitude: number;
  isAvailable: boolean;
  price: number;
  rating: number;
  distance?: number;
}

interface AdvancedMapViewProps {
  spots: ParkingSpot[];
  onSpotPress: (spotId: string) => void;
  onRegionChange?: (region: Region) => void;
  showUserLocation?: boolean;
  showTraffic?: boolean;
  showDirections?: boolean;
  selectedSpotId?: string;
  searchRadius?: number;
}

const { width, height } = Dimensions.get('window');

export default function AdvancedMapView({
  spots,
  onSpotPress,
  onRegionChange,
  showUserLocation = true,
  showTraffic = false,
  showDirections = false,
  selectedSpotId,
  searchRadius = 1000, // meters
}: AdvancedMapViewProps) {
  const mapRef = useRef<MapView>(null);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [region, setRegion] = useState<Region>({
    latitude: 37.7749,
    longitude: -122.4194,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });
  const [directions, setDirections] = useState<any[]>([]);
  const [isFollowingUser, setIsFollowingUser] = useState(false);
  const webSocket = useWebSocket();

  useEffect(() => {
    getCurrentLocation();
    setupWebSocketListeners();
    
    return () => {
      webSocket.off('spotUpdate', handleSpotUpdate);
    };
  }, []);

  useEffect(() => {
    if (spots.length > 0) {
      fitToSpots();
    }
  }, [spots]);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Location permission is required for map features.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      setUserLocation(location);
      
      const newRegion = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
      
      setRegion(newRegion);
      
      if (mapRef.current) {
        mapRef.current.animateToRegion(newRegion, 1000);
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const setupWebSocketListeners = () => {
    webSocket.on('spotUpdate', handleSpotUpdate);
  };

  const handleSpotUpdate = (update: any) => {
    // Handle real-time spot updates
    console.log('Spot update received:', update);
    // This would trigger a re-render with updated spot data
  };

  const fitToSpots = () => {
    if (spots.length === 0 || !mapRef.current) return;

    const coordinates = spots.map(spot => ({
      latitude: spot.latitude,
      longitude: spot.longitude,
    }));

    if (userLocation) {
      coordinates.push({
        latitude: userLocation.coords.latitude,
        longitude: userLocation.coords.longitude,
      });
    }

    mapRef.current.fitToCoordinates(coordinates, {
      edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
      animated: true,
    });
  };

  const centerOnUser = async () => {
    if (!userLocation) {
      await getCurrentLocation();
      return;
    }

    const newRegion = {
      latitude: userLocation.coords.latitude,
      longitude: userLocation.coords.longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };

    setRegion(newRegion);
    
    if (mapRef.current) {
      mapRef.current.animateToRegion(newRegion, 1000);
    }
    
    setIsFollowingUser(true);
  };

  const getDirections = async (destinationSpot: ParkingSpot) => {
    if (!userLocation) {
      Alert.alert('Location Required', 'Current location is required for directions.');
      return;
    }

    try {
      // In a real app, you'd use Google Directions API or similar
      // For demo, we'll create a simple straight line
      const directionsCoords = [
        {
          latitude: userLocation.coords.latitude,
          longitude: userLocation.coords.longitude,
        },
        {
          latitude: destinationSpot.latitude,
          longitude: destinationSpot.longitude,
        },
      ];

      setDirections(directionsCoords);
    } catch (error) {
      console.error('Error getting directions:', error);
      Alert.alert('Error', 'Failed to get directions');
    }
  };

  const openInMaps = (spot: ParkingSpot) => {
    const url = `https://maps.google.com/?q=${spot.latitude},${spot.longitude}`;
    Alert.alert(
      'Open in Maps',
      'Would you like to open this location in your maps app?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open', onPress: () => console.log('Open in maps:', url) },
      ]
    );
  };

  const handleRegionChangeComplete = (newRegion: Region) => {
    setRegion(newRegion);
    setIsFollowingUser(false);
    onRegionChange?.(newRegion);
  };

  const getMarkerColor = (spot: ParkingSpot) => {
    if (spot.id === selectedSpotId) return '#1a9bff';
    return spot.isAvailable ? '#10b981' : '#ef4444';
  };

  const renderSpotMarker = (spot: ParkingSpot) => (
    <Marker
      key={spot.id}
      coordinate={{
        latitude: spot.latitude,
        longitude: spot.longitude,
      }}
      pinColor={getMarkerColor(spot)}
      onPress={() => onSpotPress(spot.id)}
    >
      <Callout onPress={() => onSpotPress(spot.id)}>
        <View style={styles.calloutContainer}>
          <Text style={styles.calloutTitle}>{spot.title}</Text>
          <Text style={styles.calloutPrice}>${spot.price}/hour</Text>
          <Text style={styles.calloutRating}>★ {spot.rating.toFixed(1)}</Text>
          <Text style={[
            styles.calloutStatus,
            { color: spot.isAvailable ? '#10b981' : '#ef4444' }
          ]}>
            {spot.isAvailable ? 'Available' : 'Occupied'}
          </Text>
          <TouchableOpacity
            style={styles.directionsButton}
            onPress={() => getDirections(spot)}
          >
            <Text style={styles.directionsButtonText}>Get Directions</Text>
          </TouchableOpacity>
        </View>
      </Callout>
    </Marker>
  );

  const renderSearchRadius = () => {
    if (!userLocation || !searchRadius) return null;

    return (
      <Circle
        center={{
          latitude: userLocation.coords.latitude,
          longitude: userLocation.coords.longitude,
        }}
        radius={searchRadius}
        strokeColor="rgba(26, 155, 255, 0.5)"
        fillColor="rgba(26, 155, 255, 0.1)"
        strokeWidth={2}
      />
    );
  };

  const renderDirections = () => {
    if (!showDirections || directions.length === 0) return null;

    return (
      <Polyline
        coordinates={directions}
        strokeColor="#1a9bff"
        strokeWidth={4}
        lineDashPattern={[5, 5]}
      />
    );
  };

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        region={region}
        onRegionChangeComplete={handleRegionChangeComplete}
        showsUserLocation={showUserLocation}
        showsMyLocationButton={false}
        showsTraffic={showTraffic}
        showsBuildings={true}
        showsIndoors={true}
        loadingEnabled={true}
        mapType="standard"
      >
        {spots.map(renderSpotMarker)}
        {renderSearchRadius()}
        {renderDirections()}
      </MapView>

      {/* Map Controls */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.controlButton, isFollowingUser && styles.activeControl]}
          onPress={centerOnUser}
        >
          <Text style={styles.controlButtonText}>📍</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.controlButton}
          onPress={fitToSpots}
        >
          <Text style={styles.controlButtonText}>🎯</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.controlButton, showTraffic && styles.activeControl]}
          onPress={() => {
            // Toggle traffic - this would be handled by parent component
            console.log('Toggle traffic');
          }}
        >
          <Text style={styles.controlButtonText}>🚦</Text>
        </TouchableOpacity>
      </View>

      {/* Map Legend */}
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: '#10b981' }]} />
          <Text style={styles.legendText}>Available</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: '#ef4444' }]} />
          <Text style={styles.legendText}>Occupied</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: '#1a9bff' }]} />
          <Text style={styles.legendText}>Selected</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  controls: {
    position: 'absolute',
    top: 50,
    right: 16,
    gap: 8,
  },
  controlButton: {
    width: 44,
    height: 44,
    backgroundColor: '#ffffff',
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  activeControl: {
    backgroundColor: '#1a9bff',
  },
  controlButtonText: {
    fontSize: 18,
  },
  legend: {
    position: 'absolute',
    bottom: 100,
    left: 16,
    backgroundColor: '#ffffff',
    padding: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontSize: 12,
    color: '#374151',
  },
  calloutContainer: {
    width: 200,
    padding: 8,
  },
  calloutTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  calloutPrice: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1a9bff',
    marginBottom: 2,
  },
  calloutRating: {
    fontSize: 14,
    color: '#f59e0b',
    marginBottom: 2,
  },
  calloutStatus: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  directionsButton: {
    backgroundColor: '#1a9bff',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  directionsButtonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
});
