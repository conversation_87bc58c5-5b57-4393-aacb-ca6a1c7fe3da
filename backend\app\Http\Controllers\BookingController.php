<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\ParkingSpot;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class BookingController extends Controller
{
    /**
     * Get user's bookings
     */
    public function index(Request $request)
    {
        try {
            $query = Booking::with(['spot', 'vehicle', 'host'])
                ->forDriver(auth()->id());

            // Filter by status
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            // Pagination
            $page = $request->get('page', 1);
            $limit = min($request->get('limit', 20), 50);
            
            $bookings = $query->orderBy('created_at', 'desc')
                ->paginate($limit, ['*'], 'page', $page);

            return response()->json([
                'success' => true,
                'data' => $bookings->items(),
                'pagination' => [
                    'current_page' => $bookings->currentPage(),
                    'last_page' => $bookings->lastPage(),
                    'per_page' => $bookings->perPage(),
                    'total' => $bookings->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get bookings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new booking
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'spot_id' => 'required|exists:parking_spots,id',
            'vehicle_id' => 'required|exists:vehicles,id',
            'start_time' => 'required|date|after:now',
            'end_time' => 'required|date|after:start_time',
            'payment_method_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $spot = ParkingSpot::findOrFail($request->spot_id);
            
            // Check if spot is available for the requested time
            if ($spot->hasConflictingBooking($request->start_time, $request->end_time)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Spot is not available for the selected time'
                ], 409);
            }

            // Calculate total amount
            $startTime = Carbon::parse($request->start_time);
            $endTime = Carbon::parse($request->end_time);
            $hours = $startTime->diffInHours($endTime);
            
            $totalAmount = $hours * $spot->hourly_rate;
            
            // Apply daily rate if booking is for 8+ hours
            if ($hours >= 8 && $spot->daily_rate) {
                $days = ceil($hours / 24);
                $totalAmount = $days * $spot->daily_rate;
            }

            $booking = Booking::create([
                'driver_id' => auth()->id(),
                'host_id' => $spot->host_id,
                'spot_id' => $request->spot_id,
                'vehicle_id' => $request->vehicle_id,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'total_amount' => $totalAmount,
                'status' => 'pending',
                'qr_code' => 'SPARK_' . time() . '_' . auth()->id(),
            ]);

            // Create notifications
            Notification::createBookingNotification(
                $spot->host_id,
                'New Booking Request',
                'You have a new booking request for your parking spot.',
                ['booking_id' => $booking->id]
            );

            return response()->json([
                'success' => true,
                'message' => 'Booking created successfully',
                'data' => $booking->load(['spot', 'vehicle', 'host'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create booking',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get booking details
     */
    public function show($id)
    {
        try {
            $booking = Booking::with(['spot', 'vehicle', 'host', 'driver'])
                ->where(function ($query) {
                    $query->where('driver_id', auth()->id())
                          ->orWhere('host_id', auth()->id());
                })
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $booking
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Booking not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Get active booking for driver
     */
    public function getActiveBooking()
    {
        try {
            $booking = Booking::with(['spot', 'vehicle', 'host'])
                ->forDriver(auth()->id())
                ->current()
                ->first();

            return response()->json([
                'success' => true,
                'data' => $booking
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get active booking',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get host's bookings
     */
    public function hostBookings(Request $request)
    {
        try {
            $query = Booking::with(['spot', 'vehicle', 'driver'])
                ->forHost(auth()->id());

            // Filter by status
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            // Pagination
            $page = $request->get('page', 1);
            $limit = min($request->get('limit', 20), 50);
            
            $bookings = $query->orderBy('created_at', 'desc')
                ->paginate($limit, ['*'], 'page', $page);

            return response()->json([
                'success' => true,
                'data' => $bookings->items(),
                'pagination' => [
                    'current_page' => $bookings->currentPage(),
                    'last_page' => $bookings->lastPage(),
                    'per_page' => $bookings->perPage(),
                    'total' => $bookings->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get host bookings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Extend booking
     */
    public function extend(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'new_end_time' => 'required|date|after:now',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $booking = Booking::where('driver_id', auth()->id())
                ->where('status', 'active')
                ->findOrFail($id);

            if (!$booking->canExtendTo($request->new_end_time)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot extend booking due to conflicting reservations'
                ], 409);
            }

            $additionalAmount = $booking->extend($request->new_end_time);

            // Create notification for host
            Notification::createBookingNotification(
                $booking->host_id,
                'Booking Extended',
                'A booking has been extended for your parking spot.',
                ['booking_id' => $booking->id, 'additional_amount' => $additionalAmount]
            );

            return response()->json([
                'success' => true,
                'message' => 'Booking extended successfully',
                'data' => [
                    'booking' => $booking->fresh(),
                    'additional_amount' => $additionalAmount
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to extend booking',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel booking
     */
    public function cancel(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $booking = Booking::where('driver_id', auth()->id())
                ->findOrFail($id);

            if (!$booking->can_be_cancelled) {
                return response()->json([
                    'success' => false,
                    'message' => 'Booking cannot be cancelled at this time'
                ], 409);
            }

            $booking->cancel($request->reason);

            // Create notification for host
            Notification::createBookingNotification(
                $booking->host_id,
                'Booking Cancelled',
                'A booking has been cancelled for your parking spot.',
                ['booking_id' => $booking->id, 'reason' => $request->reason]
            );

            return response()->json([
                'success' => true,
                'message' => 'Booking cancelled successfully',
                'data' => $booking
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel booking',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Complete booking
     */
    public function complete($id)
    {
        try {
            $booking = Booking::where('driver_id', auth()->id())
                ->where('status', 'active')
                ->findOrFail($id);

            $booking->complete();

            // Create notification for host
            Notification::createBookingNotification(
                $booking->host_id,
                'Booking Completed',
                'A booking has been completed for your parking spot.',
                ['booking_id' => $booking->id]
            );

            return response()->json([
                'success' => true,
                'message' => 'Booking completed successfully',
                'data' => $booking
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to complete booking',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
