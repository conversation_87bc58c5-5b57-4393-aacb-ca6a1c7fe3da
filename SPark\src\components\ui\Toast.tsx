import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { SuccessIcon, ErrorIcon, WarningIcon, InfoIcon, CloseIcon } from './Icon';
import { colors, typography, spacing, borderRadius, shadows } from '../../theme';

const { width } = Dimensions.get('window');

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface ToastProps {
  type: ToastType;
  title: string;
  message?: string;
  duration?: number;
  onDismiss?: () => void;
  visible: boolean;
}

const Toast: React.FC<ToastProps> = ({
  type,
  title,
  message,
  duration = 4000,
  onDismiss,
  visible,
}) => {
  const translateY = useRef(new Animated.Value(-100)).current;
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      // Show animation
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      // Auto dismiss
      if (duration > 0) {
        const timer = setTimeout(() => {
          handleDismiss();
        }, duration);

        return () => clearTimeout(timer);
      }
    } else {
      handleDismiss();
    }
  }, [visible, duration]);

  const handleDismiss = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: -100,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      if (onDismiss) {
        onDismiss();
      }
    });
  };

  const getToastConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: <SuccessIcon size={24} color={colors.success[600]} />,
          backgroundColor: colors.success[50],
          borderColor: colors.success[200],
          titleColor: colors.success[800],
          messageColor: colors.success[700],
        };
      case 'error':
        return {
          icon: <ErrorIcon size={24} color={colors.error[600]} />,
          backgroundColor: colors.error[50],
          borderColor: colors.error[200],
          titleColor: colors.error[800],
          messageColor: colors.error[700],
        };
      case 'warning':
        return {
          icon: <WarningIcon size={24} color={colors.warning[600]} />,
          backgroundColor: colors.warning[50],
          borderColor: colors.warning[200],
          titleColor: colors.warning[800],
          messageColor: colors.warning[700],
        };
      case 'info':
      default:
        return {
          icon: <InfoIcon size={24} color={colors.primary[600]} />,
          backgroundColor: colors.primary[50],
          borderColor: colors.primary[200],
          titleColor: colors.primary[800],
          messageColor: colors.primary[700],
        };
    }
  };

  const config = getToastConfig();

  if (!visible) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateY }],
          opacity,
          backgroundColor: config.backgroundColor,
          borderColor: config.borderColor,
        },
      ]}
    >
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          {config.icon}
        </View>
        
        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: config.titleColor }]}>
            {title}
          </Text>
          {message && (
            <Text style={[styles.message, { color: config.messageColor }]}>
              {message}
            </Text>
          )}
        </View>
        
        <TouchableOpacity
          style={styles.closeButton}
          onPress={handleDismiss}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <CloseIcon size={20} color={colors.gray[500]} />
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50,
    left: spacing[4],
    right: spacing[4],
    borderRadius: borderRadius.md,
    borderWidth: 1,
    ...shadows.lg,
    zIndex: 1000,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: spacing[4],
  },
  iconContainer: {
    marginRight: spacing[3],
    marginTop: spacing[1],
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    marginBottom: spacing[1],
  },
  message: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    lineHeight: typography.lineHeight.sm,
  },
  closeButton: {
    marginLeft: spacing[2],
    marginTop: spacing[1],
  },
});

export default Toast;
