import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Provider } from 'react-redux';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { PlatformStripeProvider as StripeProvider } from '../src/components/PlatformStripe';
import React, { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import { store } from '../src/store';
import AppInitializer from '../src/services/appInitializer';
import Loading from '../src/components/ui/Loading';
import { ParkingIcon } from '../src/components/ui/Icon';
import { colors, typography, spacing } from '../src/theme';

function AppContent() {
  const colorScheme = useColorScheme();
  const [isInitialized, setIsInitialized] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      const result = await AppInitializer.initialize();

      if (result.success) {
        setIsInitialized(true);
        console.log('✅ App initialized with features:', result.features);
      } else {
        setInitError(result.error || 'Initialization failed');
        console.error('❌ App initialization failed:', result.error);
      }
    } catch (error: any) {
      setInitError(error.message || 'Unknown error');
      console.error('❌ App initialization error:', error);
    }
  };

  if (!isInitialized) {
    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.white,
        padding: spacing[5]
      }}>
        {initError ? (
          <View style={{ alignItems: 'center' }}>
            <ParkingIcon size={64} color={colors.error[500]} style={{ marginBottom: spacing[4] }} />
            <Text style={{
              fontSize: typography.fontSize.xl,
              fontWeight: typography.fontWeight.semibold,
              color: colors.error[600],
              marginBottom: spacing[2],
              textAlign: 'center'
            }}>
              Initialization Failed
            </Text>
            <Text style={{
              fontSize: typography.fontSize.base,
              color: colors.black[600],
              textAlign: 'center',
              marginBottom: spacing[4],
              lineHeight: typography.lineHeight.base
            }}>
              {initError}
            </Text>
            <Text style={{
              fontSize: typography.fontSize.sm,
              color: colors.gray[500],
              textAlign: 'center'
            }}>
              Please restart the app to try again
            </Text>
          </View>
        ) : (
          <View style={{ alignItems: 'center' }}>
            <View style={{
              width: 80,
              height: 80,
              borderRadius: 40,
              backgroundColor: colors.primary[500],
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: spacing[4]
            }}>
              <ParkingIcon size={40} color={colors.black[900]} />
            </View>
            <Loading
              text="Initializing SPark..."
              color={colors.primary[500]}
              textStyle={{
                color: colors.black[600],
                fontSize: typography.fontSize.base,
                fontWeight: typography.fontWeight.medium
              }}
            />
          </View>
        )}
      </View>
    );
  }

  return (
    <StripeProvider publishableKey="pk_test_51234567890abcdef">
      <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
        <Stack>
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen
            name="find-parking"
            options={{
              headerShown: false,
              presentation: 'fullScreenModal',
              gestureEnabled: true,
              animation: 'slide_from_bottom',
            }}
          />
          <Stack.Screen name="auth/login" options={{ headerShown: false }} />
          <Stack.Screen name="auth/register" options={{ headerShown: false }} />
          <Stack.Screen name="auth/forgot-password" options={{ headerShown: false }} />
          <Stack.Screen name="booking/details" options={{ title: 'Booking Details' }} />
          <Stack.Screen name="booking/payment" options={{ title: 'Payment' }} />
          <Stack.Screen name="spot/details" options={{ title: 'Parking Spot' }} />
          <Stack.Screen name="profile/edit" options={{ title: 'Edit Profile' }} />
          <Stack.Screen name="profile/vehicles" options={{ title: 'My Vehicles' }} />
          <Stack.Screen name="profile/payment-methods" options={{ title: 'Payment Methods' }} />
          <Stack.Screen name="host/create-listing" options={{ title: 'Create Listing' }} />
          <Stack.Screen name="host/edit-listing" options={{ title: 'Edit Listing' }} />
          <Stack.Screen name="host/dashboard" options={{ title: 'Host Dashboard' }} />
          <Stack.Screen name="search" options={{ title: 'Search Parking' }} />
          <Stack.Screen name="bookings" options={{ title: 'My Bookings' }} />
          <Stack.Screen name="integration-test" options={{ title: 'Integration Test' }} />
          <Stack.Screen name="+not-found" />
        </Stack>
        <StatusBar style="auto" />
      </ThemeProvider>
    </StripeProvider>
  );
}

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
    // We'll use system fonts for now and add custom fonts later
  });

  // Continue even if fonts don't load - system fonts will be used
  if (!fontsLoaded) {
    return null;
  }

  return (
    <Provider store={store}>
      <SafeAreaProvider>
        <AppContent />
      </SafeAreaProvider>
    </Provider>
  );
}
