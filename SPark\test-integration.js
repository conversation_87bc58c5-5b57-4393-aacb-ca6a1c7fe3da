/**
 * Frontend-Backend Integration Test
 * Run this with: node test-integration.js
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:8000/api';

// Test configuration
const testConfig = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
};

async function testEndpoint(name, method, url, data = null, token = null) {
  console.log(`\n${name}...`);
  
  try {
    const config = { ...testConfig };
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    let response;
    switch (method.toUpperCase()) {
      case 'GET':
        response = await axios.get(url, config);
        break;
      case 'POST':
        response = await axios.post(url, data, config);
        break;
      default:
        throw new Error(`Unsupported method: ${method}`);
    }
    
    if (response.data.success) {
      console.log(`✅ ${name} - SUCCESS`);
      if (response.data.message) {
        console.log(`   Message: ${response.data.message}`);
      }
      if (response.data.data) {
        if (Array.isArray(response.data.data)) {
          console.log(`   Data: ${response.data.data.length} items`);
        } else if (typeof response.data.data === 'object') {
          const keys = Object.keys(response.data.data);
          console.log(`   Data: Object with keys: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}`);
        }
      }
      return response.data;
    } else {
      console.log(`❌ ${name} - FAILED`);
      console.log(`   Error: ${response.data.message || 'Unknown error'}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ ${name} - ERROR`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Error: ${error.response.data?.message || error.message}`);
    } else {
      console.log(`   Error: ${error.message}`);
    }
    return null;
  }
}

async function runIntegrationTests() {
  console.log('🚀 SPark Frontend-Backend Integration Tests');
  console.log('===========================================\n');
  
  let authToken = null;
  
  // Test 1: API Health Check
  const healthCheck = await testEndpoint(
    '1. API Health Check',
    'GET',
    `${API_BASE_URL}/test`
  );
  
  // Test 2: User Login
  const loginResult = await testEndpoint(
    '2. User Login',
    'POST',
    `${API_BASE_URL}/auth/login`,
    {
      email: '<EMAIL>',
      password: 'password'
    }
  );
  
  if (loginResult && loginResult.data && loginResult.data.token) {
    authToken = loginResult.data.token;
    console.log(`   Token: ${authToken.substring(0, 20)}...`);
  }
  
  // Test 3: User Profile (requires auth)
  if (authToken) {
    await testEndpoint(
      '3. Get User Profile',
      'GET',
      `${API_BASE_URL}/auth/profile`,
      null,
      authToken
    );
  } else {
    console.log('\n❌ 3. Get User Profile - SKIPPED (no auth token)');
  }
  
  // Test 4: Spot Search (public)
  await testEndpoint(
    '4. Parking Spot Search',
    'GET',
    `${API_BASE_URL}/spots/search?latitude=40.7128&longitude=-74.0060&radius=10`
  );
  
  // Test 5: Nearby Spots (public)
  await testEndpoint(
    '5. Nearby Spots',
    'GET',
    `${API_BASE_URL}/spots/nearby?lat=40.7128&lng=-74.0060&radius=5`
  );
  
  // Test 6: User Vehicles (requires auth)
  if (authToken) {
    await testEndpoint(
      '6. Get User Vehicles',
      'GET',
      `${API_BASE_URL}/vehicles`,
      null,
      authToken
    );
  } else {
    console.log('\n❌ 6. Get User Vehicles - SKIPPED (no auth token)');
  }
  
  // Test 7: User Registration (with unique email)
  const uniqueEmail = `test${Date.now()}@example.com`;
  await testEndpoint(
    '7. User Registration',
    'POST',
    `${API_BASE_URL}/auth/register`,
    {
      email: uniqueEmail,
      password: 'password123',
      password_confirmation: 'password123',
      first_name: 'Test',
      last_name: 'User',
      user_type: 'driver'
    }
  );
  
  console.log('\n🎯 Integration Test Summary');
  console.log('===========================');
  console.log('✅ Tests completed!');
  console.log('\nIf you see mostly ✅ marks above, your integration is working!');
  console.log('If you see ❌ marks, check the error messages for troubleshooting.');
  
  console.log('\n📱 Next Steps:');
  console.log('1. Add the IntegrationTest component to your React Native app');
  console.log('2. Test the integration directly in your mobile app');
  console.log('3. Start building your app features!');
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Run the tests
runIntegrationTests().catch(console.error);
