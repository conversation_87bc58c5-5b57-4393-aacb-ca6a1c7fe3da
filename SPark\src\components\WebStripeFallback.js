// Web fallback for @stripe/stripe-react-native
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const WebStripeProvider = ({ children, publishableKey }) => {
  return React.createElement(View, null, children);
};

const WebCardField = ({ style, ...props }) => {
  return React.createElement(
    View,
    { style: [styles.webCardField, style] },
    React.createElement(Text, { style: styles.webCardFieldTitle }, '💳 Payment Card'),
    React.createElement(
      Text,
      { style: styles.webCardFieldNote },
      '📱 Card input is available in the mobile app'
    ),
    React.createElement(
      View,
      { style: styles.webCardFieldPlaceholder },
      React.createElement(Text, { style: styles.webCardFieldPlaceholderText }, '**** **** **** ****'),
      React.createElement(Text, { style: styles.webCardFieldPlaceholderText }, 'MM/YY CVC')
    )
  );
};

const webUseStripe = () => ({
  confirmPayment: async () => ({ 
    error: { message: 'Payment processing is available in the mobile app' } 
  }),
  createPaymentMethod: async () => ({ 
    error: { message: 'Payment processing is available in the mobile app' } 
  }),
  retrievePaymentIntent: async () => ({ 
    error: { message: 'Payment processing is available in the mobile app' } 
  }),
});

const webUseConfirmPayment = () => ({
  confirmPayment: async () => ({ 
    error: { message: 'Payment processing is available in the mobile app' } 
  }),
});

const styles = StyleSheet.create({
  webCardField: {
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 16,
    borderWidth: 2,
    borderColor: '#e5e7eb',
    borderStyle: 'dashed',
  },
  webCardFieldTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  webCardFieldNote: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
    marginBottom: 12,
  },
  webCardFieldPlaceholder: {
    backgroundColor: '#ffffff',
    borderRadius: 6,
    padding: 12,
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  webCardFieldPlaceholderText: {
    fontSize: 16,
    color: '#9ca3af',
    fontFamily: 'monospace',
    marginBottom: 4,
  },
});

// Export the components that @stripe/stripe-react-native would normally export
export const StripeProvider = WebStripeProvider;
export const CardField = WebCardField;
export const useStripe = webUseStripe;
export const useConfirmPayment = webUseConfirmPayment;

// Default export
export default {
  StripeProvider: WebStripeProvider,
  CardField: WebCardField,
  useStripe: webUseStripe,
  useConfirmPayment: webUseConfirmPayment,
};
