import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
} from 'react-native';
import { colors, typography, spacing, borderRadius } from '../../theme';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  hintStyle?: TextStyle;
  required?: boolean;
  disabled?: boolean;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  hint,
  leftIcon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  inputStyle,
  labelStyle,
  errorStyle,
  hintStyle,
  required = false,
  disabled = false,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const getContainerStyle = (): ViewStyle => {
    return {
      marginBottom: spacing[4],
    };
  };

  const getLabelStyle = (): TextStyle => {
    return {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      color: colors.black[800],
      marginBottom: spacing[1],
    };
  };

  const getInputContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderRadius: borderRadius.md,
      paddingHorizontal: spacing[3],
      minHeight: 48,
      backgroundColor: colors.white,
    };

    if (error) {
      baseStyle.borderColor = colors.error[500];
    } else if (isFocused) {
      baseStyle.borderColor = colors.primary[500];
      baseStyle.borderWidth = 2;
    } else {
      baseStyle.borderColor = colors.gray[300];
    }

    if (disabled) {
      baseStyle.backgroundColor = colors.gray[50];
      baseStyle.opacity = 0.6;
    }

    return baseStyle;
  };

  const getInputStyle = (): TextStyle => {
    return {
      flex: 1,
      fontSize: typography.fontSize.base,
      color: colors.black[900],
      paddingVertical: spacing[2],
      paddingHorizontal: leftIcon ? spacing[2] : 0,
    };
  };

  const getErrorStyle = (): TextStyle => {
    return {
      fontSize: typography.fontSize.sm,
      color: colors.error[500],
      marginTop: spacing[1],
    };
  };

  const getHintStyle = (): TextStyle => {
    return {
      fontSize: typography.fontSize.sm,
      color: colors.gray[500],
      marginTop: spacing[1],
    };
  };

  const renderLabel = () => {
    if (!label) return null;

    return (
      <Text style={[getLabelStyle(), labelStyle]}>
        {label}
        {required && <Text style={{ color: colors.error[500] }}> *</Text>}
      </Text>
    );
  };

  const renderLeftIcon = () => {
    if (!leftIcon) return null;

    return (
      <View style={{ marginRight: spacing[2] }}>
        {leftIcon}
      </View>
    );
  };

  const renderRightIcon = () => {
    if (!rightIcon) return null;

    const iconElement = (
      <View style={{ marginLeft: spacing[2] }}>
        {rightIcon}
      </View>
    );

    if (onRightIconPress) {
      return (
        <TouchableOpacity onPress={onRightIconPress} disabled={disabled}>
          {iconElement}
        </TouchableOpacity>
      );
    }

    return iconElement;
  };

  const renderError = () => {
    if (!error) return null;

    return (
      <Text style={[getErrorStyle(), errorStyle]}>
        {error}
      </Text>
    );
  };

  const renderHint = () => {
    if (!hint || error) return null;

    return (
      <Text style={[getHintStyle(), hintStyle]}>
        {hint}
      </Text>
    );
  };

  return (
    <View style={[getContainerStyle(), containerStyle]}>
      {renderLabel()}
      
      <View style={getInputContainerStyle()}>
        {renderLeftIcon()}
        
        <TextInput
          style={[getInputStyle(), inputStyle]}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          editable={!disabled}
          placeholderTextColor={colors.gray[400]}
          {...textInputProps}
        />
        
        {renderRightIcon()}
      </View>
      
      {renderError()}
      {renderHint()}
    </View>
  );
};

export default Input;
