import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { router } from 'expo-router';
import { useAppDispatch, useAppSelector } from '../../src/store';
import { updateProfile } from '../../src/store/slices/authSlice';
import Button from '../../src/components/ui/Button';
import Input from '../../src/components/ui/Input';
import Card from '../../src/components/ui/Card';
import { colors, spacing, typography, borderRadius } from '../../src/theme';

export default function EditProfileScreen() {
  const dispatch = useAppDispatch();
  const { user, isLoading, isAuthenticated } = useAppSelector((state) => state.auth);
  
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/auth/login');
      return;
    }
    
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        phone: user.phone || '',
      });
    }
  }, [user, isAuthenticated]);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string) => {
    if (!phone) return true; // Phone is optional
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (formData.phone && !validatePhone(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveChanges = async () => {
    if (!validateForm()) return;

    try {
      const updates = {
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim(),
        phone: formData.phone.trim() || undefined,
      };

      await dispatch(updateProfile(updates)).unwrap();
      setHasChanges(false);
      Alert.alert('Success', 'Profile updated successfully!');
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update profile. Please try again.');
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      Alert.alert(
        'Discard Changes',
        'You have unsaved changes. Are you sure you want to go back?',
        [
          { text: 'Stay', style: 'cancel' },
          { 
            text: 'Discard', 
            style: 'destructive',
            onPress: () => router.back()
          },
        ]
      );
    } else {
      router.back();
    }
  };

  const renderProfilePicture = () => (
    <View style={styles.profilePictureSection}>
      <View style={styles.avatarContainer}>
        <View style={styles.avatar}>
          <Text style={styles.avatarText}>
            {formData.firstName?.[0]?.toUpperCase()}{formData.lastName?.[0]?.toUpperCase()}
          </Text>
        </View>
        <TouchableOpacity 
          style={styles.changePhotoButton}
          onPress={() => Alert.alert('Coming Soon', 'Photo upload will be available soon.')}
        >
          <Text style={styles.changePhotoText}>📷</Text>
        </TouchableOpacity>
      </View>
      <Text style={styles.changePhotoLabel}>Tap to change photo</Text>
    </View>
  );

  const renderAccountInfo = () => (
    <Card style={styles.infoCard}>
      <Text style={styles.infoTitle}>Account Information</Text>
      <View style={styles.infoItem}>
        <Text style={styles.infoLabel}>User Type</Text>
        <Text style={styles.infoValue}>
          {user?.userType === 'host' ? 'Host' : 'Driver'}
        </Text>
      </View>
      <View style={styles.infoItem}>
        <Text style={styles.infoLabel}>Member Since</Text>
        <Text style={styles.infoValue}>
          {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
        </Text>
      </View>
      <View style={styles.infoItem}>
        <Text style={styles.infoLabel}>Verification Status</Text>
        <Text style={[
          styles.infoValue,
          { color: user?.isVerified ? colors.success[600] : colors.warning[600] }
        ]}>
          {user?.isVerified ? 'Verified' : 'Pending Verification'}
        </Text>
      </View>
    </Card>
  );

  if (!isAuthenticated || !user) {
    return null; // Will redirect to login
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        style={styles.scrollView}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        {renderProfilePicture()}

        <Card style={styles.formCard}>
          <Text style={styles.formTitle}>Personal Information</Text>
          
          <View style={styles.nameRow}>
            <Input
              label="First Name"
              value={formData.firstName}
              onChangeText={(value) => handleInputChange('firstName', value)}
              placeholder="First name"
              error={errors.firstName}
              containerStyle={styles.nameInput}
              required
            />
            
            <Input
              label="Last Name"
              value={formData.lastName}
              onChangeText={(value) => handleInputChange('lastName', value)}
              placeholder="Last name"
              error={errors.lastName}
              containerStyle={styles.nameInput}
              required
            />
          </View>

          <Input
            label="Email Address"
            value={formData.email}
            onChangeText={(value) => handleInputChange('email', value)}
            placeholder="Enter your email"
            keyboardType="email-address"
            autoCapitalize="none"
            autoCorrect={false}
            error={errors.email}
            required
          />

          <Input
            label="Phone Number"
            value={formData.phone}
            onChangeText={(value) => handleInputChange('phone', value)}
            placeholder="Enter your phone number (optional)"
            keyboardType="phone-pad"
            error={errors.phone}
          />
        </Card>

        {renderAccountInfo()}

        <View style={styles.actionButtons}>
          <Button
            title="Save Changes"
            onPress={handleSaveChanges}
            loading={isLoading}
            disabled={!hasChanges}
            fullWidth
            style={styles.saveButton}
          />
          
          <Button
            title="Cancel"
            onPress={handleCancel}
            variant="outline"
            fullWidth
            style={styles.cancelButton}
          />
        </View>

        <View style={styles.dangerZone}>
          <Text style={styles.dangerTitle}>Danger Zone</Text>
          <Button
            title="Delete Account"
            onPress={() => {
              Alert.alert(
                'Delete Account',
                'This action cannot be undone. Are you sure you want to delete your account?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  { 
                    text: 'Delete', 
                    style: 'destructive',
                    onPress: () => Alert.alert('Coming Soon', 'Account deletion will be available soon.')
                  },
                ]
              );
            }}
            variant="danger"
            fullWidth
            style={styles.deleteButton}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollView: {
    flex: 1,
    padding: spacing[4],
  },
  profilePictureSection: {
    alignItems: 'center',
    marginBottom: spacing[6],
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: spacing[2],
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.primary[500],
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    fontSize: typography.fontSize['3xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.white,
  },
  changePhotoButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: colors.gray[200],
  },
  changePhotoText: {
    fontSize: 16,
  },
  changePhotoLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
  },
  formCard: {
    marginBottom: spacing[4],
  },
  formTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[4],
  },
  nameRow: {
    flexDirection: 'row',
    gap: spacing[2],
  },
  nameInput: {
    flex: 1,
  },
  infoCard: {
    marginBottom: spacing[6],
  },
  infoTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[4],
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  infoLabel: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  infoValue: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.gray[900],
  },
  actionButtons: {
    gap: spacing[3],
    marginBottom: spacing[8],
  },
  saveButton: {
    marginBottom: 0,
  },
  cancelButton: {
    marginBottom: 0,
  },
  dangerZone: {
    paddingTop: spacing[6],
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  dangerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.error[600],
    marginBottom: spacing[4],
  },
  deleteButton: {
    marginBottom: spacing[8],
  },
});
