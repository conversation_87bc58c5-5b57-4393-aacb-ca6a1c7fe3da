import { Alert } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import { initializeStripe } from './stripe';
import NotificationService from './notifications';
import WebSocketService from './websocket';
import * as SecureStore from 'expo-secure-store';

export interface AppInitializationResult {
  success: boolean;
  error?: string;
  features: {
    stripe: boolean;
    notifications: boolean;
    websocket: boolean;
    location: boolean;
  };
}

export class AppInitializer {
  private static instance: AppInitializer;
  private isInitialized = false;
  private initializationPromise: Promise<AppInitializationResult> | null = null;

  private constructor() {}

  public static getInstance(): AppInitializer {
    if (!AppInitializer.instance) {
      AppInitializer.instance = new AppInitializer();
    }
    return AppInitializer.instance;
  }

  // Main initialization method
  public async initialize(): Promise<AppInitializationResult> {
    if (this.isInitialized) {
      return {
        success: true,
        features: {
          stripe: true,
          notifications: true,
          websocket: true,
          location: true,
        },
      };
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this.performInitialization();
    return this.initializationPromise;
  }

  private async performInitialization(): Promise<AppInitializationResult> {
    try {
      console.log('🚀 Starting SPark app initialization...');

      // Keep splash screen visible during initialization
      await SplashScreen.preventAutoHideAsync();

      const features = {
        stripe: false,
        notifications: false,
        websocket: false,
        location: false,
      };

      // Initialize Stripe
      try {
        await initializeStripe();
        features.stripe = true;
        console.log('✅ Stripe initialized successfully');
      } catch (error) {
        console.warn('⚠️ Stripe initialization failed:', error);
      }

      // Initialize Notifications (gracefully handle Expo Go limitations)
      try {
        const pushToken = await NotificationService.initialize();
        if (pushToken) {
          features.notifications = true;
          console.log('✅ Notifications initialized successfully');

          // Store push token securely
          await SecureStore.setItemAsync('pushToken', pushToken);
        } else {
          console.log('📱 Notifications not available (Expo Go or missing config)');
          features.notifications = false;
        }
      } catch (error: any) {
        console.warn('⚠️ Notifications initialization failed:', error.message);
        features.notifications = false;
      }

      // Initialize WebSocket (will connect when user logs in)
      try {
        // Just verify WebSocket service is available
        const wsService = WebSocketService.getInstance();
        if (wsService) {
          features.websocket = true;
          console.log('✅ WebSocket service ready');
        }
      } catch (error) {
        console.warn('⚠️ WebSocket service initialization failed:', error);
      }

      // Check location permissions (don't request yet)
      try {
        features.location = true; // We'll request when needed
        console.log('✅ Location service ready');
      } catch (error) {
        console.warn('⚠️ Location service initialization failed:', error);
      }

      // Setup notification handlers
      this.setupNotificationHandlers();

      // Setup app state change handlers
      this.setupAppStateHandlers();

      // Load user preferences
      await this.loadUserPreferences();

      // Hide splash screen
      await SplashScreen.hideAsync();

      this.isInitialized = true;
      console.log('🎉 SPark app initialization completed successfully');

      return {
        success: true,
        features,
      };
    } catch (error: any) {
      console.error('❌ App initialization failed:', error);
      
      // Hide splash screen even on error
      await SplashScreen.hideAsync();

      return {
        success: false,
        error: error.message || 'Unknown initialization error',
        features: {
          stripe: false,
          notifications: false,
          websocket: false,
          location: false,
        },
      };
    }
  }

  // Setup notification handlers
  private setupNotificationHandlers(): void {
    // Handle notification received while app is running
    NotificationService.addNotificationReceivedListener((notification) => {
      console.log('📱 Notification received:', notification);
      // Handle in-app notification display
    });

    // Handle notification response (when user taps notification)
    NotificationService.addNotificationResponseReceivedListener((response) => {
      console.log('👆 Notification tapped:', response);
      this.handleNotificationResponse(response);
    });
  }

  // Handle notification tap navigation
  private handleNotificationResponse(response: any): void {
    const { data } = response.notification.request.content;
    
    switch (data.type) {
      case 'booking_confirmed':
      case 'booking_reminder':
        // Navigate to booking details
        console.log('Navigate to booking:', data.bookingId);
        break;
      
      case 'spot_available':
        // Navigate to spot details
        console.log('Navigate to spot:', data.spotId);
        break;
      
      case 'host_new_booking':
        // Navigate to host dashboard
        console.log('Navigate to host dashboard');
        break;
      
      default:
        console.log('Unknown notification type:', data.type);
    }
  }

  // Setup app state change handlers
  private setupAppStateHandlers(): void {
    // Handle app coming to foreground/background
    // This would typically use AppState from react-native
    console.log('📱 App state handlers setup');
  }

  // Load user preferences
  private async loadUserPreferences(): Promise<void> {
    try {
      const preferences = await SecureStore.getItemAsync('userPreferences');
      if (preferences) {
        const parsed = JSON.parse(preferences);
        console.log('👤 User preferences loaded:', parsed);
      }
    } catch (error) {
      console.warn('⚠️ Failed to load user preferences:', error);
    }
  }

  // Connect WebSocket after user login
  public async connectWebSocket(authToken: string): Promise<void> {
    try {
      const wsService = WebSocketService.getInstance();
      wsService.connect(authToken);
      console.log('🔌 WebSocket connected');
    } catch (error) {
      console.error('❌ WebSocket connection failed:', error);
    }
  }

  // Disconnect WebSocket on logout
  public disconnectWebSocket(): void {
    try {
      const wsService = WebSocketService.getInstance();
      wsService.disconnect();
      console.log('🔌 WebSocket disconnected');
    } catch (error) {
      console.error('❌ WebSocket disconnection failed:', error);
    }
  }

  // Update push token on server
  public async updatePushToken(userId: string): Promise<void> {
    try {
      const pushToken = await SecureStore.getItemAsync('pushToken');
      if (pushToken) {
        // Send to your backend API
        console.log('📤 Updating push token for user:', userId);
        // await api.updatePushToken(userId, pushToken);
      }
    } catch (error) {
      console.error('❌ Failed to update push token:', error);
    }
  }

  // Clear app data on logout
  public async clearAppData(): Promise<void> {
    try {
      // Clear secure storage
      await SecureStore.deleteItemAsync('authToken');
      await SecureStore.deleteItemAsync('refreshToken');
      await SecureStore.deleteItemAsync('userPreferences');
      
      // Clear notifications
      await NotificationService.clearAllNotifications();
      
      // Disconnect WebSocket
      this.disconnectWebSocket();
      
      console.log('🧹 App data cleared');
    } catch (error) {
      console.error('❌ Failed to clear app data:', error);
    }
  }

  // Handle app errors
  public handleAppError(error: Error, context: string): void {
    console.error(`❌ App error in ${context}:`, error);
    
    // In production, you might want to send to crash reporting service
    // crashlytics().recordError(error);
    
    // Show user-friendly error message
    Alert.alert(
      'Something went wrong',
      'We encountered an unexpected error. Please try again.',
      [{ text: 'OK' }]
    );
  }

  // Get initialization status
  public getInitializationStatus(): boolean {
    return this.isInitialized;
  }

  // Force re-initialization (useful for testing)
  public async forceReinitialize(): Promise<AppInitializationResult> {
    this.isInitialized = false;
    this.initializationPromise = null;
    return this.initialize();
  }
}

// Export singleton instance
export default AppInitializer.getInstance();

// React hook for using app initializer
export const useAppInitializer = () => {
  const initializer = AppInitializer.getInstance();
  
  return {
    initialize: initializer.initialize.bind(initializer),
    connectWebSocket: initializer.connectWebSocket.bind(initializer),
    disconnectWebSocket: initializer.disconnectWebSocket.bind(initializer),
    updatePushToken: initializer.updatePushToken.bind(initializer),
    clearAppData: initializer.clearAppData.bind(initializer),
    handleAppError: initializer.handleAppError.bind(initializer),
    getInitializationStatus: initializer.getInitializationStatus.bind(initializer),
    forceReinitialize: initializer.forceReinitialize.bind(initializer),
  };
};
