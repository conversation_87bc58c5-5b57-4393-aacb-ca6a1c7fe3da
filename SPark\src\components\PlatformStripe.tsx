import React from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';

// Conditional imports for platform compatibility
let StripeProvider: any = null;
let CardField: any = null;
let useStripe: any = null;
let useConfirmPayment: any = null;

if (Platform.OS !== 'web') {
  try {
    const stripeModule = require('@stripe/stripe-react-native');
    StripeProvider = stripeModule.StripeProvider;
    CardField = stripeModule.CardField;
    useStripe = stripeModule.useStripe;
    useConfirmPayment = stripeModule.useConfirmPayment;
  } catch (error) {
    console.warn('Stripe React Native not available on this platform');
  }
}

// Web fallback components
const WebStripeProvider: React.FC<{ publishableKey: string; children: React.ReactNode }> = ({ 
  children, 
  publishableKey 
}) => {
  return (
    <View>
      {children}
    </View>
  );
};

const WebCardField: React.FC<any> = ({ style, ...props }) => {
  return (
    <View style={[styles.webCardField, style]}>
      <Text style={styles.webCardFieldTitle}>💳 Payment Card</Text>
      <Text style={styles.webCardFieldNote}>
        📱 Card input is available in the mobile app
      </Text>
      <View style={styles.webCardFieldPlaceholder}>
        <Text style={styles.webCardFieldPlaceholderText}>
          **** **** **** ****
        </Text>
        <Text style={styles.webCardFieldPlaceholderText}>
          MM/YY CVC
        </Text>
      </View>
    </View>
  );
};

// Web fallback hooks
const webUseStripe = () => ({
  confirmPayment: async () => ({ 
    error: { message: 'Payment processing is available in the mobile app' } 
  }),
  createPaymentMethod: async () => ({ 
    error: { message: 'Payment processing is available in the mobile app' } 
  }),
  retrievePaymentIntent: async () => ({ 
    error: { message: 'Payment processing is available in the mobile app' } 
  }),
});

const webUseConfirmPayment = () => ({
  confirmPayment: async () => ({ 
    error: { message: 'Payment processing is available in the mobile app' } 
  }),
});

// Platform-specific exports
export const PlatformStripeProvider: React.FC<{ publishableKey: string; children: React.ReactNode }> = (props) => {
  if (Platform.OS === 'web' || !StripeProvider) {
    return <WebStripeProvider {...props} />;
  }
  return <StripeProvider {...props} />;
};

export const PlatformCardField: React.FC<any> = (props) => {
  if (Platform.OS === 'web' || !CardField) {
    return <WebCardField {...props} />;
  }
  return <CardField {...props} />;
};

export const platformUseStripe = () => {
  if (Platform.OS === 'web' || !useStripe) {
    return webUseStripe();
  }
  return useStripe();
};

export const platformUseConfirmPayment = () => {
  if (Platform.OS === 'web' || !useConfirmPayment) {
    return webUseConfirmPayment();
  }
  return useConfirmPayment();
};

const styles = StyleSheet.create({
  webCardField: {
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 16,
    borderWidth: 2,
    borderColor: '#e5e7eb',
    borderStyle: 'dashed',
  },
  webCardFieldTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  webCardFieldNote: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
    marginBottom: 12,
  },
  webCardFieldPlaceholder: {
    backgroundColor: '#ffffff',
    borderRadius: 6,
    padding: 12,
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  webCardFieldPlaceholderText: {
    fontSize: 16,
    color: '#9ca3af',
    fontFamily: 'monospace',
    marginBottom: 4,
  },
});

export default PlatformStripeProvider;
