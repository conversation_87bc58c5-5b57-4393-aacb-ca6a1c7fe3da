import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Linking,
  Platform,
  Alert,
  Modal,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { ProcessedParkingSpot } from '../../types/parking';
import { formatDistance } from '../../utils/geoUtils';
import {
  LocationIcon,
  StarIcon,
  ClockIcon,
  CarIcon,
  ExternalLinkIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  CloseIcon,
} from '../ui/AnimatedIcon';
import { colors, typography, spacing, borderRadius } from '../../theme';

interface SimpleParkingBottomSheetProps {
  spots: ProcessedParkingSpot[];
  isVisible: boolean;
  onClose: () => void;
  onSpotSelect?: (spot: ProcessedParkingSpot) => void;
  loading?: boolean;
  selectedSpot?: ProcessedParkingSpot | null;
}

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

export default function SimpleParkingBottomSheet({
  spots,
  isVisible,
  onClose,
  onSpotSelect,
  loading = false,
  selectedSpot: externalSelectedSpot,
}: SimpleParkingBottomSheetProps) {
  console.log('🏠 SimpleParkingBottomSheet render');
  console.log('👁️ isVisible:', isVisible);
  console.log('🅿️ spots:', spots?.length || 0);
  console.log('⏳ loading:', loading);
  console.log('🎯 selectedSpot:', externalSelectedSpot?.name);

  const [internalSelectedSpot, setInternalSelectedSpot] = useState<ProcessedParkingSpot | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  // Use external selected spot if provided, otherwise use internal state
  const selectedSpot = externalSelectedSpot || internalSelectedSpot;

  const slideAnimation = useSharedValue(0);

  useEffect(() => {
    if (isVisible) {
      slideAnimation.value = withSpring(0, {
        damping: 50,
        stiffness: 200,
      });
    } else {
      slideAnimation.value = withTiming(400, {
        duration: 300,
      });
    }
  }, [isVisible]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateY: slideAnimation.value,
        },
      ],
    };
  });

  const handleSpotPress = (spot: ProcessedParkingSpot) => {
    if (selectedSpot?.id === spot.id) {
      setInternalSelectedSpot(null);
    } else {
      setInternalSelectedSpot(spot);
      onSpotSelect?.(spot);
    }
  };

  // Auto-expand when a spot is selected from map
  useEffect(() => {
    if (externalSelectedSpot && !isExpanded) {
      setIsExpanded(true);
    }
  }, [externalSelectedSpot]);

  const handleOpenInMaps = (spot: ProcessedParkingSpot) => {
    const { latitude, longitude, name } = spot;
    
    Alert.alert(
      'Open in Maps',
      'Choose your preferred maps app:',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Google Maps',
          onPress: () => {
            const url = Platform.select({
              ios: `maps://app?daddr=${latitude},${longitude}&q=${encodeURIComponent(name)}`,
              android: `geo:${latitude},${longitude}?q=${latitude},${longitude}(${encodeURIComponent(name)})`,
            });
            if (url) {
              Linking.openURL(url).catch(() => {
                // Fallback to web version
                Linking.openURL(`https://maps.google.com/?q=${latitude},${longitude}`);
              });
            }
          },
        },
        {
          text: 'Apple Maps',
          onPress: () => {
            const url = `http://maps.apple.com/?daddr=${latitude},${longitude}&q=${encodeURIComponent(name)}`;
            Linking.openURL(url);
          },
        },
      ]
    );
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const renderSpotItem = (spot: ProcessedParkingSpot, index: number) => {
    const isSelected = selectedSpot?.id === spot.id;
    const isFromMap = externalSelectedSpot?.id === spot.id;

    return (
      <TouchableOpacity
        key={spot.id}
        style={[
          styles.spotItem,
          isSelected && styles.spotItemSelected,
          isFromMap && styles.spotItemFromMap
        ]}
        onPress={() => handleSpotPress(spot)}
        activeOpacity={0.7}
      >
        <View style={styles.spotHeader}>
          <View style={styles.spotInfo}>
            <View style={styles.spotNameRow}>
              <Text style={styles.spotName} numberOfLines={1}>
                {spot.name}
              </Text>
              {isFromMap && (
                <View style={styles.mapSelectedBadge}>
                  <Text style={styles.mapSelectedText}>📍</Text>
                </View>
              )}
            </View>
            <View style={styles.addressRow}>
              <LocationIcon size={12} color={colors.gray[500]} />
              <Text style={styles.spotAddress} numberOfLines={1}>
                {spot.address}
              </Text>
            </View>
          </View>
          <View style={styles.spotMeta}>
            <Text style={styles.distanceText}>{formatDistance(spot.distance)}</Text>
            <View style={styles.ratingRow}>
              <StarIcon size={12} color={colors.primary[500]} />
              <Text style={styles.ratingText}>{spot.rating}</Text>
            </View>
          </View>
        </View>

        {isSelected && (
          <View style={styles.spotDetails}>
            <View style={styles.detailsRow}>
              <View style={styles.detailItem}>
                <CarIcon size={16} color={colors.gray[600]} />
                <Text style={styles.detailText}>
                  {spot.type.charAt(0).toUpperCase() + spot.type.slice(1)}
                </Text>
              </View>
              {spot.capacity && (
                <View style={styles.detailItem}>
                  <Text style={styles.detailText}>{spot.capacity} spaces</Text>
                </View>
              )}
              <View style={styles.detailItem}>
                <Text style={[styles.detailText, { color: spot.fee ? colors.error[600] : colors.success[600] }]}>
                  {spot.fee ? 'Paid' : 'Free'}
                </Text>
              </View>
            </View>

            {spot.amenities.length > 0 && (
              <View style={styles.amenitiesRow}>
                {spot.amenities.slice(0, 3).map((amenity, idx) => (
                  <View key={idx} style={styles.amenityChip}>
                    <Text style={styles.amenityText}>{amenity}</Text>
                  </View>
                ))}
              </View>
            )}

            <TouchableOpacity
              style={styles.directionsButton}
              onPress={() => handleOpenInMaps(spot)}
            >
              <ExternalLinkIcon size={16} color={colors.white} />
              <Text style={styles.directionsText}>Get Directions</Text>
            </TouchableOpacity>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderContent = () => {
    console.log('🎨 renderContent called');
    console.log('⏳ loading:', loading);
    console.log('🅿️ spots.length:', spots?.length || 0);

    if (loading) {
      console.log('⏳ Rendering loading state');
      return (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Finding nearby parking...</Text>
        </View>
      );
    }

    if (!spots || spots.length === 0) {
      console.log('❌ Rendering empty state');
      return (
        <View style={styles.emptyContainer}>
          <CarIcon size={48} color={colors.gray[400]} />
          <Text style={styles.emptyTitle}>No parking found</Text>
          <Text style={styles.emptySubtitle}>
            Try expanding your search radius or check a different area
          </Text>
        </View>
      );
    }

    console.log('✅ Rendering spots list with', spots.length, 'spots');
    return (
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {spots.map(renderSpotItem)}
      </ScrollView>
    );
  };

  console.log('🎨 Rendering SimpleParkingBottomSheet');
  console.log('👁️ Modal visible:', isVisible);

  if (!isVisible) return null;

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <View style={styles.header}>
        <View style={styles.dragHandle} />
        <View style={styles.headerContent}>
          <Text style={styles.title}>
            {loading ? 'Searching...' : `${spots?.length || 0} parking spots found`}
          </Text>
          <View style={styles.headerButtons}>
            <TouchableOpacity onPress={toggleExpanded} style={styles.expandButton}>
              {isExpanded ? (
                <ChevronDownIcon size={20} color={colors.gray[600]} />
              ) : (
                <ChevronUpIcon size={20} color={colors.gray[600]} />
              )}
            </TouchableOpacity>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <CloseIcon size={20} color={colors.gray[600]} />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <View style={[styles.content, isExpanded && styles.contentExpanded]}>
        {renderContent()}
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: SCREEN_HEIGHT * 0.7,
    minHeight: 220,
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 10,
    zIndex: 1000,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 12,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  dragHandle: {
    width: 40,
    height: 4,
    backgroundColor: colors.gray[300],
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 12,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.black[900],
    flex: 1,
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  expandButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
  },
  content: {
    height: 160,
  },
  contentExpanded: {
    height: SCREEN_HEIGHT * 0.45,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  spotItem: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.gray[200],
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  spotItemSelected: {
    borderColor: colors.primary[500],
    backgroundColor: colors.primary[50],
  },
  spotItemFromMap: {
    borderColor: colors.primary[600],
    backgroundColor: colors.primary[100],
    borderWidth: 2,
    shadowColor: colors.primary[500],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  spotHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  spotInfo: {
    flex: 1,
    marginRight: 12,
  },
  spotNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  spotName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.black[900],
    flex: 1,
  },
  mapSelectedBadge: {
    marginLeft: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    backgroundColor: colors.primary[500],
    borderRadius: 10,
  },
  mapSelectedText: {
    fontSize: 10,
    color: colors.white,
  },
  addressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  spotAddress: {
    fontSize: 14,
    color: colors.gray[600],
    flex: 1,
  },
  spotMeta: {
    alignItems: 'flex-end',
  },
  distanceText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary[600],
    marginBottom: 4,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.gray[700],
  },
  spotDetails: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  detailText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.gray[700],
  },
  amenitiesRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 12,
  },
  amenityChip: {
    backgroundColor: colors.gray[100],
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  amenityText: {
    fontSize: 10,
    fontWeight: '500',
    color: colors.gray[700],
  },
  directionsButton: {
    backgroundColor: colors.primary[500],
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 6,
  },
  directionsText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: 20,
  },
});
