// Web fallback for react-native-maps
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const WebMapView = ({ children, style, region, ...props }) => {
  return (
    <View style={[styles.webMapContainer, style]}>
      <Text style={styles.webMapTitle}>🗺️ Map View</Text>
      <Text style={styles.webMapSubtitle}>
        {region ? `Location: ${region.latitude?.toFixed(4)}, ${region.longitude?.toFixed(4)}` : 'Loading location...'}
      </Text>
      <Text style={styles.webMapNote}>
        📱 For full map functionality, please use the mobile app
      </Text>
      {children}
    </View>
  );
};

const WebMarker = ({ coordinate, title, description, children, ...props }) => {
  return (
    <View style={styles.webMarker}>
      <View style={styles.webMarkerPin} />
      <View style={styles.webMarkerInfo}>
        <Text style={styles.webMarkerTitle}>{title}</Text>
        {description && <Text style={styles.webMarkerDescription}>{description}</Text>}
        <Text style={styles.webMarkerCoords}>
          📍 {coordinate?.latitude?.toFixed(4)}, {coordinate?.longitude?.toFixed(4)}
        </Text>
      </View>
    </View>
  );
};

const WebCallout = ({ children }) => children;
const WebCircle = () => null;
const WebPolyline = () => null;

const styles = StyleSheet.create({
  webMapContainer: {
    backgroundColor: '#f0f9ff',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#1a9bff',
    borderStyle: 'dashed',
    minHeight: 200,
  },
  webMapTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a9bff',
    marginBottom: 8,
  },
  webMapSubtitle: {
    fontSize: 16,
    color: '#374151',
    marginBottom: 8,
    textAlign: 'center',
  },
  webMapNote: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    fontStyle: 'italic',
    marginBottom: 16,
  },
  webMarker: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 8,
    marginBottom: 4,
    borderRadius: 4,
    width: '100%',
  },
  webMarkerPin: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#1a9bff',
    marginRight: 8,
  },
  webMarkerInfo: {
    flex: 1,
  },
  webMarkerTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#111827',
  },
  webMarkerDescription: {
    fontSize: 10,
    color: '#6b7280',
  },
  webMarkerCoords: {
    fontSize: 9,
    color: '#9ca3af',
  },
});

// Export the components that react-native-maps would normally export
export default WebMapView;
export const Marker = WebMarker;
export const Callout = WebCallout;
export const Circle = WebCircle;
export const Polyline = WebPolyline;
export const PROVIDER_GOOGLE = 'google';

// Export everything as named exports too
export {
  WebMapView as MapView,
  WebMarker,
  WebCallout,
  WebCircle,
  WebPolyline,
};
