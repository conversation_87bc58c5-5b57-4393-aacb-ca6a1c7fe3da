import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';

const NetworkTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, message]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testNetworkConnection = async () => {
    setIsRunning(true);
    clearResults();
    
    addResult('🔍 Network Connectivity Test');
    addResult('================================\n');
    
    // URLs to test in order of preference for Expo
    const testURLs = [
      { name: 'Expo Localhost', url: 'http://localhost:8000/api/test' },
      { name: 'Expo Alt', url: 'http://127.0.0.1:8000/api/test' },
      { name: 'Your Computer IP', url: 'http://*************:8000/api/test' }, // Update this with your actual IP
      { name: 'Alternative IP', url: 'http://**********:8000/api/test' }, // Common alternative
    ];
    
    let workingURL = null;
    
    for (const testURL of testURLs) {
      try {
        addResult(`Testing ${testURL.name}...`);
        addResult(`URL: ${testURL.url}`);
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);
        
        const response = await fetch(testURL.url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          signal: controller.signal,
        });
        
        clearTimeout(timeoutId);
        
        if (response.ok) {
          const data = await response.json();
          addResult('✅ SUCCESS!');
          addResult(`Server: ${data.data?.server || 'SPark Backend'}`);
          addResult(`Status: ${response.status}\n`);
          workingURL = testURL.url;
          break;
        } else {
          addResult(`❌ HTTP Error: ${response.status}`);
          addResult(`Status Text: ${response.statusText}\n`);
        }
        
      } catch (error: any) {
        if (error.name === 'AbortError') {
          addResult('❌ Timeout (5 seconds)');
        } else {
          addResult(`❌ Error: ${error.message}`);
        }
        addResult(''); // Empty line
      }
    }
    
    if (workingURL) {
      addResult('🎉 NETWORK TEST PASSED!');
      addResult(`Working URL: ${workingURL}`);
      addResult('\n📱 You can now test the full integration!');
    } else {
      addResult('❌ ALL NETWORK TESTS FAILED');
      addResult('\n🔧 Troubleshooting Steps:');
      addResult('1. Make sure Laravel backend is running:');
      addResult('   cd backend && php artisan serve');
      addResult('2. Check if you can access http://localhost:8000 in browser');
      addResult('3. For physical devices, update the IP address');
      addResult('4. Check firewall settings');
    }
    
    setIsRunning(false);
  };

  const showNetworkHelp = () => {
    Alert.alert(
      'Network Setup Help',
      '🔧 Backend Setup:\n' +
      '1. cd backend\n' +
      '2. php artisan serve\n\n' +
      '📱 Device Setup:\n' +
      '• Web/iOS Simulator: localhost works\n' +
      '• Android Emulator: use ********\n' +
      '• Physical Device: use your computer\'s IP\n\n' +
      '🌐 Find Your IP:\n' +
      '• Windows: ipconfig\n' +
      '• Mac/Linux: ifconfig\n' +
      '• Look for 192.168.x.x or 10.x.x.x',
      [{ text: 'OK' }]
    );
  };

  const updateIPAddress = () => {
    Alert.prompt(
      'Update IP Address',
      'Enter your computer\'s IP address for physical device testing:',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Update', 
          onPress: (ip) => {
            if (ip) {
              Alert.alert('IP Updated', `New IP: ${ip}\nRestart the app to use the new IP.`);
            }
          }
        },
      ],
      'plain-text',
      '*************'
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Network Connection Test</Text>
      <Text style={styles.subtitle}>
        Test connection to Laravel backend
      </Text>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, isRunning && styles.buttonDisabled]}
          onPress={testNetworkConnection}
          disabled={isRunning}
        >
          <Text style={styles.buttonText}>
            {isRunning ? 'Testing...' : 'Test Network'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.helpButton}
          onPress={showNetworkHelp}
        >
          <Text style={styles.helpButtonText}>Help</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.ipButton}
          onPress={updateIPAddress}
        >
          <Text style={styles.ipButtonText}>Set IP</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.resultsContainer}>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 10,
  },
  button: {
    flex: 2,
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  helpButton: {
    flex: 1,
    backgroundColor: '#28a745',
    padding: 15,
    borderRadius: 8,
  },
  helpButtonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '600',
  },
  ipButton: {
    flex: 1,
    backgroundColor: '#ffc107',
    padding: 15,
    borderRadius: 8,
  },
  ipButtonText: {
    color: '#333',
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  resultText: {
    fontSize: 14,
    fontFamily: 'monospace',
    color: '#333',
    lineHeight: 20,
  },
});

export default NetworkTest;
