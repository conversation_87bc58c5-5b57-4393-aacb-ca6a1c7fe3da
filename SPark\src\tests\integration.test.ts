/**
 * Frontend-Backend Integration Tests
 * 
 * This file tests the integration between the React Native frontend
 * and the Laravel backend API.
 */

import { authAPI, spotAPI, vehicleAPI } from '../services/api';

// Mock test data
const testUser = {
  email: `test${Date.now()}@example.com`,
  password: 'password123',
  firstName: 'Test',
  lastName: 'User',
  userType: 'driver' as const,
};

const testLocation = {
  latitude: 40.7128,
  longitude: -74.0060,
  radius: 10,
};

/**
 * Test Suite: Frontend-Backend Integration
 */
export const runIntegrationTests = async () => {
  console.log('🚀 Starting Frontend-Backend Integration Tests...\n');

  try {
    // Test 1: User Registration
    console.log('1. Testing User Registration...');
    const registerResponse = await authAPI.register(testUser);
    
    if (registerResponse.success && registerResponse.data) {
      console.log('✅ Registration successful');
      console.log(`   User: ${registerResponse.data.user.first_name} ${registerResponse.data.user.last_name}`);
      console.log(`   Token: ${registerResponse.data.token.substring(0, 20)}...`);
    } else {
      throw new Error('Registration failed - no data returned');
    }

    // Test 2: User Login
    console.log('\n2. Testing User Login...');
    const loginResponse = await authAPI.login({
      email: '<EMAIL>',
      password: 'password',
    });
    
    if (loginResponse.success && loginResponse.data) {
      console.log('✅ Login successful');
      console.log(`   User: ${loginResponse.data.user.first_name} ${loginResponse.data.user.last_name}`);
      console.log(`   Token: ${loginResponse.data.token.substring(0, 20)}...`);
    } else {
      throw new Error('Login failed - no data returned');
    }

    // Test 3: Spot Search
    console.log('\n3. Testing Spot Search...');
    const searchResponse = await spotAPI.searchSpots({
      latitude: testLocation.latitude,
      longitude: testLocation.longitude,
      radius: testLocation.radius,
    });
    
    if (searchResponse.success && searchResponse.data) {
      console.log('✅ Spot search successful');
      console.log(`   Found ${searchResponse.data.length} parking spots`);
      if (searchResponse.data.length > 0) {
        console.log(`   First spot: ${searchResponse.data[0].title}`);
      }
    } else {
      throw new Error('Spot search failed - no data returned');
    }

    // Test 4: Nearby Spots
    console.log('\n4. Testing Nearby Spots...');
    const nearbyResponse = await spotAPI.getNearbySpots(testLocation);
    
    if (nearbyResponse.success && nearbyResponse.data) {
      console.log('✅ Nearby spots retrieval successful');
      console.log(`   Found ${nearbyResponse.data.length} nearby spots`);
    } else {
      throw new Error('Nearby spots failed - no data returned');
    }

    // Test 5: Get User Vehicles
    console.log('\n5. Testing Get User Vehicles...');
    const vehiclesResponse = await vehicleAPI.getUserVehicles();
    
    if (vehiclesResponse.success && vehiclesResponse.data) {
      console.log('✅ Vehicles retrieval successful');
      console.log(`   Found ${vehiclesResponse.data.length} vehicles`);
      if (vehiclesResponse.data.length > 0) {
        const vehicle = vehiclesResponse.data[0];
        console.log(`   First vehicle: ${vehicle.make} ${vehicle.model}`);
      }
    } else {
      throw new Error('Vehicles retrieval failed - no data returned');
    }

    console.log('\n🎉 All integration tests passed!');
    console.log('\n✅ Frontend-Backend Integration Status:');
    console.log('   - API Connection: Working');
    console.log('   - Authentication: Working');
    console.log('   - Spot Search: Working');
    console.log('   - Vehicle Management: Working');
    console.log('\n🚀 Your SPark app is ready for testing!');

  } catch (error: any) {
    console.error('\n❌ Integration test failed:');
    console.error(`   Error: ${error.message}`);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Make sure the Laravel backend is running (php artisan serve)');
    console.log('   2. Check that the API base URL is correct (http://localhost:8000/api)');
    console.log('   3. Verify the backend database has test data (php artisan db:seed)');
    console.log('   4. Check network connectivity between frontend and backend');
  }
};

/**
 * Quick API Health Check
 */
export const checkAPIHealth = async () => {
  try {
    console.log('🔍 Checking API Health...');
    
    // Try to hit a simple endpoint
    const response = await fetch('http://localhost:8000/api/test');
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API is healthy');
      console.log(`   Server: ${data.data?.server || 'SPark Backend'}`);
      console.log(`   Timestamp: ${data.data?.timestamp || new Date().toISOString()}`);
      return true;
    } else {
      console.log('❌ API health check failed');
      console.log(`   Status: ${response.status}`);
      return false;
    }
  } catch (error: any) {
    console.log('❌ API health check failed');
    console.log(`   Error: ${error.message}`);
    return false;
  }
};

// Export for use in React Native app
export default {
  runIntegrationTests,
  checkAPIHealth,
};
