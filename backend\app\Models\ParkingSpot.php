<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ParkingSpot extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'host_id',
        'title',
        'description',
        'address',
        'latitude',
        'longitude',
        'hourly_rate',
        'daily_rate',
        'monthly_rate',
        'amenities',
        'photos',
        'is_active',
        'availability_schedule',
    ];

    protected function casts(): array
    {
        return [
            'latitude' => 'decimal:8',
            'longitude' => 'decimal:8',
            'hourly_rate' => 'decimal:2',
            'daily_rate' => 'decimal:2',
            'monthly_rate' => 'decimal:2',
            'amenities' => 'array',
            'photos' => 'array',
            'availability_schedule' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function host()
    {
        return $this->belongsTo(User::class, 'host_id');
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class, 'spot_id');
    }

    public function reviews()
    {
        return $this->hasMany(Review::class, 'spot_id');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeNearby($query, $latitude, $longitude, $radius = 5)
    {
        // Simplified distance calculation for better compatibility
        $latRange = $radius / 111; // Rough conversion: 1 degree ≈ 111 km
        $lngRange = $radius / (111 * cos(deg2rad($latitude)));

        return $query->whereBetween('latitude', [$latitude - $latRange, $latitude + $latRange])
                    ->whereBetween('longitude', [$longitude - $lngRange, $longitude + $lngRange])
                    ->orderBy('created_at', 'desc');
    }

    public function scopeAvailableBetween($query, $startTime, $endTime)
    {
        return $query->whereDoesntHave('bookings', function ($q) use ($startTime, $endTime) {
            $q->where(function ($query) use ($startTime, $endTime) {
                $query->where('start_time', '<', $endTime)
                      ->where('end_time', '>', $startTime)
                      ->whereIn('status', ['confirmed', 'active']);
            });
        });
    }

    public function scopeWithinPriceRange($query, $minPrice, $maxPrice)
    {
        return $query->whereBetween('hourly_rate', [$minPrice, $maxPrice]);
    }

    public function scopeWithAmenities($query, $amenities)
    {
        foreach ($amenities as $amenity) {
            $query->whereJsonContains('amenities', $amenity);
        }
        return $query;
    }

    /**
     * Accessors
     */
    public function getRatingAttribute()
    {
        return $this->reviews()->avg('rating') ?? 0;
    }

    public function getReviewCountAttribute()
    {
        return $this->reviews()->count();
    }

    /**
     * Helper methods
     */
    public function isAvailableAt($dateTime)
    {
        $dayOfWeek = date('w', strtotime($dateTime));
        $time = date('H:i', strtotime($dateTime));

        if (!$this->availability_schedule) {
            return true; // Default to available if no schedule set
        }

        foreach ($this->availability_schedule as $schedule) {
            if ($schedule['day_of_week'] == $dayOfWeek) {
                return $schedule['is_available'] &&
                       $time >= $schedule['start_time'] &&
                       $time <= $schedule['end_time'];
            }
        }

        return false;
    }

    public function hasConflictingBooking($startTime, $endTime, $excludeBookingId = null)
    {
        $query = $this->bookings()
            ->where('start_time', '<', $endTime)
            ->where('end_time', '>', $startTime)
            ->whereIn('status', ['confirmed', 'active']);

        if ($excludeBookingId) {
            $query->where('id', '!=', $excludeBookingId);
        }

        return $query->exists();
    }
}
