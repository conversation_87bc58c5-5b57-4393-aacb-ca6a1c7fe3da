export interface OSMParkingSpot {
  id: string;
  type: 'node' | 'way' | 'relation';
  lat: number;
  lon: number;
  tags: {
    amenity: 'parking';
    name?: string;
    'addr:street'?: string;
    'addr:housenumber'?: string;
    'addr:city'?: string;
    'addr:postcode'?: string;
    capacity?: string;
    parking?: 'surface' | 'multi-storey' | 'underground' | 'street_side';
    fee?: 'yes' | 'no';
    access?: 'yes' | 'private' | 'customers' | 'permissive';
    operator?: string;
    opening_hours?: string;
    maxstay?: string;
    surface?: string;
    covered?: 'yes' | 'no';
    wheelchair?: 'yes' | 'no' | 'limited';
    [key: string]: any;
  };
  distance?: number; // in meters
}

export interface ProcessedParkingSpot {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  distance: number; // in meters
  capacity?: number;
  type: 'surface' | 'multi-storey' | 'underground' | 'street_side' | 'unknown';
  fee: boolean;
  access: 'public' | 'private' | 'customers' | 'unknown';
  operator?: string;
  openingHours?: string;
  maxStay?: string;
  amenities: string[];
  rating: number; // Mock rating since OSM doesn't provide this
  isAvailable: boolean; // Mock availability
}

export interface OverpassResponse {
  version: number;
  generator: string;
  osm3s: {
    timestamp_osm_base: string;
    copyright: string;
  };
  elements: OSMParkingSpot[];
}

export interface ParkingSearchParams {
  latitude: number;
  longitude: number;
  radius: number; // in meters, max 1500
  maxResults?: number;
}

export interface ParkingSearchResult {
  spots: ProcessedParkingSpot[];
  totalFound: number;
  searchRadius: number;
  userLocation: {
    latitude: number;
    longitude: number;
  };
  cached: boolean;
  timestamp: number;
}

export interface CachedParkingData {
  result: ParkingSearchResult;
  timestamp: number;
  expiration: number;
  location: {
    latitude: number;
    longitude: number;
  };
}
