import React from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';

// Conditional imports for platform compatibility
let MapView: any = null;
let Marker: any = null;
let PROVIDER_GOOGLE: any = null;

if (Platform.OS !== 'web') {
  try {
    const mapModule = require('react-native-maps');
    MapView = mapModule.default;
    Marker = mapModule.Marker;
    PROVIDER_GOOGLE = mapModule.PROVIDER_GOOGLE;
  } catch (error) {
    console.warn('react-native-maps not available');
  }
}

interface PlatformMapViewProps {
  style?: any;
  region?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  showsUserLocation?: boolean;
  showsMyLocationButton?: boolean;
  provider?: any;
  children?: React.ReactNode;
  onRegionChangeComplete?: (region: any) => void;
  showsTraffic?: boolean;
  showsBuildings?: boolean;
  showsIndoors?: boolean;
  loadingEnabled?: boolean;
  mapType?: string;
}

interface PlatformMarkerProps {
  coordinate: {
    latitude: number;
    longitude: number;
  };
  title?: string;
  description?: string;
  pinColor?: string;
  onPress?: () => void;
  children?: React.ReactNode;
}

// Web fallback map component
const WebMapFallback: React.FC<PlatformMapViewProps> = ({ style, region, children }) => {
  return (
    <View style={[styles.webMapContainer, style]}>
      <Text style={styles.webMapTitle}>🗺️ Map View</Text>
      <Text style={styles.webMapSubtitle}>
        {region ? `Location: ${region.latitude.toFixed(4)}, ${region.longitude.toFixed(4)}` : 'Loading location...'}
      </Text>
      <Text style={styles.webMapNote}>
        📱 For full map functionality, please use the mobile app
      </Text>
      {children && (
        <View style={styles.webMarkersContainer}>
          <Text style={styles.webMarkersTitle}>Parking Spots:</Text>
          {children}
        </View>
      )}
    </View>
  );
};

// Web fallback marker component
const WebMarkerFallback: React.FC<PlatformMarkerProps> = ({ 
  coordinate, 
  title, 
  description, 
  pinColor = '#1a9bff',
  onPress 
}) => {
  return (
    <View style={styles.webMarker}>
      <View style={[styles.webMarkerPin, { backgroundColor: pinColor }]} />
      <View style={styles.webMarkerInfo}>
        <Text style={styles.webMarkerTitle}>{title}</Text>
        {description && <Text style={styles.webMarkerDescription}>{description}</Text>}
        <Text style={styles.webMarkerCoords}>
          📍 {coordinate.latitude.toFixed(4)}, {coordinate.longitude.toFixed(4)}
        </Text>
      </View>
    </View>
  );
};

// Platform-specific map component
export const PlatformMapView: React.FC<PlatformMapViewProps> = (props) => {
  if (Platform.OS === 'web' || !MapView) {
    return <WebMapFallback {...props} />;
  }

  return (
    <MapView
      provider={PROVIDER_GOOGLE}
      {...props}
    />
  );
};

// Platform-specific marker component
export const PlatformMarker: React.FC<PlatformMarkerProps> = (props) => {
  if (Platform.OS === 'web' || !Marker) {
    return <WebMarkerFallback {...props} />;
  }

  return <Marker {...props} />;
};

// Export the provider constant
export const PLATFORM_PROVIDER_GOOGLE = PROVIDER_GOOGLE;

const styles = StyleSheet.create({
  webMapContainer: {
    backgroundColor: '#f0f9ff',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#1a9bff',
    borderStyle: 'dashed',
  },
  webMapTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a9bff',
    marginBottom: 8,
  },
  webMapSubtitle: {
    fontSize: 16,
    color: '#374151',
    marginBottom: 8,
    textAlign: 'center',
  },
  webMapNote: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    fontStyle: 'italic',
    marginBottom: 16,
  },
  webMarkersContainer: {
    width: '100%',
    maxHeight: 200,
  },
  webMarkersTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  webMarker: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 12,
    marginBottom: 8,
    borderRadius: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  webMarkerPin: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  webMarkerInfo: {
    flex: 1,
  },
  webMarkerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 2,
  },
  webMarkerDescription: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 2,
  },
  webMarkerCoords: {
    fontSize: 11,
    color: '#9ca3af',
  },
});

export default PlatformMapView;
