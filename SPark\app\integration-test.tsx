import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import ExpoAPITest from '../src/components/ExpoAPITest';
import NetworkDiagnostic from '../src/components/NetworkDiagnostic';

export default function IntegrationTestScreen() {
  const [activeTab, setActiveTab] = useState<'api' | 'diagnostic'>('diagnostic');

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'diagnostic' && styles.activeTab]}
          onPress={() => setActiveTab('diagnostic')}
        >
          <Text style={[styles.tabText, activeTab === 'diagnostic' && styles.activeTabText]}>
            🔍 Network Fix
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'api' && styles.activeTab]}
          onPress={() => setActiveTab('api')}
        >
          <Text style={[styles.tabText, activeTab === 'api' && styles.activeTabText]}>
            🧪 API Test
          </Text>
        </TouchableOpacity>
      </View>

      {activeTab === 'diagnostic' ? <NetworkDiagnostic /> : <ExpoAPITest />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 8,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: '#007AFF',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  activeTabText: {
    color: '#fff',
  },
});
