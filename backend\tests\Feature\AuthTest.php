<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AuthTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_register(): void
    {
        $response = $this->postJson('/api/auth/register', [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'user_type' => 'driver',
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user' => [
                            'id',
                            'email',
                            'first_name',
                            'last_name',
                            'user_type',
                        ],
                        'token'
                    ]
                ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'first_name' => '<PERSON>',
            'last_name' => 'Doe',
            'user_type' => 'driver',
        ]);
    }

    public function test_user_can_login(): void
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'first_name' => 'John',
            'last_name' => 'Doe',
            'user_type' => 'driver',
        ]);

        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user' => [
                            'id',
                            'email',
                            'first_name',
                            'last_name',
                        ],
                        'token'
                    ]
                ]);
    }

    public function test_user_cannot_login_with_invalid_credentials(): void
    {
        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Invalid credentials'
                ]);
    }

    public function test_authenticated_user_can_get_profile(): void
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'first_name' => 'John',
            'last_name' => 'Doe',
            'user_type' => 'driver',
        ]);

        $token = auth()->login($user);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/auth/profile');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'email',
                        'first_name',
                        'last_name',
                        'user_type',
                    ]
                ]);
    }

    public function test_registration_requires_valid_data(): void
    {
        $response = $this->postJson('/api/auth/register', [
            'email' => 'invalid-email',
            'password' => '123', // too short
            'first_name' => '',
            'user_type' => 'invalid',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'email',
                    'password',
                    'first_name',
                    'user_type',
                ]);
    }
}
