import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import Animated, {
  FadeInDown,
  FadeInUp,
  BounceInLeft,
  SlideInRight,
} from 'react-native-reanimated';
import { router } from 'expo-router';
import MobileInput from '../../src/components/ui/MobileInput';
import { UserIcon, ParkingIcon } from '../../src/components/ui/AnimatedIcon';
import { colors, spacing, typography, borderRadius } from '../../src/theme';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    setIsLoading(true);

    // Simulate login for demo
    setTimeout(() => {
      setIsLoading(false);
      if (email === '<EMAIL>' && password === 'password') {
        Alert.alert('Success', 'Login successful!', [
          { text: 'OK', onPress: () => router.replace('/(tabs)') }
        ]);
      } else {
        Alert.alert('Error', 'Invalid credentials. Try <EMAIL> / password');
      }
    }, 1000);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary[500]} />

      {/* Top Section with Gradient Background */}
      <Animated.View
        style={styles.topSection}
        entering={FadeInDown.duration(1000)}
      >
        <Animated.View
          style={styles.logoContainer}
          entering={BounceInLeft.duration(1200).delay(300)}
        >
          <ParkingIcon
            size={48}
            color={colors.white}
            animationType="pulse"
            trigger={true}
          />
        </Animated.View>
        <Animated.Text
          style={styles.appName}
          entering={SlideInRight.duration(800).delay(600)}
        >
          SPark
        </Animated.Text>
        <Animated.Text
          style={styles.tagline}
          entering={FadeInUp.duration(800).delay(900)}
        >
          Find parking in seconds
        </Animated.Text>
      </Animated.View>

      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          {/* Login Form Card */}
          <Animated.View
            style={styles.loginCard}
            entering={FadeInUp.duration(800).delay(1200)}
          >
            <Text style={styles.welcomeText}>Welcome back</Text>
            <Text style={styles.instructionText}>Sign in to your account</Text>

            <View style={styles.inputContainer}>
              <MobileInput
                label="Email"
                value={email}
                onChangeText={setEmail}
                placeholder="Enter your email"
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                leftIcon={<UserIcon size={20} color={colors.gray[400]} />}
              />

              <MobileInput
                label="Password"
                value={password}
                onChangeText={setPassword}
                placeholder="Enter your password"
                secureTextEntry
                autoCapitalize="none"
              />
            </View>

            <TouchableOpacity
              style={[
                styles.loginButton,
                isLoading && styles.loginButtonDisabled
              ]}
              onPress={handleLogin}
              disabled={isLoading}
              activeOpacity={0.8}
            >
              <Text style={styles.loginButtonText}>
                {isLoading ? 'Signing In...' : 'Sign In'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.forgotPassword}
              onPress={() => router.push('/auth/forgot-password')}
              activeOpacity={0.7}
            >
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>
          </Animated.View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>Don't have an account? </Text>
          <TouchableOpacity onPress={() => router.push('/auth/register')}>
            <Text style={styles.signUpText}>Sign Up</Text>
          </TouchableOpacity>
        </View>

        <Animated.View
          style={styles.demoInfo}
          entering={FadeInUp.duration(800).delay(1500)}
        >
          <Text style={styles.demoTitle}>Demo Credentials:</Text>
          <Text style={styles.demoText}>Email: <EMAIL></Text>
          <Text style={styles.demoText}>Password: password</Text>
        </Animated.View>
      </ScrollView>
    </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },

  // Top Section with Brand
  topSection: {
    backgroundColor: colors.primary[500],
    paddingTop: 60,
    paddingBottom: 40,
    paddingHorizontal: 24,
    alignItems: 'center',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  appName: {
    fontSize: 32,
    fontWeight: '700',
    fontFamily: typography.fontFamily.brand,
    color: colors.white,
    marginBottom: 8,
    letterSpacing: 1,
  },
  tagline: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    textAlign: 'center',
    fontWeight: '500',
  },

  // Form Section
  keyboardContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 32,
  },
  loginCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: '700',
    fontFamily: typography.fontFamily.brand,
    color: colors.black[900],
    marginBottom: 8,
  },
  instructionText: {
    fontSize: 16,
    color: colors.gray[600],
    marginBottom: 32,
    fontWeight: '500',
  },
  inputContainer: {
    marginBottom: 24,
  },
  loginButton: {
    backgroundColor: colors.primary[500],
    borderRadius: 12,
    paddingVertical: 18,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: colors.primary[500],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  loginButtonDisabled: {
    opacity: 0.6,
    shadowOpacity: 0.1,
  },
  loginButtonText: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.black[900],
    letterSpacing: 0.5,
  },
  forgotPassword: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  forgotPasswordText: {
    color: colors.primary[600],
    fontSize: 16,
    fontWeight: '600',
  },

  // Footer Section
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 20,
  },
  footerText: {
    fontSize: 16,
    color: colors.gray[600],
  },
  signUpText: {
    fontSize: 16,
    color: colors.primary[600],
    fontWeight: '600',
  },

  // Demo Info
  demoInfo: {
    backgroundColor: colors.primary[50],
    borderRadius: 8,
    padding: 16,
    marginTop: 20,
  },
  demoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary[700],
    marginBottom: 8,
  },
  demoText: {
    fontSize: 14,
    color: colors.primary[600],
    marginBottom: 4,
    fontWeight: '500',
  },
});


