/**
 * Simple test script to verify the OpenStreetMap parking feature
 * Run with: node test-parking-feature.js
 */

const axios = require('axios');

// Mock location data (San Francisco)
const testLocation = {
  latitude: 37.7749,
  longitude: -122.4194,
  radius: 1000 // 1km
};

// Calculate bounding box for the test location
function calculateBoundingBox(latitude, longitude, radiusMeters) {
  const earthRadius = 6371000; // Earth's radius in meters
  const latRadian = (latitude * Math.PI) / 180;
  
  const deltaLat = radiusMeters / earthRadius;
  const deltaLon = radiusMeters / (earthRadius * Math.cos(latRadian));
  
  const deltaLatDegrees = (deltaLat * 180) / Math.PI;
  const deltaLonDegrees = (deltaLon * 180) / Math.PI;
  
  return {
    north: latitude + deltaLatDegrees,
    south: latitude - deltaLatDegrees,
    east: longitude + deltaLonDegrees,
    west: longitude - deltaLonDegrees,
  };
}

// Test the Overpass API query
async function testOverpassAPI() {
  console.log('🧪 Testing OpenStreetMap Overpass API...');
  
  const bbox = calculateBoundingBox(
    testLocation.latitude,
    testLocation.longitude,
    testLocation.radius
  );

  // Overpass QL query to find parking amenities
  const query = `
    [out:json][timeout:25];
    (
      node["amenity"="parking"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
      way["amenity"="parking"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
      relation["amenity"="parking"](${bbox.south},${bbox.west},${bbox.north},${bbox.east});
    );
    out center meta;
  `;

  try {
    console.log(`📍 Searching for parking near: ${testLocation.latitude}, ${testLocation.longitude}`);
    console.log(`🔍 Search radius: ${testLocation.radius}m`);
    
    const response = await axios.post(
      'https://overpass-api.de/api/interpreter',
      query,
      {
        headers: {
          'Content-Type': 'text/plain',
          'User-Agent': 'SPark-Mobile-App-Test/1.0',
        },
        timeout: 10000,
      }
    );

    const data = response.data;
    console.log(`✅ API Response received`);
    console.log(`📊 Found ${data.elements.length} parking elements`);

    if (data.elements.length > 0) {
      console.log('\n🅿️  Sample parking spots:');
      data.elements.slice(0, 5).forEach((element, index) => {
        const name = element.tags?.name || `Parking ${element.type} ${element.id}`;
        const address = [
          element.tags?.['addr:housenumber'],
          element.tags?.['addr:street'],
          element.tags?.['addr:city']
        ].filter(Boolean).join(' ') || 'Address not available';
        
        console.log(`${index + 1}. ${name}`);
        console.log(`   📍 ${address}`);
        console.log(`   🏷️  Type: ${element.tags?.parking || 'unknown'}`);
        console.log(`   💰 Fee: ${element.tags?.fee || 'unknown'}`);
        console.log(`   🚗 Access: ${element.tags?.access || 'unknown'}`);
        console.log('');
      });
    } else {
      console.log('❌ No parking spots found in this area');
      console.log('💡 Try a different location or larger radius');
    }

    return data.elements;
  } catch (error) {
    console.error('❌ Error testing Overpass API:', error.message);
    
    if (error.code === 'ECONNABORTED') {
      console.log('⏰ Request timed out - this is normal for the Overpass API');
    } else if (error.response) {
      console.log(`🔴 HTTP ${error.response.status}: ${error.response.statusText}`);
    } else {
      console.log('🌐 Network error - check your internet connection');
    }
    
    return [];
  }
}

// Test distance calculation
function testDistanceCalculation() {
  console.log('\n🧮 Testing distance calculation...');
  
  // Test with known coordinates
  const point1 = { lat: 37.7749, lon: -122.4194 }; // San Francisco
  const point2 = { lat: 37.7849, lon: -122.4094 }; // Nearby point
  
  function calculateHaversineDistance(lat1, lon1, lat2, lon2) {
    const R = 6371000; // Earth's radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lon2 - lon1) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return Math.round(R * c);
  }
  
  const distance = calculateHaversineDistance(
    point1.lat, point1.lon,
    point2.lat, point2.lon
  );
  
  console.log(`📏 Distance between test points: ${distance}m`);
  console.log(`✅ Distance calculation ${distance > 0 && distance < 5000 ? 'PASSED' : 'FAILED'}`);
}

// Main test function
async function runTests() {
  console.log('🚀 SPark OpenStreetMap Parking Feature Test\n');
  
  // Test distance calculation
  testDistanceCalculation();
  
  // Test API connectivity
  await testOverpassAPI();
  
  console.log('\n✨ Test completed!');
  console.log('\n📝 Next steps:');
  console.log('1. Run the mobile app: npm start');
  console.log('2. Navigate to the Find Parking screen');
  console.log('3. Allow location permissions');
  console.log('4. Tap "Find Nearby Parking"');
  console.log('5. Check the bottom sheet for results');
}

// Run the tests
runTests().catch(console.error);
