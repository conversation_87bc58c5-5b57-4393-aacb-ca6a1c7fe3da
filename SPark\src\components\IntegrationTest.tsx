import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { authAPI, spotAPI, vehicleAPI } from '../services/api';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message?: string;
  data?: any;
}

const IntegrationTest: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'API Health Check', status: 'pending' },
    { name: 'User Login', status: 'pending' },
    { name: 'Spot Search', status: 'pending' },
    { name: 'Nearby Spots', status: 'pending' },
    { name: 'User Vehicles', status: 'pending' },
  ]);
  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (index: number, updates: Partial<TestResult>) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, ...updates } : test
    ));
  };

  const runTests = async () => {
    setIsRunning(true);
    
    try {
      // Test 1: API Health Check
      updateTest(0, { status: 'pending', message: 'Checking...' });
      try {
        const response = await fetch('http://localhost:8000/api/test');
        if (response.ok) {
          const data = await response.json();
          updateTest(0, { 
            status: 'success', 
            message: `Server: ${data.data?.server || 'SPark Backend'}`,
            data: data.data 
          });
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error: any) {
        updateTest(0, { 
          status: 'error', 
          message: error.message || 'Connection failed' 
        });
      }

      // Test 2: User Login
      updateTest(1, { status: 'pending', message: 'Logging in...' });
      try {
        const loginResponse = await authAPI.login({
          email: '<EMAIL>',
          password: 'password',
        });
        
        if (loginResponse.success && loginResponse.data) {
          updateTest(1, { 
            status: 'success', 
            message: `Welcome ${loginResponse.data.user.first_name}!`,
            data: loginResponse.data.user 
          });
        } else {
          throw new Error('Login failed - no data returned');
        }
      } catch (error: any) {
        updateTest(1, { 
          status: 'error', 
          message: error.message || 'Login failed' 
        });
      }

      // Test 3: Spot Search
      updateTest(2, { status: 'pending', message: 'Searching spots...' });
      try {
        const searchResponse = await spotAPI.searchSpots({
          latitude: 40.7128,
          longitude: -74.0060,
          radius: 10,
        });
        
        if (searchResponse.success && searchResponse.data) {
          updateTest(2, { 
            status: 'success', 
            message: `Found ${searchResponse.data.length} spots`,
            data: searchResponse.data 
          });
        } else {
          throw new Error('Search failed - no data returned');
        }
      } catch (error: any) {
        updateTest(2, { 
          status: 'error', 
          message: error.message || 'Search failed' 
        });
      }

      // Test 4: Nearby Spots
      updateTest(3, { status: 'pending', message: 'Getting nearby spots...' });
      try {
        const nearbyResponse = await spotAPI.getNearbySpots({
          latitude: 40.7128,
          longitude: -74.0060,
          radius: 5,
        });
        
        if (nearbyResponse.success && nearbyResponse.data) {
          updateTest(3, { 
            status: 'success', 
            message: `Found ${nearbyResponse.data.length} nearby spots`,
            data: nearbyResponse.data 
          });
        } else {
          throw new Error('Nearby search failed - no data returned');
        }
      } catch (error: any) {
        updateTest(3, { 
          status: 'error', 
          message: error.message || 'Nearby search failed' 
        });
      }

      // Test 5: User Vehicles
      updateTest(4, { status: 'pending', message: 'Getting vehicles...' });
      try {
        const vehiclesResponse = await vehicleAPI.getUserVehicles();
        
        if (vehiclesResponse.success && vehiclesResponse.data) {
          updateTest(4, { 
            status: 'success', 
            message: `Found ${vehiclesResponse.data.length} vehicles`,
            data: vehiclesResponse.data 
          });
        } else {
          throw new Error('Vehicles retrieval failed - no data returned');
        }
      } catch (error: any) {
        updateTest(4, { 
          status: 'error', 
          message: error.message || 'Vehicles retrieval failed' 
        });
      }

    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'pending': return '⏳';
      default: return '⚪';
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return '#4CAF50';
      case 'error': return '#F44336';
      case 'pending': return '#FF9800';
      default: return '#9E9E9E';
    }
  };

  const showTestDetails = (test: TestResult) => {
    if (test.data) {
      Alert.alert(
        test.name,
        JSON.stringify(test.data, null, 2),
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Frontend-Backend Integration Test</Text>
      <Text style={styles.subtitle}>
        Test the connection between React Native and Laravel backend
      </Text>

      <TouchableOpacity
        style={[styles.button, isRunning && styles.buttonDisabled]}
        onPress={runTests}
        disabled={isRunning}
      >
        <Text style={styles.buttonText}>
          {isRunning ? 'Running Tests...' : 'Run Integration Tests'}
        </Text>
      </TouchableOpacity>

      <ScrollView style={styles.testList}>
        {tests.map((test, index) => (
          <TouchableOpacity
            key={index}
            style={styles.testItem}
            onPress={() => showTestDetails(test)}
          >
            <View style={styles.testHeader}>
              <Text style={styles.testIcon}>{getStatusIcon(test.status)}</Text>
              <Text style={styles.testName}>{test.name}</Text>
            </View>
            {test.message && (
              <Text style={[styles.testMessage, { color: getStatusColor(test.status) }]}>
                {test.message}
              </Text>
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>

      <Text style={styles.footer}>
        Make sure Laravel backend is running on localhost:8000
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  testList: {
    flex: 1,
  },
  testItem: {
    backgroundColor: 'white',
    padding: 15,
    marginBottom: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  testHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  testIcon: {
    fontSize: 20,
    marginRight: 10,
  },
  testName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  testMessage: {
    fontSize: 14,
    marginTop: 5,
    marginLeft: 30,
  },
  footer: {
    textAlign: 'center',
    fontSize: 12,
    color: '#999',
    marginTop: 10,
  },
});

export default IntegrationTest;
