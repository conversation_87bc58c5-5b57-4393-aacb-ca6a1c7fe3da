<?php

echo "🔍 Simple Endpoint Test\n";
echo "========================\n\n";

function testEndpoint($url, $description) {
    echo "Testing: $description\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Status: $httpCode\n";
    
    if ($httpCode == 200) {
        $data = json_decode($response, true);
        if ($data) {
            echo "✅ JSON Response: " . json_encode($data, JSON_PRETTY_PRINT) . "\n";
        } else {
            echo "❌ Invalid JSON: " . substr($response, 0, 200) . "\n";
        }
    } else {
        echo "❌ Error: " . substr($response, 0, 200) . "\n";
    }
    echo "\n";
}

// Test the simple endpoint
testEndpoint('http://localhost:8000/api/test', 'Simple Test Endpoint');

// Test auth login with a simple POST
echo "Testing: Auth Login POST\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/auth/login');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'email' => '<EMAIL>',
    'password' => 'password'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Status: $httpCode\n";
if ($httpCode == 200) {
    $data = json_decode($response, true);
    if ($data && isset($data['success'])) {
        echo "✅ Login Response: " . json_encode($data, JSON_PRETTY_PRINT) . "\n";
    } else {
        echo "❌ Invalid JSON: " . substr($response, 0, 200) . "\n";
    }
} else {
    echo "❌ Login Error: " . substr($response, 0, 200) . "\n";
}
