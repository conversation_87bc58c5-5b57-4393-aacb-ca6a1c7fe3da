import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { SpotState, ParkingSpot, SearchFilters, ApiResponse } from '../../types';
import { spotAPI } from '../../services/api';

const initialState: SpotState = {
  spots: [],
  selectedSpot: null,
  searchResults: [],
  filters: {},
  isLoading: false,
  error: null,
};

// Async thunks
export const searchParkingSpots = createAsyncThunk(
  'spot/search',
  async (filters: SearchFilters, { rejectWithValue }) => {
    try {
      const response = await spotAPI.searchSpots(filters);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Search failed');
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const fetchSpotDetails = createAsyncThunk(
  'spot/fetchDetails',
  async (spotId: string, { rejectWithValue }) => {
    try {
      const response = await spotAPI.getSpotDetails(spotId);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch spot details');
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const fetchNearbySpots = createAsyncThunk(
  'spot/fetchNearby',
  async (location: { latitude: number; longitude: number; radius?: number }, { rejectWithValue }) => {
    try {
      const response = await spotAPI.getNearbySpots(location);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch nearby spots');
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const createParkingSpot = createAsyncThunk(
  'spot/create',
  async (spotData: Omit<ParkingSpot, 'id' | 'rating' | 'reviewCount' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      const response = await spotAPI.createSpot(spotData);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to create parking spot');
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const updateParkingSpot = createAsyncThunk(
  'spot/update',
  async (data: { spotId: string; updates: Partial<ParkingSpot> }, { rejectWithValue }) => {
    try {
      const response = await spotAPI.updateSpot(data.spotId, data.updates);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to update parking spot');
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const fetchHostSpots = createAsyncThunk(
  'spot/fetchHostSpots',
  async (_, { rejectWithValue }) => {
    try {
      const response = await spotAPI.getHostSpots();
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to fetch host spots');
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

export const toggleSpotAvailability = createAsyncThunk(
  'spot/toggleAvailability',
  async (data: { spotId: string; isActive: boolean }, { rejectWithValue }) => {
    try {
      const response = await spotAPI.toggleSpotAvailability(data.spotId, data.isActive);
      if (response.success && response.data) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to toggle spot availability');
    } catch (error: any) {
      return rejectWithValue(error.message || 'Network error');
    }
  }
);

const spotSlice = createSlice({
  name: 'spot',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSelectedSpot: (state, action: PayloadAction<ParkingSpot | null>) => {
      state.selectedSpot = action.payload;
    },
    setFilters: (state, action: PayloadAction<SearchFilters>) => {
      state.filters = action.payload;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
    },
    updateSpotInList: (state, action: PayloadAction<ParkingSpot>) => {
      const updatedSpot = action.payload;
      
      // Update in spots array
      const spotIndex = state.spots.findIndex(s => s.id === updatedSpot.id);
      if (spotIndex !== -1) {
        state.spots[spotIndex] = updatedSpot;
      }
      
      // Update in search results
      const searchIndex = state.searchResults.findIndex(s => s.id === updatedSpot.id);
      if (searchIndex !== -1) {
        state.searchResults[searchIndex] = updatedSpot;
      }
      
      // Update selected spot if it matches
      if (state.selectedSpot?.id === updatedSpot.id) {
        state.selectedSpot = updatedSpot;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Search parking spots
      .addCase(searchParkingSpots.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchParkingSpots.fulfilled, (state, action) => {
        state.isLoading = false;
        state.searchResults = action.payload;
        state.error = null;
      })
      .addCase(searchParkingSpots.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch spot details
      .addCase(fetchSpotDetails.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSpotDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedSpot = action.payload;
        state.error = null;
      })
      .addCase(fetchSpotDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch nearby spots
      .addCase(fetchNearbySpots.fulfilled, (state, action) => {
        state.spots = action.payload;
      })
      // Create parking spot
      .addCase(createParkingSpot.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createParkingSpot.fulfilled, (state, action) => {
        state.isLoading = false;
        state.spots.unshift(action.payload);
        state.error = null;
      })
      .addCase(createParkingSpot.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Update parking spot
      .addCase(updateParkingSpot.fulfilled, (state, action) => {
        const updatedSpot = action.payload;
        
        // Update in spots array
        const spotIndex = state.spots.findIndex(s => s.id === updatedSpot.id);
        if (spotIndex !== -1) {
          state.spots[spotIndex] = updatedSpot;
        }
        
        // Update selected spot if it matches
        if (state.selectedSpot?.id === updatedSpot.id) {
          state.selectedSpot = updatedSpot;
        }
      })
      // Fetch host spots
      .addCase(fetchHostSpots.fulfilled, (state, action) => {
        state.spots = action.payload;
      })
      // Toggle spot availability
      .addCase(toggleSpotAvailability.fulfilled, (state, action) => {
        const updatedSpot = action.payload;
        
        // Update in spots array
        const spotIndex = state.spots.findIndex(s => s.id === updatedSpot.id);
        if (spotIndex !== -1) {
          state.spots[spotIndex] = updatedSpot;
        }
      });
  },
});

export const { 
  clearError, 
  setSelectedSpot, 
  setFilters, 
  clearSearchResults, 
  updateSpotInList 
} = spotSlice.actions;

export default spotSlice.reducer;
