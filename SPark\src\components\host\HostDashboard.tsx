import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { router } from 'expo-router';
import { useWebSocket } from '../../services/websocket';
import NotificationService from '../../services/notifications';

const { width } = Dimensions.get('window');

interface HostStats {
  totalEarnings: number;
  monthlyEarnings: number;
  totalBookings: number;
  activeBookings: number;
  totalSpots: number;
  occupancyRate: number;
  averageRating: number;
  responseTime: string;
}

interface RecentBooking {
  id: string;
  guestName: string;
  spotTitle: string;
  startTime: string;
  endTime: string;
  amount: number;
  status: 'confirmed' | 'active' | 'completed' | 'cancelled';
}

interface SpotPerformance {
  id: string;
  title: string;
  totalBookings: number;
  totalEarnings: number;
  occupancyRate: number;
  averageRating: number;
  isActive: boolean;
}

export default function HostDashboard() {
  const [stats, setStats] = useState<HostStats>({
    totalEarnings: 2847.50,
    monthlyEarnings: 485.20,
    totalBookings: 127,
    activeBookings: 3,
    totalSpots: 5,
    occupancyRate: 78,
    averageRating: 4.7,
    responseTime: '< 1 hour',
  });

  const [recentBookings, setRecentBookings] = useState<RecentBooking[]>([
    {
      id: '1',
      guestName: 'John Smith',
      spotTitle: 'Downtown Garage A1',
      startTime: '2024-01-15T09:00:00Z',
      endTime: '2024-01-15T17:00:00Z',
      amount: 48.00,
      status: 'active',
    },
    {
      id: '2',
      guestName: 'Sarah Johnson',
      spotTitle: 'Business District Lot B3',
      startTime: '2024-01-15T14:00:00Z',
      endTime: '2024-01-15T18:00:00Z',
      amount: 32.00,
      status: 'confirmed',
    },
  ]);

  const [spotPerformance, setSpotPerformance] = useState<SpotPerformance[]>([
    {
      id: '1',
      title: 'Downtown Garage A1',
      totalBookings: 45,
      totalEarnings: 1250.00,
      occupancyRate: 85,
      averageRating: 4.8,
      isActive: true,
    },
    {
      id: '2',
      title: 'Business District Lot B3',
      totalBookings: 38,
      totalEarnings: 980.00,
      occupancyRate: 72,
      averageRating: 4.6,
      isActive: true,
    },
  ]);

  const [isRefreshing, setIsRefreshing] = useState(false);
  const webSocket = useWebSocket();

  useEffect(() => {
    setupWebSocketListeners();
    loadDashboardData();
    
    return () => {
      webSocket.off('newBooking', handleNewBooking);
      webSocket.off('bookingUpdate', handleBookingUpdate);
    };
  }, []);

  const setupWebSocketListeners = () => {
    webSocket.subscribeToHostUpdates('host_123'); // Replace with actual host ID
    webSocket.on('newBooking', handleNewBooking);
    webSocket.on('bookingUpdate', handleBookingUpdate);
  };

  const handleNewBooking = async (booking: any) => {
    // Update recent bookings
    setRecentBookings(prev => [booking, ...prev.slice(0, 4)]);
    
    // Send notification
    const notification = NotificationService.createHostNewBookingNotification(
      booking.spotTitle,
      booking.guestName,
      booking.id
    );
    await NotificationService.sendLocalNotification(notification);
    
    // Update stats
    setStats(prev => ({
      ...prev,
      totalBookings: prev.totalBookings + 1,
      activeBookings: prev.activeBookings + 1,
    }));
  };

  const handleBookingUpdate = (update: any) => {
    setRecentBookings(prev =>
      prev.map(booking =>
        booking.id === update.bookingId
          ? { ...booking, status: update.status }
          : booking
      )
    );
  };

  const loadDashboardData = async () => {
    try {
      // In a real app, this would fetch from API
      // For demo, we're using mock data
      console.log('Loading dashboard data...');
    } catch (error) {
      Alert.alert('Error', 'Failed to load dashboard data');
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadDashboardData();
    setIsRefreshing(false);
  };

  const toggleSpotAvailability = async (spotId: string, isActive: boolean) => {
    try {
      webSocket.updateSpotAvailability(spotId, isActive);
      
      setSpotPerformance(prev =>
        prev.map(spot =>
          spot.id === spotId ? { ...spot, isActive } : spot
        )
      );
      
      Alert.alert(
        'Success',
        `Spot ${isActive ? 'activated' : 'deactivated'} successfully`
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to update spot availability');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#10b981';
      case 'confirmed':
        return '#1a9bff';
      case 'completed':
        return '#6b7280';
      case 'cancelled':
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  const renderStatsCard = () => (
    <View style={styles.statsCard}>
      <Text style={styles.sectionTitle}>Overview</Text>
      
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{formatCurrency(stats.totalEarnings)}</Text>
          <Text style={styles.statLabel}>Total Earnings</Text>
        </View>
        
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{formatCurrency(stats.monthlyEarnings)}</Text>
          <Text style={styles.statLabel}>This Month</Text>
        </View>
        
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.totalBookings}</Text>
          <Text style={styles.statLabel}>Total Bookings</Text>
        </View>
        
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.activeBookings}</Text>
          <Text style={styles.statLabel}>Active Now</Text>
        </View>
        
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.occupancyRate}%</Text>
          <Text style={styles.statLabel}>Occupancy Rate</Text>
        </View>
        
        <View style={styles.statItem}>
          <Text style={styles.statValue}>★ {stats.averageRating}</Text>
          <Text style={styles.statLabel}>Rating</Text>
        </View>
      </View>
    </View>
  );

  const renderRecentBookings = () => (
    <View style={styles.bookingsCard}>
      <View style={styles.cardHeader}>
        <Text style={styles.sectionTitle}>Recent Bookings</Text>
        <TouchableOpacity onPress={() => router.push('/host/bookings')}>
          <Text style={styles.viewAllText}>View All</Text>
        </TouchableOpacity>
      </View>
      
      {recentBookings.map((booking) => (
        <TouchableOpacity
          key={booking.id}
          style={styles.bookingItem}
          onPress={() => router.push(`/booking/details?bookingId=${booking.id}`)}
        >
          <View style={styles.bookingInfo}>
            <Text style={styles.guestName}>{booking.guestName}</Text>
            <Text style={styles.spotTitle}>{booking.spotTitle}</Text>
            <Text style={styles.bookingTime}>
              {formatDate(booking.startTime)} - {formatDate(booking.endTime)}
            </Text>
          </View>
          
          <View style={styles.bookingMeta}>
            <Text style={styles.bookingAmount}>{formatCurrency(booking.amount)}</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(booking.status) }]}>
              <Text style={styles.statusText}>{booking.status}</Text>
            </View>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderSpotPerformance = () => (
    <View style={styles.spotsCard}>
      <View style={styles.cardHeader}>
        <Text style={styles.sectionTitle}>Spot Performance</Text>
        <TouchableOpacity onPress={() => router.push('/host/spots')}>
          <Text style={styles.viewAllText}>Manage</Text>
        </TouchableOpacity>
      </View>
      
      {spotPerformance.map((spot) => (
        <View key={spot.id} style={styles.spotItem}>
          <View style={styles.spotInfo}>
            <Text style={styles.spotTitle}>{spot.title}</Text>
            <Text style={styles.spotStats}>
              {spot.totalBookings} bookings • {spot.occupancyRate}% occupied • ★ {spot.averageRating}
            </Text>
            <Text style={styles.spotEarnings}>{formatCurrency(spot.totalEarnings)} earned</Text>
          </View>
          
          <TouchableOpacity
            style={[
              styles.toggleButton,
              { backgroundColor: spot.isActive ? '#10b981' : '#ef4444' }
            ]}
            onPress={() => toggleSpotAvailability(spot.id, !spot.isActive)}
          >
            <Text style={styles.toggleButtonText}>
              {spot.isActive ? 'Active' : 'Inactive'}
            </Text>
          </TouchableOpacity>
        </View>
      ))}
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.actionsCard}>
      <Text style={styles.sectionTitle}>Quick Actions</Text>
      
      <View style={styles.actionsGrid}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push('/host/create-listing')}
        >
          <Text style={styles.actionIcon}>➕</Text>
          <Text style={styles.actionText}>Add Spot</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push('/host/analytics')}
        >
          <Text style={styles.actionIcon}>📊</Text>
          <Text style={styles.actionText}>Analytics</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push('/host/earnings')}
        >
          <Text style={styles.actionIcon}>💰</Text>
          <Text style={styles.actionText}>Earnings</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push('/host/settings')}
        >
          <Text style={styles.actionIcon}>⚙️</Text>
          <Text style={styles.actionText}>Settings</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
      }
    >
      {renderStatsCard()}
      {renderRecentBookings()}
      {renderSpotPerformance()}
      {renderQuickActions()}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
    padding: 16,
  },
  statsCard: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  statItem: {
    width: (width - 80) / 2,
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  bookingsCard: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    color: '#1a9bff',
    fontWeight: '600',
  },
  bookingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  bookingInfo: {
    flex: 1,
  },
  guestName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 2,
  },
  spotTitle: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 2,
  },
  bookingTime: {
    fontSize: 12,
    color: '#9ca3af',
  },
  bookingMeta: {
    alignItems: 'flex-end',
  },
  bookingAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  spotsCard: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  spotItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  spotInfo: {
    flex: 1,
  },
  spotStats: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 2,
  },
  spotEarnings: {
    fontSize: 14,
    fontWeight: '600',
    color: '#10b981',
  },
  toggleButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  toggleButtonText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '600',
  },
  actionsCard: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionButton: {
    width: (width - 80) / 2,
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  actionIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },
});
