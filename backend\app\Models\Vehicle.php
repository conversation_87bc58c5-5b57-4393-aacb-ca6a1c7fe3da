<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vehicle extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'make',
        'model',
        'year',
        'license_plate',
        'color',
        'is_default',
    ];

    protected function casts(): array
    {
        return [
            'year' => 'integer',
            'is_default' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Scopes
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Accessors
     */
    public function getDisplayNameAttribute()
    {
        return $this->year . ' ' . $this->make . ' ' . $this->model;
    }

    /**
     * Helper methods
     */
    public function setAsDefault()
    {
        // Remove default from other vehicles for this user
        $this->user->vehicles()->where('id', '!=', $this->id)->update(['is_default' => false]);
        
        // Set this vehicle as default
        $this->is_default = true;
        $this->save();
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // When creating a new vehicle, if it's the user's first vehicle, make it default
        static::created(function ($vehicle) {
            if ($vehicle->user->vehicles()->count() === 1) {
                $vehicle->is_default = true;
                $vehicle->save();
            }
        });

        // When deleting a default vehicle, set another vehicle as default if available
        static::deleting(function ($vehicle) {
            if ($vehicle->is_default) {
                $nextVehicle = $vehicle->user->vehicles()
                    ->where('id', '!=', $vehicle->id)
                    ->first();
                
                if ($nextVehicle) {
                    $nextVehicle->is_default = true;
                    $nextVehicle->save();
                }
            }
        });
    }
}
