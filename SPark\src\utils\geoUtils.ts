/**
 * Calculates the distance between two geographic points using the Haversine formula
 * @param lat1 Latitude of first point
 * @param lon1 Longitude of first point
 * @param lat2 Latitude of second point
 * @param lon2 Longitude of second point
 * @returns Distance in meters
 */
export function calculateHaversineDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371000; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180; // φ, λ in radians
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  const distance = R * c; // in meters
  return Math.round(distance);
}

/**
 * Validates if coordinates are within valid ranges
 * @param latitude Latitude to validate
 * @param longitude Longitude to validate
 * @returns True if coordinates are valid
 */
export function validateCoordinates(latitude: number, longitude: number): boolean {
  return (
    latitude >= -90 &&
    latitude <= 90 &&
    longitude >= -180 &&
    longitude <= 180 &&
    !isNaN(latitude) &&
    !isNaN(longitude)
  );
}

/**
 * Formats distance for display
 * @param meters Distance in meters
 * @returns Formatted distance string
 */
export function formatDistance(meters: number): string {
  if (meters < 1000) {
    return `${meters}m`;
  } else {
    const km = (meters / 1000).toFixed(1);
    return `${km}km`;
  }
}

/**
 * Calculates the bounding box for a given center point and radius
 * @param latitude Center latitude
 * @param longitude Center longitude
 * @param radiusMeters Radius in meters
 * @returns Bounding box coordinates
 */
export function calculateBoundingBox(
  latitude: number,
  longitude: number,
  radiusMeters: number
): {
  north: number;
  south: number;
  east: number;
  west: number;
} {
  const earthRadius = 6371000; // Earth's radius in meters
  const latRadian = (latitude * Math.PI) / 180;
  
  const deltaLat = radiusMeters / earthRadius;
  const deltaLon = radiusMeters / (earthRadius * Math.cos(latRadian));
  
  const deltaLatDegrees = (deltaLat * 180) / Math.PI;
  const deltaLonDegrees = (deltaLon * 180) / Math.PI;
  
  return {
    north: latitude + deltaLatDegrees,
    south: latitude - deltaLatDegrees,
    east: longitude + deltaLonDegrees,
    west: longitude - deltaLonDegrees,
  };
}

/**
 * Checks if two locations are within a certain distance
 * @param lat1 First location latitude
 * @param lon1 First location longitude
 * @param lat2 Second location latitude
 * @param lon2 Second location longitude
 * @param maxDistanceMeters Maximum distance in meters
 * @returns True if locations are within the specified distance
 */
export function isWithinDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
  maxDistanceMeters: number
): boolean {
  const distance = calculateHaversineDistance(lat1, lon1, lat2, lon2);
  return distance <= maxDistanceMeters;
}

/**
 * Generates a cache key for location-based data
 * @param latitude Latitude
 * @param longitude Longitude
 * @param radius Search radius
 * @param precision Coordinate precision for caching (default: 3 decimal places)
 * @returns Cache key string
 */
export function generateLocationCacheKey(
  latitude: number,
  longitude: number,
  radius: number,
  precision: number = 3
): string {
  const lat = latitude.toFixed(precision);
  const lon = longitude.toFixed(precision);
  return `parking_${lat}_${lon}_${radius}`;
}
