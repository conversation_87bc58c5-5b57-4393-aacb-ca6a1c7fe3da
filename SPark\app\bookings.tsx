import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  SafeAreaView,
  StatusBar,
  FlatList,
} from 'react-native';
import Animated, {
  FadeInDown,
  FadeInRight,
  Layout,
} from 'react-native-reanimated';
import { router } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAppDispatch, useAppSelector } from '../src/store';
import { fetchUserBookings, fetchActiveBooking, cancelBooking, extendBooking } from '../src/store/slices/bookingSlice';
import {
  BackIcon,
  CarIcon,
  ClockIcon,
  LocationIcon,
  StarIcon,
  CheckIcon,
  CloseIcon
} from '../src/components/ui/AnimatedIcon';
import { colors, spacing, typography, borderRadius } from '../src/theme';
import { Booking } from '../src/types';

export default function BookingsScreen() {
  const dispatch = useAppDispatch();
  const { bookings, activeBooking, isLoading } = useAppSelector((state) => state.booking);
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'active' | 'upcoming' | 'past'>('active');

  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/auth/login');
      return;
    }
    
    loadBookings();
  }, [isAuthenticated]);

  const loadBookings = async () => {
    try {
      await Promise.all([
        dispatch(fetchUserBookings({})).unwrap(),
        dispatch(fetchActiveBooking()).unwrap(),
      ]);
    } catch (error) {
      console.error('Error loading bookings:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadBookings();
    setRefreshing(false);
  };

  const handleCancelBooking = (bookingId: string) => {
    Alert.alert(
      'Cancel Booking',
      'Are you sure you want to cancel this booking? This action cannot be undone.',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              await dispatch(cancelBooking(bookingId)).unwrap();
              Alert.alert('Success', 'Booking cancelled successfully.');
            } catch (error) {
              Alert.alert('Error', 'Failed to cancel booking. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleExtendBooking = (bookingId: string) => {
    // For now, extend by 1 hour
    const newEndTime = new Date(Date.now() + 60 * 60 * 1000).toISOString();
    
    Alert.alert(
      'Extend Booking',
      'Extend your parking session by 1 hour?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Extend',
          onPress: async () => {
            try {
              await dispatch(extendBooking({ bookingId, newEndTime })).unwrap();
              Alert.alert('Success', 'Booking extended successfully.');
            } catch (error) {
              Alert.alert('Error', 'Failed to extend booking. Please try again.');
            }
          },
        },
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return colors.success[500];
      case 'confirmed':
        return colors.primary[500];
      case 'pending':
        return colors.warning[500];
      case 'cancelled':
        return colors.error[500];
      case 'completed':
        return colors.gray[500];
      default:
        return colors.gray[500];
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'confirmed':
        return 'Confirmed';
      case 'pending':
        return 'Pending';
      case 'cancelled':
        return 'Cancelled';
      case 'completed':
        return 'Completed';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const renderActiveBooking = () => {
    if (!activeBooking) return null;

    const timeRemaining = new Date(activeBooking.endTime).getTime() - Date.now();
    const hoursRemaining = Math.max(0, Math.floor(timeRemaining / (1000 * 60 * 60)));
    const minutesRemaining = Math.max(0, Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60)));

    return (
      <Animated.View
        style={styles.activeBookingCard}
        entering={FadeInDown.duration(800).delay(300)}
      >
        <View style={styles.activeBookingHeader}>
          <Text style={styles.activeBookingTitle}>Active Parking Session</Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(activeBooking.status) }]}>
            <Text style={styles.statusText}>{getStatusText(activeBooking.status)}</Text>
          </View>
        </View>

        <Text style={styles.spotTitle}>Downtown Parking Garage</Text>
        <Text style={styles.spotAddress}>123 Main St, Downtown</Text>

        <View style={styles.timeInfo}>
          <Text style={styles.timeRemaining}>
            {hoursRemaining}h {minutesRemaining}m remaining
          </Text>
          <Text style={styles.endTime}>
            Ends at {formatTime(activeBooking.endTime)}
          </Text>
        </View>

        <View style={styles.activeBookingActions}>
          <Button
            title="Extend"
            onPress={() => handleExtendBooking(activeBooking.id)}
            variant="outline"
            style={styles.extendButton}
          />
          <Button
            title="View Details"
            onPress={() => router.push(`/booking/details?bookingId=${activeBooking.id}`)}
            style={styles.detailsButton}
          />
        </View>
      </Animated.View>
    );
  };

  const renderTabSelector = () => (
    <View style={styles.tabSelector}>
      {[
        { key: 'active', label: 'Active' },
        { key: 'upcoming', label: 'Upcoming' },
        { key: 'past', label: 'Past' },
      ].map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tab,
            selectedTab === tab.key && styles.tabActive,
          ]}
          onPress={() => setSelectedTab(tab.key as any)}
        >
          <Text
            style={[
              styles.tabText,
              selectedTab === tab.key && styles.tabTextActive,
            ]}
          >
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderBookingItem = (booking: Booking) => {
    const isUpcoming = new Date(booking.startTime) > new Date();
    const isPast = new Date(booking.endTime) < new Date();
    const canCancel = booking.status === 'confirmed' && isUpcoming;

    return (
      <Animated.View
        key={booking.id}
        style={styles.bookingCard}
        entering={FadeInDown.duration(600).delay(index * 100)}
        layout={Layout.springify()}
      >
        <View style={styles.bookingHeader}>
          <View style={styles.bookingInfo}>
            <Text style={styles.bookingTitle}>Downtown Parking Garage</Text>
            <Text style={styles.bookingAddress}>123 Main St, Downtown</Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(booking.status) }]}>
            <Text style={styles.statusText}>{getStatusText(booking.status)}</Text>
          </View>
        </View>

        <View style={styles.bookingDetails}>
          <View style={styles.bookingTime}>
            <Text style={styles.bookingDate}>{formatDate(booking.startTime)}</Text>
            <Text style={styles.bookingTimeRange}>
              {formatTime(booking.startTime)} - {formatTime(booking.endTime)}
            </Text>
          </View>
          <Text style={styles.bookingPrice}>${booking.totalAmount}</Text>
        </View>

        <View style={styles.bookingActions}>
          <Button
            title="View Details"
            onPress={() => router.push(`/booking/details?bookingId=${booking.id}`)}
            variant="outline"
            size="sm"
            style={styles.actionButton}
          />
          {canCancel && (
            <Button
              title="Cancel"
              onPress={() => handleCancelBooking(booking.id)}
              variant="danger"
              size="sm"
              style={styles.actionButton}
            />
          )}
        </View>
      </Animated.View>
    );
  };

  const getFilteredBookings = () => {
    const now = new Date();
    
    switch (selectedTab) {
      case 'active':
        return bookings.filter(b => b.status === 'active');
      case 'upcoming':
        return bookings.filter(b => 
          (b.status === 'confirmed' || b.status === 'pending') && 
          new Date(b.startTime) > now
        );
      case 'past':
        return bookings.filter(b => 
          b.status === 'completed' || 
          b.status === 'cancelled' || 
          (new Date(b.endTime) < now && b.status !== 'active')
        );
      default:
        return [];
    }
  };

  const renderBookingsList = () => {
    const filteredBookings = getFilteredBookings();

    if (filteredBookings.length === 0) {
      return (
        <Animated.View
          style={styles.emptyCard}
          entering={FadeInDown.duration(800)}
        >
          <CarIcon size={64} color={colors.gray[400]} />
          <Text style={styles.emptyTitle}>No {selectedTab} bookings</Text>
          <Text style={styles.emptyText}>
            {selectedTab === 'active' && 'You don\'t have any active parking sessions.'}
            {selectedTab === 'upcoming' && 'You don\'t have any upcoming bookings.'}
            {selectedTab === 'past' && 'You don\'t have any past bookings.'}
          </Text>
          {selectedTab !== 'past' && (
            <TouchableOpacity
              style={styles.findParkingButton}
              onPress={() => router.push('/search')}
            >
              <Text style={styles.findParkingButtonText}>Find Parking</Text>
            </TouchableOpacity>
          )}
        </Animated.View>
      );
    }

    return (
      <View style={styles.bookingsList}>
        {filteredBookings.map(renderBookingItem)}
      </View>
    );
  };

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  const renderHeader = () => (
    <Animated.View
      style={styles.header}
      entering={FadeInDown.duration(600)}
    >
      <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
        <BackIcon size={24} color={colors.black[900]} />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>My Bookings</Text>
      <View style={styles.placeholder} />
    </Animated.View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      {renderHeader()}

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {activeBooking && renderActiveBooking()}
        {renderTabSelector()}
        {renderBookingsList()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },

  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    fontFamily: typography.fontFamily.brand,
    color: colors.black[900],
  },
  placeholder: {
    width: 40,
  },

  scrollView: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  activeBookingCard: {
    marginBottom: spacing[4],
    backgroundColor: colors.primary[50],
    borderColor: colors.primary[200],
    borderWidth: 1,
  },
  activeBookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[3],
  },
  activeBookingTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.primary[700],
  },
  spotTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[1],
  },
  spotAddress: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    marginBottom: spacing[3],
  },
  timeInfo: {
    marginBottom: spacing[4],
  },
  timeRemaining: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
    marginBottom: spacing[1],
  },
  endTime: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
  },
  activeBookingActions: {
    flexDirection: 'row',
    gap: spacing[2],
  },
  extendButton: {
    flex: 1,
  },
  detailsButton: {
    flex: 1,
  },
  tabSelector: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: borderRadius.md,
    padding: spacing[1],
    marginBottom: spacing[4],
  },
  tab: {
    flex: 1,
    paddingVertical: spacing[2],
    alignItems: 'center',
    borderRadius: borderRadius.sm,
  },
  tabActive: {
    backgroundColor: colors.primary[500],
  },
  tabText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.gray[600],
  },
  tabTextActive: {
    color: colors.white,
  },
  bookingsList: {
    gap: spacing[3],
  },
  bookingCard: {
    marginBottom: spacing[3],
  },
  bookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing[3],
  },
  bookingInfo: {
    flex: 1,
  },
  bookingTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[1],
  },
  bookingAddress: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
  },
  statusBadge: {
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: borderRadius.sm,
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    color: colors.white,
  },
  bookingDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[3],
  },
  bookingTime: {
    flex: 1,
  },
  bookingDate: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.gray[900],
    marginBottom: spacing[1],
  },
  bookingTimeRange: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
  },
  bookingPrice: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
  },
  bookingActions: {
    flexDirection: 'row',
    gap: spacing[2],
  },
  actionButton: {
    flex: 1,
  },
  emptyCard: {
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[2],
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing[4],
  },
  findParkingButton: {
    backgroundColor: colors.primary[500],
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    marginTop: 16,
  },
  findParkingButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.black[900],
  },
});
