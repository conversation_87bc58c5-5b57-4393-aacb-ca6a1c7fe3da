<?php

namespace App\Http\Controllers;

use App\Models\Review;
use App\Models\Booking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ReviewController extends Controller
{
    /**
     * Get reviews (for user or spot)
     */
    public function index(Request $request)
    {
        try {
            $query = Review::with(['reviewer', 'reviewee', 'spot', 'booking']);

            // Filter by spot
            if ($request->has('spot_id')) {
                $query->forSpot($request->spot_id);
            }

            // Filter by reviewee (user being reviewed)
            if ($request->has('reviewee_id')) {
                $query->forReviewee($request->reviewee_id);
            }

            // Filter by reviewer
            if ($request->has('reviewer_id')) {
                $query->byReviewer($request->reviewer_id);
            }

            // Filter by type
            if ($request->has('type')) {
                if ($request->type === 'driver_to_host') {
                    $query->driverToHost();
                } elseif ($request->type === 'host_to_driver') {
                    $query->hostToDriver();
                }
            }

            // Filter by minimum rating
            if ($request->has('min_rating')) {
                $query->highRated($request->min_rating);
            }

            // Pagination
            $page = $request->get('page', 1);
            $limit = min($request->get('limit', 20), 50);
            
            $reviews = $query->orderBy('created_at', 'desc')
                ->paginate($limit, ['*'], 'page', $page);

            return response()->json([
                'success' => true,
                'data' => $reviews->items(),
                'pagination' => [
                    'current_page' => $reviews->currentPage(),
                    'last_page' => $reviews->lastPage(),
                    'per_page' => $reviews->perPage(),
                    'total' => $reviews->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get reviews',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new review
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), Review::rules());

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $booking = Booking::findOrFail($request->booking_id);

            // Check if user is part of this booking
            if ($booking->driver_id !== auth()->id() && $booking->host_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized to review this booking'
                ], 403);
            }

            // Check if booking is completed
            if ($booking->status !== 'completed') {
                return response()->json([
                    'success' => false,
                    'message' => 'Can only review completed bookings'
                ], 400);
            }

            // Determine review type and participants
            $reviewerId = auth()->id();
            $revieweeId = null;
            $type = $request->type;

            if ($type === 'driver_to_host' && $booking->driver_id === auth()->id()) {
                $revieweeId = $booking->host_id;
            } elseif ($type === 'host_to_driver' && $booking->host_id === auth()->id()) {
                $revieweeId = $booking->driver_id;
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid review type for this user'
                ], 400);
            }

            // Check if review already exists
            $existingReview = Review::where('booking_id', $request->booking_id)
                ->where('type', $type)
                ->first();

            if ($existingReview) {
                return response()->json([
                    'success' => false,
                    'message' => 'Review already exists for this booking'
                ], 409);
            }

            $review = Review::create([
                'booking_id' => $request->booking_id,
                'spot_id' => $booking->spot_id,
                'reviewer_id' => $reviewerId,
                'reviewee_id' => $revieweeId,
                'rating' => $request->rating,
                'comment' => $request->comment,
                'type' => $type,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Review created successfully',
                'data' => $review->load(['reviewer', 'reviewee', 'spot'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create review',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get review details
     */
    public function show($id)
    {
        try {
            $review = Review::with(['reviewer', 'reviewee', 'spot', 'booking'])
                ->findOrFail($id);

            // Check if user has access to this review
            if ($review->reviewer_id !== auth()->id() && 
                $review->reviewee_id !== auth()->id() && 
                $review->booking->driver_id !== auth()->id() && 
                $review->booking->host_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized to view this review'
                ], 403);
            }

            return response()->json([
                'success' => true,
                'data' => $review
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Review not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update review
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'rating' => 'sometimes|required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $review = Review::where('reviewer_id', auth()->id())
                ->findOrFail($id);

            $review->update($request->only(['rating', 'comment']));

            return response()->json([
                'success' => true,
                'message' => 'Review updated successfully',
                'data' => $review->load(['reviewer', 'reviewee', 'spot'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update review',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete review
     */
    public function destroy($id)
    {
        try {
            $review = Review::where('reviewer_id', auth()->id())
                ->findOrFail($id);

            $review->delete();

            return response()->json([
                'success' => true,
                'message' => 'Review deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete review',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
