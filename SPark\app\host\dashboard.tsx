import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  SafeAreaView,
  StatusBar,
  FlatList,
} from 'react-native';
import Animated, {
  FadeInDown,
  FadeInRight,
  SlideInUp,
  Layout,
} from 'react-native-reanimated';
import { router } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAppDispatch, useAppSelector } from '../../src/store';
import { fetchHostSpots, toggleSpotAvailability } from '../../src/store/slices/spotSlice';
import { bookingAPI } from '../../src/services/api';
import {
  BackIcon,
  PlusIcon,
  StarIcon,
  LocationIcon,
  WalletIcon,
  CarIcon,
  SettingsIcon,
  EditIcon
} from '../../src/components/ui/AnimatedIcon';
import { colors, spacing, typography, borderRadius } from '../../src/theme';

export default function HostDashboardScreen() {
  const dispatch = useAppDispatch();
  const { spots, isLoading } = useAppSelector((state) => state.spot);
  const { isAuthenticated, user } = useAppSelector((state) => state.auth);
  
  const [refreshing, setRefreshing] = useState(false);
  const [hostBookings, setHostBookings] = useState<any[]>([]);
  const [earnings, setEarnings] = useState({
    today: 0,
    thisWeek: 0,
    thisMonth: 0,
    total: 0,
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/auth/login');
      return;
    }
    
    if (user?.userType !== 'host') {
      Alert.alert('Access Denied', 'This section is only available for hosts.');
      router.back();
      return;
    }
    
    loadDashboardData();
  }, [isAuthenticated, user]);

  const loadDashboardData = async () => {
    try {
      // Load host spots
      await dispatch(fetchHostSpots()).unwrap();
      
      // Load host bookings
      const bookingsResponse = await bookingAPI.getHostBookings({ limit: 10 });
      if (bookingsResponse.success && bookingsResponse.data) {
        setHostBookings(bookingsResponse.data);
      }
      
      // Mock earnings data - in real app, this would come from API
      setEarnings({
        today: 45.50,
        thisWeek: 312.75,
        thisMonth: 1247.20,
        total: 5623.85,
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const handleToggleSpotAvailability = async (spotId: string, isActive: boolean) => {
    try {
      await dispatch(toggleSpotAvailability({ spotId, isActive })).unwrap();
      Alert.alert(
        'Success',
        `Parking spot ${isActive ? 'activated' : 'deactivated'} successfully.`
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to update spot availability. Please try again.');
    }
  };

  const renderEarningsOverview = () => (
    <Card style={styles.earningsCard}>
      <Text style={styles.sectionTitle}>Earnings Overview</Text>
      
      <View style={styles.earningsGrid}>
        <View style={styles.earningItem}>
          <Text style={styles.earningValue}>${earnings.today.toFixed(2)}</Text>
          <Text style={styles.earningLabel}>Today</Text>
        </View>
        
        <View style={styles.earningItem}>
          <Text style={styles.earningValue}>${earnings.thisWeek.toFixed(2)}</Text>
          <Text style={styles.earningLabel}>This Week</Text>
        </View>
        
        <View style={styles.earningItem}>
          <Text style={styles.earningValue}>${earnings.thisMonth.toFixed(2)}</Text>
          <Text style={styles.earningLabel}>This Month</Text>
        </View>
        
        <View style={styles.earningItem}>
          <Text style={styles.earningValue}>${earnings.total.toFixed(2)}</Text>
          <Text style={styles.earningLabel}>Total</Text>
        </View>
      </View>
      
      <Button
        title="View Detailed Earnings"
        onPress={() => Alert.alert('Coming Soon', 'Detailed earnings view will be available soon.')}
        variant="outline"
        fullWidth
        style={styles.earningsButton}
      />
    </Card>
  );

  const renderQuickActions = () => (
    <Card style={styles.actionsCard}>
      <Text style={styles.sectionTitle}>Quick Actions</Text>
      
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push('/host/create-listing')}
        >
          <View style={styles.actionIcon}>
            <Text style={styles.actionIconText}>➕</Text>
          </View>
          <Text style={styles.actionTitle}>Add New Spot</Text>
          <Text style={styles.actionSubtitle}>Create a new parking listing</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => Alert.alert('Coming Soon', 'Analytics will be available soon.')}
        >
          <View style={styles.actionIcon}>
            <Text style={styles.actionIconText}>📊</Text>
          </View>
          <Text style={styles.actionTitle}>View Analytics</Text>
          <Text style={styles.actionSubtitle}>Track performance metrics</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => Alert.alert('Coming Soon', 'Payout settings will be available soon.')}
        >
          <View style={styles.actionIcon}>
            <Text style={styles.actionIconText}>💰</Text>
          </View>
          <Text style={styles.actionTitle}>Payout Settings</Text>
          <Text style={styles.actionSubtitle}>Manage payment preferences</Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  const renderMySpots = () => (
    <Card style={styles.spotsCard}>
      <View style={styles.spotsHeader}>
        <Text style={styles.sectionTitle}>My Parking Spots</Text>
        <Button
          title="Add Spot"
          onPress={() => router.push('/host/create-listing')}
          size="sm"
          style={styles.addSpotButton}
        />
      </View>
      
      {spots.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyTitle}>No parking spots yet</Text>
          <Text style={styles.emptyText}>
            Create your first parking spot listing to start earning money.
          </Text>
          <Button
            title="Create First Listing"
            onPress={() => router.push('/host/create-listing')}
            style={styles.createFirstButton}
          />
        </View>
      ) : (
        <View style={styles.spotsList}>
          {spots.map((spot) => (
            <View key={spot.id} style={styles.spotItem}>
              <View style={styles.spotInfo}>
                <Text style={styles.spotTitle}>{spot.title}</Text>
                <Text style={styles.spotAddress}>{spot.address}</Text>
                <Text style={styles.spotPrice}>${spot.hourlyRate}/hour</Text>
              </View>
              
              <View style={styles.spotActions}>
                <View style={[
                  styles.statusIndicator,
                  { backgroundColor: spot.isActive ? colors.success[500] : colors.gray[400] }
                ]}>
                  <Text style={styles.statusText}>
                    {spot.isActive ? 'Active' : 'Inactive'}
                  </Text>
                </View>
                
                <View style={styles.spotButtons}>
                  <TouchableOpacity
                    style={styles.toggleButton}
                    onPress={() => handleToggleSpotAvailability(spot.id, !spot.isActive)}
                  >
                    <Text style={styles.toggleButtonText}>
                      {spot.isActive ? 'Deactivate' : 'Activate'}
                    </Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={styles.editButton}
                    onPress={() => router.push(`/host/edit-listing?spotId=${spot.id}`)}
                  >
                    <Text style={styles.editButtonText}>Edit</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          ))}
        </View>
      )}
    </Card>
  );

  const renderRecentBookings = () => (
    <Card style={styles.bookingsCard}>
      <Text style={styles.sectionTitle}>Recent Bookings</Text>
      
      {hostBookings.length === 0 ? (
        <View style={styles.emptyBookings}>
          <Text style={styles.emptyText}>No recent bookings</Text>
        </View>
      ) : (
        <View style={styles.bookingsList}>
          {hostBookings.slice(0, 5).map((booking, index) => (
            <View key={index} style={styles.bookingItem}>
              <View style={styles.bookingInfo}>
                <Text style={styles.bookingSpot}>Downtown Parking Garage</Text>
                <Text style={styles.bookingDate}>
                  {new Date().toLocaleDateString()} • 2 hours
                </Text>
              </View>
              <Text style={styles.bookingAmount}>$24.00</Text>
            </View>
          ))}
        </View>
      )}
      
      <Button
        title="View All Bookings"
        onPress={() => Alert.alert('Coming Soon', 'Detailed bookings view will be available soon.')}
        variant="outline"
        fullWidth
        style={styles.viewAllButton}
      />
    </Card>
  );

  if (!isAuthenticated || user?.userType !== 'host') {
    return null; // Will redirect or show error
  }

  const renderHeader = () => (
    <Animated.View
      style={styles.header}
      entering={FadeInDown.duration(600)}
    >
      <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
        <BackIcon size={24} color={colors.white} />
      </TouchableOpacity>
      <View style={styles.headerContent}>
        <Text style={styles.welcomeText}>Welcome back, {user?.firstName || 'Host'}!</Text>
        <Text style={styles.subtitleText}>Manage your parking spots and earnings</Text>
      </View>
      <TouchableOpacity style={styles.settingsButton} onPress={() => router.push('/host/settings')}>
        <SettingsIcon size={24} color={colors.white} />
      </TouchableOpacity>
    </Animated.View>
  );

  const renderMobileEarningsCard = () => (
    <Animated.View
      style={styles.mobileEarningsCard}
      entering={SlideInUp.duration(800).delay(200)}
    >
      <View style={styles.earningsHeader}>
        <WalletIcon size={24} color={colors.primary[500]} />
        <Text style={styles.earningsTitle}>Today's Earnings</Text>
      </View>
      <Text style={styles.earningsAmount}>$127.50</Text>
      <Text style={styles.earningsSubtext}>+23% from yesterday</Text>

      <View style={styles.earningsStats}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>8</Text>
          <Text style={styles.statLabel}>Bookings</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>4.8</Text>
          <Text style={styles.statLabel}>Rating</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>3</Text>
          <Text style={styles.statLabel}>Active Spots</Text>
        </View>
      </View>
    </Animated.View>
  );

  const renderQuickActionsMobile = () => (
    <Animated.View
      style={styles.quickActionsMobile}
      entering={FadeInRight.duration(800).delay(400)}
    >
      <TouchableOpacity
        style={styles.quickActionCard}
        onPress={() => router.push('/host/create-listing')}
      >
        <PlusIcon size={28} color={colors.primary[500]} />
        <Text style={styles.quickActionText}>Add Spot</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.quickActionCard}
        onPress={() => router.push('/host/earnings')}
      >
        <WalletIcon size={28} color={colors.primary[500]} />
        <Text style={styles.quickActionText}>Earnings</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.quickActionCard}
        onPress={() => router.push('/host/analytics')}
      >
        <StarIcon size={28} color={colors.primary[500]} />
        <Text style={styles.quickActionText}>Analytics</Text>
      </TouchableOpacity>
    </Animated.View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary[500]} />

      {renderHeader()}

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderMobileEarningsCard()}
        {renderQuickActionsMobile()}
        {renderMySpots()}
        {renderRecentBookings()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },

  // Header
  header: {
    backgroundColor: colors.primary[500],
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 24,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerContent: {
    flex: 1,
    marginHorizontal: 16,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: '700',
    fontFamily: typography.fontFamily.brand,
    color: colors.white,
    marginBottom: 4,
  },
  subtitleText: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.8)',
    fontWeight: '500',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Mobile Earnings Card
  mobileEarningsCard: {
    backgroundColor: colors.white,
    margin: 20,
    marginTop: -40, // Overlap with header
    borderRadius: 16,
    padding: 24,
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  earningsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  earningsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[700],
    marginLeft: 12,
  },
  earningsAmount: {
    fontSize: 36,
    fontWeight: '700',
    fontFamily: typography.fontFamily.brand,
    color: colors.black[900],
    marginBottom: 8,
  },
  earningsSubtext: {
    fontSize: 14,
    color: colors.success[600],
    fontWeight: '600',
    marginBottom: 24,
  },
  earningsStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.black[900],
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.gray[600],
    fontWeight: '500',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: colors.gray[200],
  },

  // Quick Actions Mobile
  quickActionsMobile: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 12,
  },
  quickActionCard: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[700],
    marginTop: 8,
    textAlign: 'center',
  },
  earningsCard: {
    marginBottom: spacing[4],
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[4],
  },
  earningsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing[3],
    marginBottom: spacing[4],
  },
  earningItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: spacing[3],
    backgroundColor: colors.primary[50],
    borderRadius: borderRadius.md,
  },
  earningValue: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary[600],
    marginBottom: spacing[1],
  },
  earningLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
  },
  earningsButton: {
    marginTop: spacing[2],
  },
  actionsCard: {
    marginBottom: spacing[4],
  },
  actionButtons: {
    gap: spacing[3],
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing[3],
    backgroundColor: colors.white,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  actionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing[3],
  },
  actionIconText: {
    fontSize: typography.fontSize.lg,
  },
  actionTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.medium,
    color: colors.gray[900],
    marginBottom: spacing[1],
    flex: 1,
  },
  actionSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    flex: 1,
  },
  spotsCard: {
    marginBottom: spacing[4],
  },
  spotsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[4],
  },
  addSpotButton: {
    paddingHorizontal: spacing[3],
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  emptyTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[2],
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing[4],
  },
  createFirstButton: {
    paddingHorizontal: spacing[6],
  },
  spotsList: {
    gap: spacing[3],
  },
  spotItem: {
    flexDirection: 'row',
    padding: spacing[3],
    backgroundColor: colors.white,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  spotInfo: {
    flex: 1,
  },
  spotTitle: {
    fontSize: typography.fontSize.base,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[1],
  },
  spotAddress: {
    fontSize: typography.fontSize.sm,
    color: colors.gray[600],
    marginBottom: spacing[1],
  },
  spotPrice: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
    color: colors.primary[600],
  },
  spotActions: {
    alignItems: 'flex-end',
  },
  statusIndicator: {
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: borderRadius.sm,
    marginBottom: spacing[2],
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    color: colors.white,
  },
  spotButtons: {
    flexDirection: 'row',
    gap: spacing[2],
  },
  toggleButton: {
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    backgroundColor: colors.gray[100],
    borderRadius: borderRadius.sm,
  },
  toggleButtonText: {
    fontSize: typography.fontSize.xs,
    color: colors.gray[700],
  },
  editButton: {
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    backgroundColor: colors.primary[100],
    borderRadius: borderRadius.sm,
  },
  editButtonText: {
    fontSize: typography.fontSize.xs,
    color: colors.primary[700],
  },
  bookingsCard: {
    marginBottom: spacing[4],
  },
  emptyBookings: {
    alignItems: 'center',
    paddingVertical: spacing[6],
  },
  bookingsList: {
    gap: spacing[2],
    marginBottom: spacing[4],
  },
  bookingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing[2],
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  bookingInfo: {
    flex: 1,
  },
  bookingSpot: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.gray[900],
    marginBottom: spacing[1],
  },
  bookingDate: {
    fontSize: typography.fontSize.xs,
    color: colors.gray[600],
  },
  bookingAmount: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semibold,
    color: colors.primary[600],
  },
  viewAllButton: {
    marginTop: spacing[2],
  },
});
