import React, { useEffect } from 'react';
import { ViewStyle } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { Ionicons, MaterialIcons, Feather, AntDesign, FontAwesome5 } from '@expo/vector-icons';
import { colors } from '../../theme';

export type IconLibrary = 'Ionicons' | 'MaterialIcons' | 'Feather' | 'AntDesign' | 'FontAwesome5';

export interface AnimatedIconProps {
  name: string;
  size?: number;
  color?: string;
  library?: IconLibrary;
  style?: ViewStyle;
  animationType?: 'bounce' | 'scale' | 'rotate' | 'pulse' | 'none';
  trigger?: boolean;
}

const AnimatedIcon: React.FC<AnimatedIconProps> = ({
  name,
  size = 24,
  color = colors.black[800],
  library = 'Ionicons',
  style,
  animationType = 'none',
  trigger = false,
}) => {
  const animationValue = useSharedValue(0);

  useEffect(() => {
    if (trigger) {
      switch (animationType) {
        case 'bounce':
          animationValue.value = withSpring(1, {
            damping: 8,
            stiffness: 100,
          });
          break;
        case 'scale':
          animationValue.value = withTiming(1, { duration: 200 });
          break;
        case 'rotate':
          animationValue.value = withTiming(1, { duration: 500 });
          break;
        case 'pulse':
          animationValue.value = withTiming(1, { duration: 300 });
          break;
        default:
          break;
      }
    } else {
      animationValue.value = withTiming(0, { duration: 200 });
    }
  }, [trigger, animationType]);

  const animatedStyle = useAnimatedStyle(() => {
    switch (animationType) {
      case 'bounce':
        return {
          transform: [
            {
              scale: interpolate(
                animationValue.value,
                [0, 1],
                [1, 1.2]
              ),
            },
          ],
        };
      case 'scale':
        return {
          transform: [
            {
              scale: interpolate(
                animationValue.value,
                [0, 1],
                [1, 1.1]
              ),
            },
          ],
        };
      case 'rotate':
        return {
          transform: [
            {
              rotate: `${interpolate(
                animationValue.value,
                [0, 1],
                [0, 360]
              )}deg`,
            },
          ],
        };
      case 'pulse':
        return {
          opacity: interpolate(
            animationValue.value,
            [0, 0.5, 1],
            [1, 0.5, 1]
          ),
        };
      default:
        return {};
    }
  });

  const iconProps = {
    name: name as any,
    size,
    color,
  };

  const renderIcon = () => {
    switch (library) {
      case 'MaterialIcons':
        return <MaterialIcons {...iconProps} />;
      case 'Feather':
        return <Feather {...iconProps} />;
      case 'AntDesign':
        return <AntDesign {...iconProps} />;
      case 'FontAwesome5':
        return <FontAwesome5 {...iconProps} />;
      case 'Ionicons':
      default:
        return <Ionicons {...iconProps} />;
    }
  };

  return (
    <Animated.View style={[animatedStyle, style]}>
      {renderIcon()}
    </Animated.View>
  );
};

// Enhanced predefined icon components with animations
export const SearchIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="search" library="Ionicons" {...props} />
);

export const LocationIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="location" library="Ionicons" {...props} />
);

export const CarIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="car" library="Ionicons" {...props} />
);

export const StarIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="star" library="Ionicons" {...props} />
);

export const HeartIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="heart" library="Ionicons" {...props} />
);

export const UserIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="person" library="Ionicons" {...props} />
);

export const SettingsIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="settings" library="Ionicons" {...props} />
);

export const NotificationIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="notifications" library="Ionicons" {...props} />
);

export const MenuIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="menu" library="Ionicons" {...props} />
);

export const BackIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="arrow-back" library="Ionicons" {...props} />
);

export const RadarIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="radio-button-on" library="Ionicons" {...props} />
);

export const CompassIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="compass" library="Ionicons" {...props} />
);

export const ScanIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="scan" library="Ionicons" {...props} />
);

export const ForwardIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="chevron-forward" library="Ionicons" {...props} />
);

export const CloseIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="close" library="Ionicons" {...props} />
);

export const CheckIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="checkmark" library="Ionicons" {...props} />
);

export const PlusIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="add" library="Ionicons" {...props} />
);

export const FilterIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="filter" library="Ionicons" {...props} />
);

export const MapIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="map" library="Ionicons" {...props} />
);

export const PaymentIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="card" library="Ionicons" {...props} />
);

export const WalletIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="wallet" library="Ionicons" {...props} />
);

export const EditIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="create" library="Ionicons" {...props} />
);

export const InfoIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="information-circle" library="Ionicons" {...props} />
);

export const ParkingIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="car-sport" library="Ionicons" {...props} />
);

export const CreditCardIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="card" library="Ionicons" {...props} />
);

export const ClockIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="time" library="Ionicons" {...props} />
);

export const ShareIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="share" library="Ionicons" {...props} />
);

export const RefreshIcon: React.FC<Omit<AnimatedIconProps, 'name' | 'library'>> = (props) => (
  <AnimatedIcon name="refresh" library="Ionicons" {...props} />
);

export default AnimatedIcon;
