import React from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';
import { LocationIcon, ParkingIcon } from './ui/AnimatedIcon';
import { colors, typography } from '../theme';

interface MapFallbackProps {
  style?: any;
  children?: React.ReactNode;
  showsUserLocation?: boolean;
  initialRegion?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  onRegionChange?: (region: any) => void;
  ref?: any;
}

interface MarkerProps {
  coordinate: {
    latitude: number;
    longitude: number;
  };
  title?: string;
  description?: string;
  pinColor?: string;
  children?: React.ReactNode;
}

interface CircleProps {
  center: {
    latitude: number;
    longitude: number;
  };
  radius: number;
  strokeColor?: string;
  strokeWidth?: number;
  fillColor?: string;
}

// Web/Fallback MapView Component
const WebMapView: React.FC<MapFallbackProps> = ({ 
  style, 
  children, 
  showsUserLocation,
  initialRegion,
  ...props 
}) => {
  return (
    <View style={[styles.mapContainer, style]}>
      <View style={styles.mapPlaceholder}>
        <LocationIcon size={64} color={colors.primary[300]} />
        <Text style={styles.mapTitle}>Interactive Map</Text>
        <Text style={styles.mapSubtitle}>
          {Platform.OS === 'web' 
            ? 'Map view is optimized for mobile devices'
            : 'Map integration coming soon!'
          }
        </Text>
        
        {showsUserLocation && (
          <View style={styles.locationIndicator}>
            <View style={styles.userLocationDot} />
            <Text style={styles.locationText}>Your Location</Text>
          </View>
        )}
        
        {initialRegion && (
          <Text style={styles.coordinatesText}>
            📍 {initialRegion.latitude.toFixed(4)}, {initialRegion.longitude.toFixed(4)}
          </Text>
        )}
      </View>
      
      {/* Render any markers or circles as a list */}
      <View style={styles.markersContainer}>
        {children}
      </View>
    </View>
  );
};

// Web/Fallback Marker Component
const WebMarker: React.FC<MarkerProps> = ({ 
  coordinate, 
  title, 
  description, 
  pinColor = colors.primary[500] 
}) => {
  return (
    <View style={styles.markerItem}>
      <View style={[styles.markerPin, { backgroundColor: pinColor }]}>
        <ParkingIcon size={12} color={colors.white} />
      </View>
      <View style={styles.markerInfo}>
        <Text style={styles.markerTitle}>{title}</Text>
        {description && (
          <Text style={styles.markerDescription}>{description}</Text>
        )}
        <Text style={styles.markerCoordinates}>
          {coordinate.latitude.toFixed(4)}, {coordinate.longitude.toFixed(4)}
        </Text>
      </View>
    </View>
  );
};

// Web/Fallback Circle Component
const WebCircle: React.FC<CircleProps> = ({ 
  center, 
  radius, 
  strokeColor = colors.primary[500] 
}) => {
  return (
    <View style={styles.circleItem}>
      <View style={[styles.circleIndicator, { borderColor: strokeColor }]} />
      <Text style={styles.circleText}>
        Search radius: {(radius / 1000).toFixed(1)}km around your location
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  mapContainer: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  mapPlaceholder: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  mapTitle: {
    fontSize: 24,
    fontWeight: '700',
    fontFamily: typography.fontFamily.brand,
    color: colors.black[900],
    marginTop: 16,
    marginBottom: 8,
  },
  mapSubtitle: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  locationIndicator: {
    alignItems: 'center',
    marginBottom: 16,
  },
  userLocationDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: colors.blue[500],
    marginBottom: 8,
    shadowColor: colors.blue[500],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.5,
    shadowRadius: 4,
    elevation: 4,
  },
  locationText: {
    fontSize: 14,
    color: colors.blue[600],
    fontWeight: '600',
  },
  coordinatesText: {
    fontSize: 12,
    color: colors.gray[500],
    fontFamily: 'monospace',
  },
  markersContainer: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    maxHeight: 200,
    shadowColor: colors.black[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  markerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  markerPin: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  markerInfo: {
    flex: 1,
  },
  markerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.black[900],
    marginBottom: 2,
  },
  markerDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 2,
  },
  markerCoordinates: {
    fontSize: 12,
    color: colors.gray[500],
    fontFamily: 'monospace',
  },
  circleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.primary[50],
    borderRadius: 8,
    marginBottom: 8,
  },
  circleIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    marginRight: 8,
  },
  circleText: {
    fontSize: 14,
    color: colors.primary[700],
    fontWeight: '500',
  },
});

// Export components with the same names as react-native-maps
export default WebMapView;
export const Marker = WebMarker;
export const Circle = WebCircle;
