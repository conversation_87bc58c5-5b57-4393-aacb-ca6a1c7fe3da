import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { router } from 'expo-router';
import { useAppSelector } from '../../src/store';
import { vehicleAPI } from '../../src/services/api';
import Button from '../../src/components/ui/Button';
import Card from '../../src/components/ui/Card';
import { colors, spacing, typography, borderRadius } from '../../src/theme';

interface Vehicle {
  id: string;
  make: string;
  model: string;
  year: number;
  licensePlate: string;
  color: string;
  isDefault: boolean;
}

export default function VehiclesScreen() {
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/auth/login');
      return;
    }
    
    loadVehicles();
  }, [isAuthenticated]);

  const loadVehicles = async () => {
    try {
      setIsLoading(true);
      const response = await vehicleAPI.getUserVehicles();
      if (response.success && response.data) {
        setVehicles(response.data);
      }
    } catch (error) {
      console.error('Error loading vehicles:', error);
      Alert.alert('Error', 'Failed to load vehicles. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadVehicles();
    setRefreshing(false);
  };

  const handleSetDefault = async (vehicleId: string) => {
    try {
      await vehicleAPI.setDefaultVehicle(vehicleId);
      await loadVehicles(); // Refresh the list
      Alert.alert('Success', 'Default vehicle updated successfully.');
    } catch (error) {
      Alert.alert('Error', 'Failed to set default vehicle. Please try again.');
    }
  };

  const handleDeleteVehicle = (vehicleId: string, vehicleName: string) => {
    Alert.alert(
      'Delete Vehicle',
      `Are you sure you want to delete ${vehicleName}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await vehicleAPI.deleteVehicle(vehicleId);
              await loadVehicles(); // Refresh the list
              Alert.alert('Success', 'Vehicle deleted successfully.');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete vehicle. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleAddVehicle = () => {
    // For now, show a coming soon alert
    // In a real app, this would navigate to an add vehicle screen
    Alert.alert('Coming Soon', 'Add vehicle functionality will be available soon.');
  };

  const handleEditVehicle = (vehicleId: string) => {
    // For now, show a coming soon alert
    // In a real app, this would navigate to an edit vehicle screen
    Alert.alert('Coming Soon', 'Edit vehicle functionality will be available soon.');
  };

  const renderVehicleItem = (vehicle: Vehicle) => (
    <Card key={vehicle.id} style={styles.vehicleCard}>
      <View style={styles.vehicleHeader}>
        <View style={styles.vehicleInfo}>
          <Text style={styles.vehicleName}>
            {vehicle.year} {vehicle.make} {vehicle.model}
          </Text>
          <Text style={styles.vehicleDetails}>
            {vehicle.color} • {vehicle.licensePlate}
          </Text>
        </View>
        
        {vehicle.isDefault && (
          <View style={styles.defaultBadge}>
            <Text style={styles.defaultText}>Default</Text>
          </View>
        )}
      </View>

      <View style={styles.vehicleActions}>
        {!vehicle.isDefault && (
          <Button
            title="Set as Default"
            onPress={() => handleSetDefault(vehicle.id)}
            variant="outline"
            size="sm"
            style={styles.actionButton}
          />
        )}
        
        <Button
          title="Edit"
          onPress={() => handleEditVehicle(vehicle.id)}
          variant="outline"
          size="sm"
          style={styles.actionButton}
        />
        
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteVehicle(vehicle.id, `${vehicle.year} ${vehicle.make} ${vehicle.model}`)}
        >
          <Text style={styles.deleteButtonText}>🗑️</Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <Card style={styles.emptyCard}>
      <View style={styles.emptyContent}>
        <Text style={styles.emptyIcon}>🚗</Text>
        <Text style={styles.emptyTitle}>No vehicles added</Text>
        <Text style={styles.emptyText}>
          Add your vehicles to make booking parking spots faster and easier.
        </Text>
        <Button
          title="Add Your First Vehicle"
          onPress={handleAddVehicle}
          style={styles.addFirstButton}
        />
      </View>
    </Card>
  );

  const renderVehiclesList = () => {
    if (vehicles.length === 0) {
      return renderEmptyState();
    }

    return (
      <View style={styles.vehiclesList}>
        {vehicles.map(renderVehicleItem)}
      </View>
    );
  };

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading vehicles...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.title}>My Vehicles</Text>
        <Text style={styles.subtitle}>
          Manage your registered vehicles for faster booking
        </Text>
      </View>

      {vehicles.length > 0 && (
        <View style={styles.addButtonContainer}>
          <Button
            title="Add New Vehicle"
            onPress={handleAddVehicle}
            style={styles.addButton}
          />
        </View>
      )}

      {renderVehiclesList()}

      <Card style={styles.infoCard}>
        <Text style={styles.infoTitle}>Why add vehicles?</Text>
        <View style={styles.benefitsList}>
          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>⚡</Text>
            <Text style={styles.benefitText}>Faster booking process</Text>
          </View>
          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>🎯</Text>
            <Text style={styles.benefitText}>Accurate parking spot recommendations</Text>
          </View>
          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>📱</Text>
            <Text style={styles.benefitText}>Quick access during emergencies</Text>
          </View>
          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>🔒</Text>
            <Text style={styles.benefitText}>Secure and private information</Text>
          </View>
        </View>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
    padding: spacing[4],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
  },
  loadingText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  header: {
    marginBottom: spacing[6],
  },
  title: {
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.gray[900],
    marginBottom: spacing[2],
  },
  subtitle: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  addButtonContainer: {
    marginBottom: spacing[4],
  },
  addButton: {
    alignSelf: 'flex-start',
  },
  vehiclesList: {
    gap: spacing[3],
    marginBottom: spacing[6],
  },
  vehicleCard: {
    padding: spacing[4],
  },
  vehicleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing[3],
  },
  vehicleInfo: {
    flex: 1,
  },
  vehicleName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[1],
  },
  vehicleDetails: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
  },
  defaultBadge: {
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    backgroundColor: colors.primary[100],
    borderRadius: borderRadius.sm,
  },
  defaultText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    color: colors.primary[700],
  },
  vehicleActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[2],
  },
  actionButton: {
    paddingHorizontal: spacing[3],
  },
  deleteButton: {
    padding: spacing[2],
    borderRadius: borderRadius.sm,
    backgroundColor: colors.error[50],
  },
  deleteButtonText: {
    fontSize: 16,
  },
  emptyCard: {
    marginBottom: spacing[6],
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: spacing[4],
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[2],
  },
  emptyText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: spacing[6],
    lineHeight: 24,
  },
  addFirstButton: {
    paddingHorizontal: spacing[6],
  },
  infoCard: {
    marginBottom: spacing[6],
  },
  infoTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semibold,
    color: colors.gray[900],
    marginBottom: spacing[4],
  },
  benefitsList: {
    gap: spacing[3],
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  benefitIcon: {
    fontSize: 20,
    marginRight: spacing[3],
  },
  benefitText: {
    fontSize: typography.fontSize.base,
    color: colors.gray[700],
    flex: 1,
  },
});
