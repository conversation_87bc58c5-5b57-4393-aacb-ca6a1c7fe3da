<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'last4',
        'brand',
        'is_default',
        'stripe_payment_method_id',
        'exp_month',
        'exp_year',
    ];

    protected function casts(): array
    {
        return [
            'is_default' => 'boolean',
            'exp_month' => 'integer',
            'exp_year' => 'integer',
        ];
    }

    /**
     * Relationships
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scopes
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeCards($query)
    {
        return $query->where('type', 'card');
    }

    /**
     * Accessors
     */
    public function getDisplayNameAttribute()
    {
        if ($this->type === 'card') {
            return ucfirst($this->brand) . ' ending in ' . $this->last4;
        }
        
        return ucfirst($this->type) . ' ending in ' . $this->last4;
    }

    public function getIsExpiredAttribute()
    {
        if (!$this->exp_month || !$this->exp_year) {
            return false;
        }

        $currentYear = date('Y');
        $currentMonth = date('n');

        return ($this->exp_year < $currentYear) || 
               ($this->exp_year == $currentYear && $this->exp_month < $currentMonth);
    }

    /**
     * Helper methods
     */
    public function setAsDefault()
    {
        // Remove default from other payment methods for this user
        $this->user->paymentMethods()
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);
        
        // Set this payment method as default
        $this->is_default = true;
        $this->save();
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // When creating a new payment method, if it's the user's first, make it default
        static::created(function ($paymentMethod) {
            if ($paymentMethod->user->paymentMethods()->count() === 1) {
                $paymentMethod->is_default = true;
                $paymentMethod->save();
            }
        });

        // When deleting a default payment method, set another as default if available
        static::deleting(function ($paymentMethod) {
            if ($paymentMethod->is_default) {
                $nextPaymentMethod = $paymentMethod->user->paymentMethods()
                    ->where('id', '!=', $paymentMethod->id)
                    ->first();
                
                if ($nextPaymentMethod) {
                    $nextPaymentMethod->is_default = true;
                    $nextPaymentMethod->save();
                }
            }
        });
    }
}
