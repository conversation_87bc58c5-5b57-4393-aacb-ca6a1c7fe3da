<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\ParkingSpot;
use App\Models\Vehicle;
use Illuminate\Support\Facades\Hash;

class SparkSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test users
        $driver = User::create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'first_name' => 'John',
            'last_name' => 'Driver',
            'phone' => '+1234567890',
            'user_type' => 'driver',
            'is_verified' => true,
        ]);

        $host = User::create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'first_name' => 'Jane',
            'last_name' => 'Host',
            'phone' => '+1234567891',
            'user_type' => 'host',
            'is_verified' => true,
        ]);

        $admin = User::create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'first_name' => 'Admin',
            'last_name' => 'User',
            'phone' => '+**********',
            'user_type' => 'admin',
            'is_verified' => true,
        ]);

        // Create test vehicle for driver
        Vehicle::create([
            'user_id' => $driver->id,
            'make' => 'Toyota',
            'model' => 'Camry',
            'year' => 2022,
            'license_plate' => 'ABC123',
            'color' => 'Blue',
            'is_default' => true,
        ]);

        // Create test parking spots
        $spots = [
            [
                'host_id' => $host->id,
                'title' => 'Downtown Parking Spot',
                'description' => 'Convenient parking spot in the heart of downtown. Close to restaurants and shopping.',
                'address' => '123 Main St, Downtown',
                'latitude' => 40.7128,
                'longitude' => -74.0060,
                'hourly_rate' => 5.00,
                'daily_rate' => 25.00,
                'amenities' => ['covered', 'security_camera', 'well_lit'],
                'is_active' => true,
            ],
            [
                'host_id' => $host->id,
                'title' => 'Airport Parking',
                'description' => 'Secure parking near the airport. Perfect for travelers.',
                'address' => '456 Airport Rd, Near Terminal',
                'latitude' => 40.6892,
                'longitude' => -74.1745,
                'hourly_rate' => 3.00,
                'daily_rate' => 15.00,
                'monthly_rate' => 300.00,
                'amenities' => ['covered', 'security_camera', 'shuttle_service'],
                'is_active' => true,
            ],
            [
                'host_id' => $host->id,
                'title' => 'Shopping Mall Parking',
                'description' => 'Convenient parking at the shopping mall. Easy access to stores.',
                'address' => '789 Mall Ave, Shopping District',
                'latitude' => 40.7589,
                'longitude' => -73.9851,
                'hourly_rate' => 4.00,
                'daily_rate' => 20.00,
                'amenities' => ['covered', 'well_lit', 'close_to_entrance'],
                'is_active' => true,
            ],
        ];

        foreach ($spots as $spotData) {
            ParkingSpot::create($spotData);
        }

        $this->command->info('SPark test data seeded successfully!');
        $this->command->info('Test accounts created:');
        $this->command->info('Driver: <EMAIL> / password');
        $this->command->info('Host: <EMAIL> / password');
        $this->command->info('Admin: <EMAIL> / password');
    }
}
