# SPark API Documentation

## Base URL
- Development: `http://localhost:8000/api`
- Production: `https://api.spark-parking.com/api`

## Authentication
All protected endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Response Format
All API responses follow this structure:
```json
{
  "success": true|false,
  "message": "Response message",
  "data": {}, // Response data (optional)
  "errors": [] // Validation errors (optional)
}
```

## Endpoints

### Authentication

#### POST /auth/register
Register a new user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "first_name": "<PERSON>",
  "last_name": "Doe",
  "phone": "+1234567890",
  "user_type": "driver" // or "host"
}
```

#### POST /auth/login
Login user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### GET /auth/profile
Get authenticated user's profile. (Protected)

#### PUT /auth/profile
Update user profile. (Protected)

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+1234567890",
  "avatar": "https://example.com/avatar.jpg"
}
```

### Parking Spots

#### GET /spots/search
Search parking spots with filters.

**Query Parameters:**
- `latitude` (number): Latitude for location-based search
- `longitude` (number): Longitude for location-based search
- `radius` (number): Search radius in kilometers (default: 5)
- `start_date` (string): Start date for availability check
- `end_date` (string): End date for availability check
- `min_price` (number): Minimum hourly rate
- `max_price` (number): Maximum hourly rate
- `amenities` (array): Required amenities
- `sort_by` (string): Sort by 'price', 'distance', or 'rating'
- `sort_order` (string): 'asc' or 'desc'
- `limit` (number): Results per page (max 50)

#### GET /spots/nearby
Get nearby parking spots.

**Query Parameters:**
- `lat` (required): Latitude
- `lng` (required): Longitude
- `radius` (optional): Search radius in km (default: 5)

#### GET /spots/{id}
Get parking spot details.

#### POST /spots
Create a new parking spot. (Protected - Host only)

**Request Body:**
```json
{
  "title": "Downtown Parking",
  "description": "Convenient parking spot",
  "address": "123 Main St",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "hourly_rate": 5.00,
  "daily_rate": 25.00,
  "monthly_rate": 300.00,
  "amenities": ["covered", "security_camera"],
  "availability_schedule": [
    {
      "day_of_week": 1,
      "start_time": "08:00",
      "end_time": "18:00",
      "is_available": true
    }
  ]
}
```

### Bookings

#### GET /bookings
Get user's bookings. (Protected)

**Query Parameters:**
- `status` (string): Filter by booking status
- `page` (number): Page number
- `limit` (number): Results per page

#### POST /bookings
Create a new booking. (Protected)

**Request Body:**
```json
{
  "spot_id": "1",
  "vehicle_id": "1",
  "start_time": "2024-01-15T10:00:00Z",
  "end_time": "2024-01-15T14:00:00Z",
  "payment_method_id": "pm_1234567890"
}
```

#### GET /bookings/active
Get user's active booking. (Protected)

#### PUT /bookings/{id}/extend
Extend a booking. (Protected)

**Request Body:**
```json
{
  "new_end_time": "2024-01-15T16:00:00Z"
}
```

#### PUT /bookings/{id}/cancel
Cancel a booking. (Protected)

**Request Body:**
```json
{
  "reason": "Plans changed"
}
```

### Vehicles

#### GET /vehicles
Get user's vehicles. (Protected)

#### POST /vehicles
Add a new vehicle. (Protected)

**Request Body:**
```json
{
  "make": "Toyota",
  "model": "Camry",
  "year": 2022,
  "license_plate": "ABC123",
  "color": "Blue",
  "is_default": false
}
```

### Payment

#### GET /payment/methods
Get user's payment methods. (Protected)

#### POST /payment/methods
Add a payment method. (Protected)

**Request Body:**
```json
{
  "payment_method_id": "pm_1234567890",
  "is_default": false
}
```

#### POST /payment/process
Process payment for booking. (Protected)

**Request Body:**
```json
{
  "booking_id": "1",
  "payment_method_id": "1"
}
```

### Reviews

#### GET /reviews
Get reviews with filters.

**Query Parameters:**
- `spot_id` (string): Filter by spot
- `reviewee_id` (string): Filter by user being reviewed
- `type` (string): 'driver_to_host' or 'host_to_driver'
- `min_rating` (number): Minimum rating

#### POST /reviews
Create a review. (Protected)

**Request Body:**
```json
{
  "booking_id": "1",
  "rating": 5,
  "comment": "Great parking spot!",
  "type": "driver_to_host"
}
```

### Notifications

#### GET /notifications
Get user's notifications. (Protected)

**Query Parameters:**
- `type` (string): Filter by notification type
- `is_read` (boolean): Filter by read status

#### PUT /notifications/{id}/read
Mark notification as read. (Protected)

#### PUT /notifications/read-all
Mark all notifications as read. (Protected)

## Error Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Validation Error
- `500` - Internal Server Error

## Rate Limiting

API requests are rate limited to prevent abuse:
- 60 requests per minute for authenticated users
- 30 requests per minute for unauthenticated users

## Pagination

Paginated endpoints return data in this format:
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "current_page": 1,
    "last_page": 5,
    "per_page": 20,
    "total": 100
  }
}
```
