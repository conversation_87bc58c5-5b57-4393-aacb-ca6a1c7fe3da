import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  Platform,
  Modal,
  TextInput,
} from 'react-native';
import Constants from 'expo-constants';

const ExpoAPITest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [apiUrl, setApiUrl] = useState('http://localhost:8000/api');
  const [showUrlModal, setShowUrlModal] = useState(false);
  const [tempUrl, setTempUrl] = useState('http://localhost:8000/api');

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, message]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testAPIConnection = async () => {
    setIsRunning(true);
    clearResults();
    
    addResult('🚀 Expo API Connection Test');
    addResult('================================\n');
    
    // Show environment info
    addResult('📱 Environment Info:');
    addResult(`Platform: ${Platform.OS}`);
    addResult(`Expo SDK: ${Constants.expoVersion}`);
    addResult(`API URL: ${apiUrl}`);
    addResult('');
    
    try {
      // Test 1: Basic connection
      addResult('1. Testing API connection...');
      
      const response = await fetch(`${apiUrl}/test`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        addResult('✅ Connection successful!');
        addResult(`Server: ${data.data?.server || 'SPark Backend'}`);
        addResult(`Status: ${response.status}`);
        addResult('');
        
        // Test 2: Login
        addResult('2. Testing login...');
        
        const loginResponse = await fetch(`${apiUrl}/auth/login`, {
          method: 'POST',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password',
          }),
        });
        
        if (loginResponse.ok) {
          const loginData = await loginResponse.json();
          if (loginData.success) {
            addResult('✅ Login successful!');
            addResult(`User: ${loginData.data.user.first_name} ${loginData.data.user.last_name}`);
            addResult(`Token: ${loginData.data.token.substring(0, 20)}...`);
            
            // Store token for next tests
            const token = loginData.data.token;
            
            // Test 3: Get spots
            addResult('\n3. Testing spot search...');
            
            const spotsResponse = await fetch(`${apiUrl}/spots/search?latitude=40.7128&longitude=-74.0060&radius=10`, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
              },
            });
            
            if (spotsResponse.ok) {
              const spotsData = await spotsResponse.json();
              if (spotsData.success) {
                addResult('✅ Spot search successful!');
                addResult(`Found: ${spotsData.data.length} parking spots`);
              } else {
                addResult(`❌ Spot search failed: ${spotsData.message}`);
              }
            } else {
              addResult(`❌ Spot search request failed: HTTP ${spotsResponse.status}`);
            }
          } else {
            addResult(`❌ Login failed: ${loginData.message}`);
          }
        } else {
          addResult(`❌ Login request failed: HTTP ${loginResponse.status}`);
        }
      } else {
        addResult(`❌ Connection failed: HTTP ${response.status}`);
        addResult(`Status Text: ${response.statusText}`);
      }
    } catch (error: any) {
      addResult(`❌ Error: ${error.message}`);
      
      if (error.message.includes('Network request failed')) {
        addResult('\n🔧 Network Troubleshooting:');
        addResult('1. Make sure Laravel backend is running:');
        addResult('   cd backend && php artisan serve');
        addResult('2. For physical devices, update the API URL with your computer\'s IP');
        addResult('3. Check that your device and computer are on the same network');
      }
    } finally {
      setIsRunning(false);
    }
  };

  const updateApiUrl = () => {
    setTempUrl(apiUrl);
    setShowUrlModal(true);
  };

  const saveApiUrl = () => {
    if (tempUrl.trim()) {
      setApiUrl(tempUrl.trim());
      addResult(`\n🔄 API URL updated to: ${tempUrl.trim()}`);
      setShowUrlModal(false);
    }
  };

  const cancelUrlUpdate = () => {
    setTempUrl(apiUrl);
    setShowUrlModal(false);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Expo API Test</Text>
      <Text style={styles.subtitle}>
        Test connection to Laravel backend
      </Text>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, isRunning && styles.buttonDisabled]}
          onPress={testAPIConnection}
          disabled={isRunning}
        >
          <Text style={styles.buttonText}>
            {isRunning ? 'Testing...' : 'Test API'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.urlButton}
          onPress={updateApiUrl}
        >
          <Text style={styles.urlButtonText}>Change URL</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.resultsContainer}>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </ScrollView>

      {/* URL Update Modal */}
      <Modal
        visible={showUrlModal}
        transparent={true}
        animationType="slide"
        onRequestClose={cancelUrlUpdate}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Update API URL</Text>
            <Text style={styles.modalSubtitle}>
              Enter your computer's IP address for physical device testing
            </Text>

            <TextInput
              style={styles.urlInput}
              value={tempUrl}
              onChangeText={setTempUrl}
              placeholder="http://*************:8000/api"
              autoCapitalize="none"
              autoCorrect={false}
              keyboardType="url"
            />

            <Text style={styles.modalHint}>
              💡 Find your IP: Windows (ipconfig) | Mac (ifconfig)
            </Text>

            <View style={styles.presetButtons}>
              <TouchableOpacity
                style={styles.presetButton}
                onPress={() => setTempUrl('http://localhost:8000/api')}
              >
                <Text style={styles.presetButtonText}>Localhost</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.presetButton}
                onPress={() => setTempUrl('http://*************:8000/api')}
              >
                <Text style={styles.presetButtonText}>*************</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.presetButton}
                onPress={() => setTempUrl('http://**********:8000/api')}
              >
                <Text style={styles.presetButtonText}>**********</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={cancelUrlUpdate}
              >
                <Text style={styles.modalCancelText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.modalSaveButton}
                onPress={saveApiUrl}
              >
                <Text style={styles.modalSaveText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 10,
  },
  button: {
    flex: 2,
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  urlButton: {
    flex: 1,
    backgroundColor: '#ffc107',
    padding: 15,
    borderRadius: 8,
  },
  urlButtonText: {
    color: '#333',
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  resultText: {
    fontSize: 14,
    fontFamily: 'monospace',
    color: '#333',
    lineHeight: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#333',
  },
  modalSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  urlInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 10,
    backgroundColor: '#f9f9f9',
  },
  modalHint: {
    fontSize: 12,
    color: '#888',
    textAlign: 'center',
    marginBottom: 15,
  },
  presetButtons: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 20,
  },
  presetButton: {
    flex: 1,
    backgroundColor: '#e9ecef',
    padding: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  presetButtonText: {
    fontSize: 12,
    color: '#495057',
    textAlign: 'center',
    fontWeight: '500',
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  modalCancelButton: {
    flex: 1,
    backgroundColor: '#6c757d',
    padding: 12,
    borderRadius: 8,
  },
  modalCancelText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  modalSaveButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
  },
  modalSaveText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ExpoAPITest;
