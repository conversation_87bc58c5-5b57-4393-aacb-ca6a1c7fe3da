import * as Location from 'expo-location';

export interface ParkingSpot {
  id: string;
  latitude: number;
  longitude: number;
  title: string;
  address: string;
  type: 'street' | 'garage' | 'lot' | 'private';
  price: number;
  hourlyRate: number;
  available: boolean;
  distance: number;
  rating: number;
  amenities: string[];
  maxDuration: number; // in hours
  description: string;
  imageUrl?: string;
}

export interface SearchFilters {
  maxDistance: number; // in km
  maxPrice: number;
  parkingTypes: string[];
  amenities: string[];
  maxDuration: number;
}

export interface SearchResult {
  spots: ParkingSpot[];
  totalFound: number;
  searchRadius: number;
  userLocation: {
    latitude: number;
    longitude: number;
  };
}

class ParkingSearchService {
  private readonly DEFAULT_SEARCH_RADIUS = 2; // 2km
  private readonly MAX_SEARCH_RADIUS = 10; // 10km

  /**
   * Find nearby parking spots based on user location
   */
  async findNearbyParking(
    userLocation: Location.LocationObject,
    filters?: Partial<SearchFilters>
  ): Promise<SearchResult> {
    const defaultFilters: SearchFilters = {
      maxDistance: this.DEFAULT_SEARCH_RADIUS,
      maxPrice: 50,
      parkingTypes: ['street', 'garage', 'lot', 'private'],
      amenities: [],
      maxDuration: 24,
    };

    const searchFilters = { ...defaultFilters, ...filters };

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Generate mock parking spots around user location
    const mockSpots = this.generateMockParkingSpots(userLocation, searchFilters);

    return {
      spots: mockSpots,
      totalFound: mockSpots.length,
      searchRadius: searchFilters.maxDistance,
      userLocation: {
        latitude: userLocation.coords.latitude,
        longitude: userLocation.coords.longitude,
      },
    };
  }

  /**
   * Search parking spots by destination address
   */
  async searchByDestination(
    destination: string,
    filters?: Partial<SearchFilters>
  ): Promise<SearchResult> {
    // In a real app, you would geocode the destination first
    // For now, we'll use a mock location
    const mockDestination = {
      latitude: 37.7749,
      longitude: -122.4194,
    };

    const mockUserLocation: Location.LocationObject = {
      coords: {
        latitude: mockDestination.latitude,
        longitude: mockDestination.longitude,
        altitude: null,
        accuracy: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null,
      },
      timestamp: Date.now(),
    };

    return this.findNearbyParking(mockUserLocation, filters);
  }

  /**
   * Get parking recommendations based on user preferences and history
   */
  async getRecommendations(
    userLocation: Location.LocationObject,
    userPreferences?: {
      preferredTypes: string[];
      maxWalkingDistance: number;
      priceRange: [number, number];
    }
  ): Promise<ParkingSpot[]> {
    const filters: Partial<SearchFilters> = {
      maxDistance: userPreferences?.maxWalkingDistance || 0.5,
      maxPrice: userPreferences?.priceRange?.[1] || 20,
      parkingTypes: userPreferences?.preferredTypes || ['garage', 'lot'],
    };

    const result = await this.findNearbyParking(userLocation, filters);
    
    // Sort by rating and price
    return result.spots
      .sort((a, b) => {
        const scoreA = a.rating * 0.7 + (1 / a.price) * 0.3;
        const scoreB = b.rating * 0.7 + (1 / b.price) * 0.3;
        return scoreB - scoreA;
      })
      .slice(0, 5); // Top 5 recommendations
  }

  /**
   * Calculate distance between two coordinates
   */
  private calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // Earth's radius in km
    const dLat = this.toRad(lat2 - lat1);
    const dLon = this.toRad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRad(lat1)) *
        Math.cos(this.toRad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRad(value: number): number {
    return (value * Math.PI) / 180;
  }

  /**
   * Generate mock parking spots for demonstration
   */
  private generateMockParkingSpots(
    userLocation: Location.LocationObject,
    filters: SearchFilters
  ): ParkingSpot[] {
    const spots: ParkingSpot[] = [];
    const { latitude, longitude } = userLocation.coords;

    const mockData = [
      {
        title: 'Downtown Parking Garage',
        type: 'garage' as const,
        price: 8.50,
        rating: 4.5,
        amenities: ['covered', 'security', 'ev_charging'],
        description: 'Secure underground parking with 24/7 security',
      },
      {
        title: 'Street Parking - Main St',
        type: 'street' as const,
        price: 3.00,
        rating: 3.8,
        amenities: ['metered'],
        description: 'Convenient street parking with 2-hour limit',
      },
      {
        title: 'Shopping Mall Lot',
        type: 'lot' as const,
        price: 5.00,
        rating: 4.2,
        amenities: ['free_first_hour', 'covered'],
        description: 'Large parking lot with first hour free',
      },
      {
        title: 'Private Driveway',
        type: 'private' as const,
        price: 6.00,
        rating: 4.8,
        amenities: ['private', 'secure'],
        description: 'Private residential driveway, very secure',
      },
      {
        title: 'Business District Garage',
        type: 'garage' as const,
        price: 12.00,
        rating: 4.3,
        amenities: ['covered', 'valet', 'ev_charging'],
        description: 'Premium parking with valet service',
      },
    ];

    mockData.forEach((data, index) => {
      // Generate random coordinates within search radius
      const offsetLat = (Math.random() - 0.5) * (filters.maxDistance / 111); // Rough conversion
      const offsetLon = (Math.random() - 0.5) * (filters.maxDistance / 111);
      
      const spotLat = latitude + offsetLat;
      const spotLon = longitude + offsetLon;
      const distance = this.calculateDistance(latitude, longitude, spotLat, spotLon);

      if (
        distance <= filters.maxDistance &&
        data.price <= filters.maxPrice &&
        filters.parkingTypes.includes(data.type)
      ) {
        spots.push({
          id: `spot_${index + 1}`,
          latitude: spotLat,
          longitude: spotLon,
          title: data.title,
          address: `${123 + index} Example St, City`,
          type: data.type,
          price: data.price,
          hourlyRate: data.price,
          available: Math.random() > 0.2, // 80% availability
          distance: Math.round(distance * 100) / 100,
          rating: data.rating,
          amenities: data.amenities,
          maxDuration: 8,
          description: data.description,
        });
      }
    });

    return spots.sort((a, b) => a.distance - b.distance);
  }
}

export const parkingSearchService = new ParkingSearchService();
