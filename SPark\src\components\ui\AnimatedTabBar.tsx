import React from 'react';
import { View, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { colors, spacing } from '../../theme';

const { width: screenWidth } = Dimensions.get('window');

interface AnimatedTabBarProps {
  state: any;
  descriptors: any;
  navigation: any;
}

const AnimatedTabBar: React.FC<AnimatedTabBarProps> = ({
  state,
  descriptors,
  navigation,
}) => {
  const insets = useSafeAreaInsets();
  const tabWidth = screenWidth / state.routes.length;
  const translateX = useSharedValue(0);

  React.useEffect(() => {
    translateX.value = withSpring(state.index * tabWidth, {
      damping: 15,
      stiffness: 150,
    });
  }, [state.index, tabWidth]);

  const indicatorStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      {/* Animated indicator */}
      <Animated.View
        style={[
          styles.indicator,
          { width: tabWidth },
          indicatorStyle,
        ]}
      />
      
      {/* Tab buttons */}
      <View style={styles.tabsContainer}>
        {state.routes.map((route: any, index: number) => {
          const { options } = descriptors[route.key];
          const label = options.tabBarLabel !== undefined
            ? options.tabBarLabel
            : options.title !== undefined
            ? options.title
            : route.name;

          const isFocused = state.index === index;

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          const onLongPress = () => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            });
          };

          const animatedIconStyle = useAnimatedStyle(() => {
            const scale = interpolate(
              translateX.value,
              [(index - 1) * tabWidth, index * tabWidth, (index + 1) * tabWidth],
              [0.8, 1.2, 0.8],
              'clamp'
            );

            return {
              transform: [{ scale }],
            };
          });

          const animatedTextStyle = useAnimatedStyle(() => {
            const opacity = interpolate(
              translateX.value,
              [(index - 1) * tabWidth, index * tabWidth, (index + 1) * tabWidth],
              [0.6, 1, 0.6],
              'clamp'
            );

            return { opacity };
          });

          return (
            <TouchableOpacity
              key={route.key}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarTestID}
              onPress={onPress}
              onLongPress={onLongPress}
              style={styles.tab}
            >
              <Animated.View style={[styles.iconContainer, animatedIconStyle]}>
                {options.tabBarIcon({
                  focused: isFocused,
                  color: isFocused ? colors.primary[500] : colors.gray[400],
                  size: 24,
                })}
              </Animated.View>
              
              <Animated.Text
                style={[
                  styles.label,
                  {
                    color: isFocused ? colors.primary[500] : colors.gray[400],
                  },
                  animatedTextStyle,
                ]}
              >
                {label}
              </Animated.Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    paddingTop: 8,
    paddingHorizontal: 16,
  },
  indicator: {
    position: 'absolute',
    top: 0,
    height: 3,
    backgroundColor: colors.primary[500],
    borderRadius: 2,
  },
  tabsContainer: {
    flexDirection: 'row',
    height: 60,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  iconContainer: {
    marginBottom: 4,
  },
  label: {
    fontSize: 11,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default AnimatedTabBar;
