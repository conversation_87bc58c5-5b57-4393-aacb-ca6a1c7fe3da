import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import { colors, typography, spacing, borderRadius } from '../../theme';

interface MobileInputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  error?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  variant?: 'default' | 'search' | 'minimal';
}

const MobileInput: React.FC<MobileInputProps> = ({
  label,
  error,
  leftIcon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  inputStyle,
  variant = 'default',
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      marginBottom: spacing[4],
    };

    switch (variant) {
      case 'search':
        return {
          ...baseStyle,
          backgroundColor: colors.gray[100],
          borderRadius: 8, // Reduced from 12 to 8
          paddingHorizontal: 16,
          paddingVertical: 16,
          flexDirection: 'row',
          alignItems: 'center',
          gap: 12,
        };
      case 'minimal':
        return {
          ...baseStyle,
          borderBottomWidth: 2,
          borderBottomColor: isFocused ? colors.primary[500] : colors.gray[300],
          paddingBottom: 8,
        };
      default:
        return {
          ...baseStyle,
          borderWidth: 2,
          borderColor: error ? colors.error[500] : isFocused ? colors.primary[500] : colors.gray[300],
          borderRadius: 8, // Reduced from 12 to 8
          paddingHorizontal: 16,
          paddingVertical: 16,
          backgroundColor: colors.white,
        };
    }
  };

  const getInputStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      fontSize: 16,
      fontWeight: '500',
      color: colors.black[900],
      flex: 1,
    };

    if (variant === 'search') {
      return {
        ...baseStyle,
        fontSize: 16,
        fontWeight: '500',
      };
    }

    return baseStyle;
  };

  const renderInput = () => {
    if (variant === 'search') {
      return (
        <View style={[getContainerStyle(), containerStyle]}>
          {leftIcon}
          <TextInput
            style={[getInputStyle(), inputStyle]}
            placeholderTextColor={colors.gray[500]}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            {...textInputProps}
          />
          {rightIcon && (
            <TouchableOpacity onPress={onRightIconPress}>
              {rightIcon}
            </TouchableOpacity>
          )}
        </View>
      );
    }

    return (
      <View style={[containerStyle]}>
        {label && <Text style={styles.label}>{label}</Text>}
        
        <View style={getContainerStyle()}>
          {leftIcon && <View style={styles.leftIcon}>{leftIcon}</View>}
          
          <TextInput
            style={[getInputStyle(), inputStyle]}
            placeholderTextColor={colors.gray[500]}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            {...textInputProps}
          />
          
          {rightIcon && (
            <TouchableOpacity style={styles.rightIcon} onPress={onRightIconPress}>
              {rightIcon}
            </TouchableOpacity>
          )}
        </View>
        
        {error && <Text style={styles.error}>{error}</Text>}
      </View>
    );
  };

  return renderInput();
};

const styles = StyleSheet.create({
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.black[800],
    marginBottom: 8,
  },
  leftIcon: {
    marginRight: 12,
  },
  rightIcon: {
    marginLeft: 12,
  },
  error: {
    fontSize: 14,
    color: colors.error[500],
    marginTop: 4,
    fontWeight: '500',
  },
});

export default MobileInput;
